import { Stack } from '@fluentui/react';
import useClass from 'hooks/useClass';
import useNavigator from 'hooks/useNavigator';
import useRequest from 'hooks/useRequest';
import React, { useContext } from 'react';
import cn from 'classnames';
import { t } from 'utils/string';
import { API_PATIENT } from 'constants/urlRequest';
import { inchToFeetAndInch } from 'utils';
import round from 'lodash/round';
import { useDispatch, useSelector } from 'react-redux';
import { setHeaderInfoPopup } from 'store/actions/application';
import DiagnosisPopup from './DiagnoisPopup';
import WorklistPopup from './WorklistPopup';
import { PatientPermissionContext } from 'contexts/PatientPermissionContext';
import { PATIENT_INFORMATION_BAR_ITEMS } from 'constants/settings';

const InfoItem = ({ label = '', value = '', leftSide = true, onClick, id }) => {
  const css = useClass(infoItemStyle);

  return (
    <Stack
      id={id}
      data-isbutton="button"
      onClick={onClick}
      horizontal
      tokens={{ childrenGap: 4, padding: '0 8px' }}
      className={cn('text-12-16', css.container, leftSide ? css.leftSideItem : css.rightSideItem)}
    >
      <div>{label}</div>
      <div>{value}</div>
    </Stack>
  );
};

const infoItemStyle = {
  container: {
    color: '#FFFFFF',
    cursor: 'pointer',
  },
  leftSideItem: {
    borderRight: '1px solid rgba(255,255,255,0.6)',
  },
  rightSideItem: {
    borderLeft: '1px solid rgba(255,255,255,0.6)',
  },
};

const MoreInforBar = () => {
  const css = useClass(moreInforBarStyles);
  const { searchParams } = useNavigator();
  const dispatch = useDispatch();
  const { isAllowPatientPermission } = useContext(PatientPermissionContext);
  const patientMiniBar = useSelector((state) => state.settings.patientMiniBar);
  const barItems = patientMiniBar?.split(',') || [];

  const {
    data: {
      weight = 0,
      height = 0,
      bmi = 0,
      bloodPressureTotal = '-/-',
      totalAllergies = '',
      totalDiagnosis = '',
      totalMedicine = '',
      totalProblemList = '',
      totalVitals = '',
    } = {},
  } = useRequest({
    url: API_PATIENT.HEADER_INFO(searchParams.patientId),
    enabled: !!searchParams.patientId,
  });

  const onOpenInfoPopup = (id) => dispatch(setHeaderInfoPopup(id));

  return !isAllowPatientPermission || !barItems?.length ? (
    <div className={css.container} />
  ) : (
    <Stack
      horizontal
      horizontalAlign="space-between"
      className={css.container}
      tokens={{ padding: '3px 0' }}
    >
      <Stack horizontal>
        {barItems.includes(PATIENT_INFORMATION_BAR_ITEMS.BODY_METRICS) && (
          <React.Fragment>
            <InfoItem label={t('Weight:')} value={`${weight || 0} Lbs`} />
            <InfoItem
              label={t('Height:')}
              value={`${inchToFeetAndInch(height).feet}ft, ${inchToFeetAndInch(height).inches}in`}
            />
            <InfoItem label={t('BMI:')} value={round(bmi, 1)} />
            <InfoItem label={t('Blood Pressure:')} value={bloodPressureTotal} />
          </React.Fragment>
        )}
      </Stack>
      <Stack horizontal>
        {barItems.includes(PATIENT_INFORMATION_BAR_ITEMS.ALLERGIES) && (
          <InfoItem
            leftSide={false}
            label={`Allergies (${totalAllergies})`}
            id={ITEM_IDS.ALLERGIES}
            data-isbutton="button"
            onClick={() => onOpenInfoPopup(ITEM_IDS.ALLERGIES)}
          />
        )}
        {barItems.includes(PATIENT_INFORMATION_BAR_ITEMS.VITALS) && (
          <InfoItem
            leftSide={false}
            label={`Vitals (${totalVitals})`}
            id={ITEM_IDS.VITALS}
            data-isbutton="button"
            onClick={() => onOpenInfoPopup(ITEM_IDS.VITALS)}
          />
        )}
        {barItems.includes(PATIENT_INFORMATION_BAR_ITEMS.PROBLEMS_LIST) && (
          <InfoItem
            leftSide={false}
            label={`Problems List (${totalProblemList})`}
            id={ITEM_IDS.PROBLEM_LIST}
            data-isbutton="button"
            onClick={() => onOpenInfoPopup(ITEM_IDS.PROBLEM_LIST)}
          />
        )}
        {barItems.includes(PATIENT_INFORMATION_BAR_ITEMS.DIAGNOSIS) && (
          <React.Fragment>
            <InfoItem
              leftSide={false}
              label={`Diagnosis (${totalDiagnosis})`}
              id={ITEM_IDS.DIAGNOSIS}
              data-isbutton="button"
              onClick={() => onOpenInfoPopup(ITEM_IDS.DIAGNOSIS)}
            />
            <DiagnosisPopup targetId={ITEM_IDS.DIAGNOSIS} />
          </React.Fragment>
        )}
        {barItems.includes(PATIENT_INFORMATION_BAR_ITEMS.MEDICATIONS) && (
          <InfoItem
            leftSide={false}
            label={`Current Meds (${totalMedicine})`}
            id={ITEM_IDS.MEDICATIONS}
            data-isbutton="button"
            onClick={() => onOpenInfoPopup(ITEM_IDS.MEDICATIONS)}
          />
        )}
        {barItems.includes(PATIENT_INFORMATION_BAR_ITEMS.WORKLIST) && (
          <React.Fragment>
            <InfoItem
              leftSide={false}
              label={`Work list`}
              id={ITEM_IDS.WORKLIST}
              data-isbutton="button"
              onClick={() => onOpenInfoPopup(ITEM_IDS.WORKLIST)}
            />
            <WorklistPopup targetId={ITEM_IDS.WORKLIST} />
          </React.Fragment>
        )}
      </Stack>
    </Stack>
  );
};

const moreInforBarStyles = (theme) => ({
  container: {
    backgroundColor: theme.palette.themeDark,
    height: 20,
    boxSizing: 'content-box',
  },
});

export default MoreInforBar;

const ITEM_IDS = {
  ALLERGIES: 'Allergies-header-info',
  VITALS: 'Vitals-header-info',
  PROBLEM_LIST: 'ProblemList-header-info',
  DIAGNOSIS: 'Diagnosis-header-info',
  MEDICATIONS: 'Medications-header-info',
  WORKLIST: 'Worklist-header-info',
};
