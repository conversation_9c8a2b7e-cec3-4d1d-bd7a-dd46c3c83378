import { De<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Button, Stack } from '@fluentui/react';
import cn from 'classnames';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import SelectorPrimary from 'components/Selector';
import { API_FORM_TEMPLATE, API_METHOD } from 'constants/urlRequest';
import { useClass, useModal, useNavigator, useRequest } from 'hooks';
import React, { useEffect, useState } from 'react';
import { formatToFluentOptions } from 'utils';
import { t } from 'utils/string';
import { useFormContext } from 'react-hook-form';
import AddTemplateModal from 'modules/InpatientNote/components/AddTemplateModal';
import { toast } from 'react-toastify';
import { MODAL_SIZE } from 'constants/modal';
import { TEXT_TEMPLATE } from 'constants/texts';
import { fetchByQueryKey } from 'apis/queryClient';

function FormTemplate({ className, formId, templateChoosen, setTemplateChoosen }) {
  const [parentWidth, setParentWidth] = useState(0);

  const { getValues, reset } = useFormContext();
  const { hash } = useNavigator();
  const { showModal } = useModal();
  const css = useClass();
  const apiGetTemplate = useRequest({
    key: `${API_FORM_TEMPLATE}-${formId}`,
    url: API_FORM_TEMPLATE,
    params: { formName: formId },
  });

  const apiSaveTemplate = useRequest({
    url: API_FORM_TEMPLATE,
    method: API_METHOD.POST,
  });

  useEffect(() => {
    if (document.querySelector(`#${formId}-template`)) {
      setParentWidth(document.querySelector(`#${formId}-template`)?.parentElement.clientWidth);
    }
  }, [hash]);

  const onSaveTemplate = (templateName) => {
    apiSaveTemplate.mutateAsync(
      { templateData: JSON.stringify(getValues()), formName: formId, templateName },
      {
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t(`Template`)));
          fetchByQueryKey(`${API_FORM_TEMPLATE}-${formId}`);
        },
      },
    );
  };

  const onUpdateTemplate = (templateName) => {
    const newData = {
      ...templateChoosen,
      templateData: JSON.stringify(getValues()),
      templateName,
    };
    apiSaveTemplate.mutateAsync(newData, {
      onSuccess: () => {
        toast.success(TEXT_TEMPLATE.UPDATE_SUCCESSFULLY(t(`Template`)));
        fetchByQueryKey(`${API_FORM_TEMPLATE}-${formId}`);
        setTemplateChoosen(newData);
      },
    });
  };

  return (
    <Stack
      className={cn(className, css.cmClass.backgroundGrey300, 'mb-16', {
        [css.cmClass.grid3Columns]: parentWidth >= 1000,
        'd-flex': !!parentWidth && parentWidth < 1000,
      })}
      id={`${formId}-template`}
      tokens={{ padding: '12px 16px' }}
    >
      <LoadingWrapper loading={apiSaveTemplate.loading || apiGetTemplate.loading}>
        <Stack horizontal tokens={{ childrenGap: 16 }}>
          <Stack.Item shrink={0}>
            <SelectorPrimary
              textFieldProps={{
                prefix: t('Template:'),
              }}
              options={formatToFluentOptions(apiGetTemplate?.data, 'templateName', 'templateId')}
              value={templateChoosen?.templateId}
              onChange={(formData) => {
                if (formData) {
                  setTemplateChoosen(formData);
                  reset(JSON.parse(formData.templateData), false);
                } else {
                  setTemplateChoosen();
                }
              }}
            />
          </Stack.Item>
          <Stack.Item shrink={0}>
            <DefaultButton
              text={t('Update Template')}
              className="self-end"
              disabled={!templateChoosen?.templateId}
              data-isbutton="button"
              onClick={() =>
                showModal({
                  content: (
                    <AddTemplateModal
                      loading={apiSaveTemplate.loading}
                      onSubmit={onUpdateTemplate}
                      templateName={templateChoosen?.templateName}
                    />
                  ),
                  isReplace: false,
                  size: MODAL_SIZE.X_SMALL,
                })
              }
            />
          </Stack.Item>
          <Stack.Item shrink={0}>
            <PrimaryButton
              className="self-end"
              text={t('Save Template')}
              data-isbutton="button"
              onClick={() =>
                showModal({
                  content: (
                    <AddTemplateModal loading={apiSaveTemplate.loading} onSubmit={onSaveTemplate} />
                  ),
                  isReplace: false,
                  size: MODAL_SIZE.X_SMALL,
                })
              }
            />
          </Stack.Item>
        </Stack>
      </LoadingWrapper>
    </Stack>
  );
}

export default FormTemplate;
