import { SETTING_KEYS } from 'constants/settingKeys';
import { useSetting } from 'hooks';
import React from 'react';
import { FormProvider } from 'react-hook-form';
import FormTemplate from './FormTemplate';

export default function HookFormProvider({
  children,
  autoShowFormTemplate,
  formId,
  templateChoosen,
  setTemplateChoosen,
  ...methods
}) {
  const [showFormTemplate, formSetting] = useSetting(
    SETTING_KEYS.SHOW_FORM_TEMPLATE,
    SETTING_KEYS.FORM_SETTINGS,
  );

  const showTemplate =
    autoShowFormTemplate ||
    (showFormTemplate && (formSetting?.find((i) => i.id === formId) || {}).showTemplate);

  return (
    <FormProvider {...methods}>
      {showTemplate && (
        <FormTemplate
          formId={formId}
          templateChoosen={templateChoosen}
          setTemplateChoosen={setTemplateChoosen}
        />
      )}
      {children}
    </FormProvider>
  );
}
