// useHookForm.js
import { useForm } from 'react-hook-form';
import cloneDeep from 'lodash/cloneDeep';
import _set from 'lodash/set';
import useDidUpdateEffect from 'hooks/useDidUpdateEffect';
import { useState } from 'react';
/**
 * @template T
 * @param {import('react-hook-form').UseFormProps<T> & {dependencies: {path: string, value: any}}} [options]
 * @returns {import('react-hook-form').UseFormReturn<T>}
 */

export default function useHookForm(options = {}) {
  const methods = useForm({ reValidateMode: 'onSubmit', ...options });
  const [templateChoosen, setTemplateChoosen] = useState(null);
  // reset form values when dependencies change
  // new values will be combined between defaultValues and dependencies

  useDidUpdateEffect(() => {
    if (options?.dependencies) {
      (async () => {
        const defaultValues =
          typeof options?.defaultValues === 'function'
            ? await options?.defaultValues()
            : options?.defaultValues;
        let values = cloneDeep(defaultValues || methods.getValues());
        Object.entries(options?.dependencies).forEach(([path, value]) => {
          _set(values, path, value);
        });

        methods.reset(values);
      })();
    }
  }, [JSON.stringify(options?.dependencies)]);

  const customReset = (values, isResetTemplate = true) => {
    methods.reset(values);
    if (isResetTemplate) {
      setTemplateChoosen(null);
    }
  };

  return { ...methods, reset: customReset, templateChoosen, setTemplateChoosen };
}
