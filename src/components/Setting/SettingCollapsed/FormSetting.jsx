import React, { useCallback, useState } from 'react';
import {
  Checkbox,
  DefaultButton,
  IconButton,
  PrimaryButton,
  SearchBox,
  Stack,
} from '@fluentui/react';
import MyField from 'components/Form/Field';
import FieldCheckBox from 'components/Form/FieldCheckbox';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import Table from 'components/Table';
import { FORM_LIST } from 'constants/formId';
import { Formik } from 'formik';
import { useModal } from 'hooks';
import uniqBy from 'lodash/uniqBy';
import { useDispatch, useSelector } from 'react-redux';
import { setGlobalSettings } from 'store/actions/settings';
import { t } from 'utils/string';
import { debounceFunc } from 'utils/time';

function FormSetting() {
  const { hideModal } = useModal();
  const { isSetLoading, settings, navigationItems } = useSelector(({ settings }) => settings);
  const [search, setSearch] = useState('');
  const dispatch = useDispatch();

  const onSearch = useCallback(
    debounceFunc((searchValue) => {
      setSearch(searchValue);
    }, 300),
    [],
  );

  const onSubmit = async (form) => {
    dispatch(
      setGlobalSettings(
        { ...settings, formSetting: form.values.pages },
        {
          onSuccess: hideModal,
        },
      ),
    );
  };

  const pages = uniqBy([...(settings.formSetting || []), ...FORM_LIST], 'id').filter(
    (i) => navigationItems.find((j) => j.path === i.rootPage) || !i.rootPage,
  );

  return (
    <Formik initialValues={{ pages }} enableReinitialize>
      <ModalLayout
        loading={isSetLoading}
        title={t('Form Settings')}
        footerRightSide={
          <Stack
            horizontal
            tokens={{ childrenGap: 16 }}
            horizontalAlign="end"
            verticalAlign="center"
          >
            <MyField isFast={false}>
              {({ form }) => (
                <PrimaryButton
                  text={t('Save')}
                  data-isbutton="button"
                  onClick={() => onSubmit(form)}
                />
              )}
            </MyField>
            <DefaultButton text={t('Cancel')} data-isbutton="button" onClick={hideModal} />
          </Stack>
        }
      >
        <Stack tokens={{ childrenGap: 16, padding: 16 }}>
          <Stack horizontal tokens={{ childrenGap: 8 }}>
            <SearchBox
              iconProps={{ iconName: 'Zoom' }}
              className="flex-1"
              placeholder={t('Search...')}
              onChange={(_, value) => onSearch(value)}
            />
            <MyField>
              {({ form }) => (
                <IconButton
                  iconProps={{ iconName: 'Refresh' }}
                  data-isbutton="button"
                  onClick={() => form.setFieldValue('pages', FORM_LIST)}
                />
              )}
            </MyField>
          </Stack>

          <MyField name="pages" isFast={false}>
            {({ field }) => {
              const data =
                field.value?.filter((i) => i.name?.toLowerCase().includes(search.toLowerCase())) ||
                [];

              return <Table columns={getColumn(data)} items={data} pagination={false} />;
            }}
          </MyField>
        </Stack>
      </ModalLayout>
    </Formik>
  );
}

export default FormSetting;

const getColumn = (pages) => [
  {
    name: 'Form',
    fieldName: 'name',
  },
  {
    name: 'Add',
    fieldName: '',
    maxWidth: 200,
    minWidth: 200,
    onRenderHeader: () => (
      <Stack tokens={{ padding: '0 0 0 16px' }} className="mt-8">
        <FieldCheckBox
          name="allRequired"
          label={t('Show Required Field')}
          isFast={false}
          onChange={({ setFieldValue, name, checked }) => {
            const temp = pages || [];
            for (const i of temp) {
              i.showRequiredField = checked;
            }
            setFieldValue(name, checked);
            setFieldValue('pages', temp);
          }}
        />
      </Stack>
    ),
    renderItem: (_, index) => (
      <Stack tokens={{ padding: '0 0 0 16px' }}>
        <MyField name="pages" isFast={false}>
          {({ field, form: { setFieldValue } }) => (
            <Checkbox
              key={pages[index]?.id}
              checked={!!pages[index]?.showRequiredField}
              defaultChecked={!!pages[index]?.showRequiredField}
              disabled={pages[index]?.disabled}
              onChange={(_, value) => {
                const temp = field.value || [];
                const row = temp.find((i) => i.id === pages[index].id);
                row.showRequiredField = value;
                setFieldValue('pages', temp);
              }}
            />
          )}
        </MyField>
      </Stack>
    ),
  },
  {
    name: 'Add',
    fieldName: '',
    maxWidth: 200,
    minWidth: 200,
    onRenderHeader: () => (
      <Stack tokens={{ padding: '0 0 0 16px' }} className="mt-8">
        <FieldCheckBox
          name="allTemplate"
          label={t('Data Entry Template')}
          isFast={false}
          onChange={({ setFieldValue, name, checked }) => {
            const temp = pages || [];
            for (const i of temp) {
              i.showTemplate = checked;
            }
            setFieldValue(name, checked);
            setFieldValue('pages', temp);
          }}
        />
      </Stack>
    ),
    renderItem: (_, index) => (
      <Stack tokens={{ padding: '0 0 0 16px' }}>
        <MyField name="pages" isFast={false}>
          {({ field, form: { setFieldValue } }) => {
            return (
              <Checkbox
                key={pages[index]?.id}
                checked={!!pages[index]?.showTemplate}
                defaultChecked={!!pages[index]?.showTemplate}
                onChange={(_, value) => {
                  const temp = field.value || [];
                  const row = temp.find((i) => i.id === pages[index].id);
                  row.showTemplate = value;
                  setFieldValue('pages', temp);
                }}
              />
            );
          }}
        </MyField>
      </Stack>
    ),
    testColumn: JSON.stringify(pages),
  },
];
