import { t } from 'utils/string';
import { ContextualMenuItemType } from '@fluentui/react';
import {
  BHTInpatient,
  CINA,
  CIWA,
  Comments,
  ContrabandCheckInpatient,
  DischargeInpatient,
  DischargeResidential,
  OrderList,
  Overview,
  PatientValuables,
  RoomCheckInpatient,
  RoomCheckResidential,
  Vitals,
  InformedConsent,
  RestraintAndSeclusion,
  NurseProgressNote,
  DailyNursingFlowSheet,
  StaffDailyNote,
  MedicationReconciliation,
  InpatientInternalOrder,
  AlertAction,
  EngagementSessionNote,
  ComprehensiveNursingAssessment,
  DischargePlan,
  InpatientEmar,
  InpatientASAM,
  Dependents,
  InpatientNote,
  ServicePlan,
  PsychotherapyProgressNote,
  ImportantDates,
  PsychosocialAssessmentNote,
  ContrabandCheckResidential,
  ViewDischargeInpatient,
  ViewDischargeResidential,
  ScanedDocuments,
  AdmitInforResidential,
  NurseDailyNote,
  DailyStaffNote,
  NursingAdmissionAssessment,
  ART,
  HealthPhysicalNote,
  DynamicForm,
  AdmissionOrders,
  AncillaryOrders,
  PsychNote as InpatientPsychNote,
  PsychEval as InpatientPsychEval,
} from 'modules/Inpatient-Residential/components/Action';
import {
  Education,
  EmploymentHireInformation,
  Comments as HRComments,
  Identifier,
  Positions,
  Psychiatrist,
  Roles,
  Signature,
  Specialties,
  Programs,
} from 'modules/HREnrollment/components/Action';
import RoleTransfer from 'modules/HREnrollment/components/Action/RoleTransfer';
import BHTResidential from 'modules/Inpatient-Residential/components/Action/BHTRoundingSheet/BHTResidential';
import { ROUTE_NAME } from 'constants/routes';
import Laboratory from 'modules/Inpatient-Residential/components/Action/LabOrder';
import AdmitInfoTriage from 'modules/Inpatient-Residential/components/Action/AdmitInfo/AdmitInfoTriage';

export const DRAG_OPTION = {
  handle: '.sortable-view',
  direction: 'horizontal',
  animation: 200,
  swapThreshold: 1,
  ghostClass: 'ghost',
  easing: 'ease-out',
  dragClass: 'sortableDrag',
};

export const ACTION_KEY = {
  // Inpatient-Residential action keys
  ADMISSION_ORDERS: 'inpatient-admission-orders',
  ART: 'ChildAndFamily',
  ALERTS: 'alerts',
  ASAM: 'asam',
  ANCILLARY_ORDERS: 'inpatient-ancillary-orders',
  BHT_ROUNDING_SHEET_INPATIENT: 'inpatient-bht-rounding-sheet-inpatient',
  BHT_ROUNDING_SHEET_RESIDENTIAL: 'residential-bht-rounding-sheet-residential',
  CINA: 'cina',
  CIWA: 'ciwa',
  COMMENTS: 'inpatient-comments',
  COMPREHENSIVE_NURSING_ASSESSMENT: 'inpatient-comprehensive-nursing-assessment',
  CONTRABAND_CHECK_INPATIENT: 'contraband-check-inpatient',
  CONTRABAND_CHECK_RESIDENTIAL: 'contraband-check-residential',
  DAILY_NURSING_FLOW_SHEET: 'daily-nursing-flow-sheet',
  DISCHARGE_INPATIENT: 'discharge-inpatient',
  DISCHARGE_RESIDENTIAL: 'discharge-residential',
  DISCHARGE_PLAN: 'discharge-plan',
  EMAR: 'emar',
  ENGAGEMENT_SESSION_NOTE: 'engagement-session-note',
  INFORMED_CONSENT: 'informed-consent',
  INPATIENT_NOTE: 'inpatient-note',
  INTERNAL_ORDER: 'internal-order',
  MED_RECONCILIATION: 'med-reconciliation',
  NURSE_PROGRESS_NOTE: 'nurse-progress-note',
  ORDER_LIST: 'order-list',
  OVERVIEW: 'overview',
  PATIENT_VALUABLES: 'inpatient-patient-valuables',
  PSYCH_PROGRESS_NOTE: 'psych-progress-note',
  PRINT_DISCHARGE: 'print-discharge',
  PRINT_LABEL: 'print-label',
  RESTRAINT_SECLUSION: 'inpatient-restraint-seclusion',
  ROOM_CHECK_INPATIENT: 'room-check-inpatient',
  ROOM_CHECK_RESIDENTIAL: 'room-check-residential',
  STAFF_DAILY_NOTE: 'inpatient-staff-daily-note',
  VIEW_DISCHARGE_INPATIENT: 'edit-discharge-date-inpatient',
  VIEW_DISCHARGE_RESIDENTIAL: 'edit-discharge-date-residential',
  VITALS: 'vitals',
  DEPENDENTS: 'dependents',
  IMPORTANT_DATES: 'important-dates',
  SERVICE_PLAN: 'service-plan',
  PSYCHOTHERAPY_PROGRESS_NOTE: 'psyco-progress-note',
  PSYCHOSOCIAL_ASSESSMENT_NOTE: 'psychosocial-progress-note',
  PSYCHIATRIC_EVALUATION: 'psychiatric-evaluation-note',
  SCANED_DOCUMENTS: 'scaned-documents',
  ADMIT_INFOR_RESIDENTIAL: 'admit-info-residential',
  NURSE_DAILY_NOTE: 'residential-nurse-daily-note',
  DAILY_STAFF_NOTE: 'residential-daily-staff-note',
  NURSING_ADMISSION_ASSESSMENT: 'inpatient-nursing-admission-assessment',
  HEALTH_PHYSICAL_NOTE: 'inpatient-healthphysicalnote',
  DYNAMIC_FORM: 'DynamicLink',

  // HR action keys
  EDUCATION_LICENSE: 'hr-education-license',
  EMPLOYMENT_HIRE_INFORMATION: 'hr-employment-hire-information',
  COMMENT: 'hr-comment',
  SPECIALTIES: 'hr-specialties',
  POSITIONS: 'hr-positions',
  IDENTIFIER: 'hr-identifier',
  SIGNATURE: 'hr-signature',
  PSYCHIATRIST: 'hr-psychiatrist',
  ROLES: 'hr-roles',
  COPY_ROLES: 'hr-copy-roles',
  PROGRAMS: 'hr-programs',

  //Triage action keys
  STANDING_ORDERS: 'standing-orders',
  DC_ORDERS: 'discharge-orders',
  OBSERVATION_LOG: 'observation-log',
  LAB: 'lab-order',
  ADMIT_INFO_TRIAGE: 'admit-info-triage',
};

export const RESIDENTIAL_ACTION_KEYS = [
  { key: 'dischargeHeader', text: t('Discharge'), itemType: ContextualMenuItemType.Header },
  ACTION_KEY.DISCHARGE_RESIDENTIAL,
  ACTION_KEY.PRINT_DISCHARGE,
  ACTION_KEY.VIEW_DISCHARGE_RESIDENTIAL,
  { key: 'note', text: t('Note'), itemType: ContextualMenuItemType.Header },
  ACTION_KEY.DAILY_STAFF_NOTE,
  ACTION_KEY.NURSE_DAILY_NOTE,
  ACTION_KEY.PSYCH_PROGRESS_NOTE,
  ACTION_KEY.PSYCHIATRIC_EVALUATION,
  { key: 'others', text: t('Others'), itemType: ContextualMenuItemType.Header },
  ACTION_KEY.ADMIT_INFOR_RESIDENTIAL,
  ACTION_KEY.ALERTS,
  ACTION_KEY.BHT_ROUNDING_SHEET_RESIDENTIAL,
  ACTION_KEY.CINA,
  ACTION_KEY.CIWA,
  ACTION_KEY.COMMENTS,
  ACTION_KEY.CONTRABAND_CHECK_RESIDENTIAL,
  ACTION_KEY.DEPENDENTS,
  ACTION_KEY.EMAR,
  ACTION_KEY.IMPORTANT_DATES,
  ACTION_KEY.INTERNAL_ORDER,
  ACTION_KEY.OVERVIEW,
  ACTION_KEY.PATIENT_VALUABLES,
  ACTION_KEY.RESTRAINT_SECLUSION,
  ACTION_KEY.ROOM_CHECK_RESIDENTIAL,
  ACTION_KEY.SCANED_DOCUMENTS,
];

export const INPATIENT_ACTION_KEYS = [
  { key: 'dischargeHeader', text: t('Discharge'), itemType: ContextualMenuItemType.Header },
  ACTION_KEY.DISCHARGE_INPATIENT,
  ACTION_KEY.DISCHARGE_PLAN,
  ACTION_KEY.PRINT_DISCHARGE,
  ACTION_KEY.VIEW_DISCHARGE_INPATIENT,
  { key: 'inpatientStatus', text: t('Inpatient Status'), itemType: ContextualMenuItemType.Header },
  ACTION_KEY.ADMISSION_ORDERS,
  ACTION_KEY.ANCILLARY_ORDERS,
  { key: 'note', text: t('Note'), itemType: ContextualMenuItemType.Header },
  ACTION_KEY.COMPREHENSIVE_NURSING_ASSESSMENT,
  ACTION_KEY.ENGAGEMENT_SESSION_NOTE,
  ACTION_KEY.HEALTH_PHYSICAL_NOTE,
  ACTION_KEY.INPATIENT_NOTE,
  ACTION_KEY.NURSE_PROGRESS_NOTE,
  ACTION_KEY.NURSING_ADMISSION_ASSESSMENT,
  ACTION_KEY.PSYCH_PROGRESS_NOTE,
  ACTION_KEY.PSYCHIATRIC_EVALUATION,
  ACTION_KEY.PSYCHOSOCIAL_ASSESSMENT_NOTE,
  ACTION_KEY.PSYCHOTHERAPY_PROGRESS_NOTE,
  ACTION_KEY.SERVICE_PLAN,
  ACTION_KEY.STAFF_DAILY_NOTE,
  { key: 'others', text: t('Others'), itemType: ContextualMenuItemType.Header },
  ACTION_KEY.ALERTS,
  ACTION_KEY.ART,
  ACTION_KEY.ASAM,
  ACTION_KEY.BHT_ROUNDING_SHEET_INPATIENT,
  ACTION_KEY.CINA,
  ACTION_KEY.CIWA,
  ACTION_KEY.COMMENTS,
  ACTION_KEY.CONTRABAND_CHECK_INPATIENT,
  ACTION_KEY.DAILY_NURSING_FLOW_SHEET,
  ACTION_KEY.EMAR,
  ACTION_KEY.INFORMED_CONSENT,
  ACTION_KEY.INTERNAL_ORDER,
  ACTION_KEY.MED_RECONCILIATION,
  ACTION_KEY.ORDER_LIST,
  ACTION_KEY.OVERVIEW,
  ACTION_KEY.PATIENT_VALUABLES,
  ACTION_KEY.PRINT_LABEL,
  ACTION_KEY.RESTRAINT_SECLUSION,
  ACTION_KEY.ROOM_CHECK_INPATIENT,
  ACTION_KEY.VITALS,
];

export const ENROLLMENT_ACTION_KEYS = [
  ACTION_KEY.COMMENT,
  ACTION_KEY.EDUCATION_LICENSE,
  ACTION_KEY.EMPLOYMENT_HIRE_INFORMATION,
  ACTION_KEY.COPY_ROLES,
  ACTION_KEY.IDENTIFIER,
  ACTION_KEY.POSITIONS,
  ACTION_KEY.PSYCHIATRIST,
  ACTION_KEY.ROLES,
  ACTION_KEY.SIGNATURE,
  ACTION_KEY.SPECIALTIES,
  ACTION_KEY.PROGRAMS,
];

export const TRIAGE_ACTION_KEYS = [
  // { key: 'dischargeHeader', text: t('Discharge'), itemType: ContextualMenuItemType.Header },
  // ACTION_KEY.DISCHARGE_INPATIENT,
  // ACTION_KEY.DISCHARGE_PLAN,
  // ACTION_KEY.PRINT_DISCHARGE,
  // ACTION_KEY.VIEW_DISCHARGE_INPATIENT,
  { key: 'inpatientStatus', text: t('Inpatient Status'), itemType: ContextualMenuItemType.Header },
  ACTION_KEY.ADMIT_INFO_TRIAGE,
  // ACTION_KEY.ANCILLARY_ORDERS,
  // ACTION_KEY.DC_ORDERS,
  // ACTION_KEY.STANDING_ORDERS,
  // { key: 'others', text: t('Others'), itemType: ContextualMenuItemType.Header },
  // ACTION_KEY.EMAR,
  // ACTION_KEY.LAB,
  // ACTION_KEY.PATIENT_VALUABLES,
  // ACTION_KEY.VITALS,
];

export const ACTION_NAME = {
  // Inpatient-Residential Actions
  [ACTION_KEY.ART]: { title: t('ART / CFT'), component: ART, route: ROUTE_NAME.ART_CFT },
  [ACTION_KEY.PATIENT_VALUABLES]: { title: t('Patient Valuables'), component: PatientValuables },
  [ACTION_KEY.CONTRABAND_CHECK_INPATIENT]: {
    title: t('Contraband Check'),
    component: ContrabandCheckInpatient,
  },
  [ACTION_KEY.ROOM_CHECK_INPATIENT]: { title: t('Room Check'), component: RoomCheckInpatient },
  [ACTION_KEY.ROOM_CHECK_RESIDENTIAL]: { title: t('Room Check'), component: RoomCheckResidential },
  [ACTION_KEY.STAFF_DAILY_NOTE]: { title: t('Staff Daily Note'), component: StaffDailyNote },
  [ACTION_KEY.CIWA]: { title: t('CIWA'), component: CIWA, route: ROUTE_NAME.CIWA },
  [ACTION_KEY.VITALS]: { title: t('Vitals'), component: Vitals, route: ROUTE_NAME.VITALS },
  [ACTION_KEY.EMAR]: { title: t('eMar'), component: InpatientEmar },
  [ACTION_KEY.ASAM]: { title: t('ASAM'), component: InpatientASAM, route: ROUTE_NAME.ASAM },
  [ACTION_KEY.BHT_ROUNDING_SHEET_INPATIENT]: {
    title: t('BHT Rounding Sheet'),
    component: BHTInpatient,
  },
  [ACTION_KEY.BHT_ROUNDING_SHEET_RESIDENTIAL]: {
    title: t('BHT Rounding Sheet'),
    component: BHTResidential,
  },
  [ACTION_KEY.COMMENTS]: { title: t('Comment'), component: Comments },
  [ACTION_KEY.DISCHARGE_INPATIENT]: {
    title: t('Discharge'),
    component: DischargeInpatient,
    getTitle: ({ dischargeDate }) => (dischargeDate ? t('Edit Discharge') : t('Discharge')),
  },
  [ACTION_KEY.DISCHARGE_RESIDENTIAL]: {
    title: t('Discharge'),
    component: DischargeResidential,
    getTitle: ({ dischargeDate }) => (dischargeDate ? t('Edit Discharge') : t('Discharge')),
  },
  [ACTION_KEY.PRINT_DISCHARGE]: { title: t('Print Discharge'), urlKey: 'DISCHARGE_PRINT' },
  [ACTION_KEY.VIEW_DISCHARGE_INPATIENT]: {
    title: t('View Discharge'),
    component: ViewDischargeInpatient,
  },
  [ACTION_KEY.VIEW_DISCHARGE_RESIDENTIAL]: {
    title: t('View Discharge'),
    component: ViewDischargeResidential,
  },
  [ACTION_KEY.ORDER_LIST]: { title: t('Order List'), component: OrderList },
  [ACTION_KEY.INFORMED_CONSENT]: {
    title: t('Informed Consent'),
    component: InformedConsent,
    route: ROUTE_NAME.INFORMED_CONSENT,
  },
  [ACTION_KEY.RESTRAINT_SECLUSION]: {
    title: t('Restraint And Seclusion'),
    component: RestraintAndSeclusion,
  },
  [ACTION_KEY.PRINT_LABEL]: { title: t('Print Label'), urlKey: 'PRINT_LABEL' },
  [ACTION_KEY.NURSE_PROGRESS_NOTE]: {
    title: t('Nurse Progress Note'),
    component: NurseProgressNote,
    route: ROUTE_NAME.NURSE_PROGRESS_NOTE,
  },
  [ACTION_KEY.OVERVIEW]: { title: t('Overview'), component: Overview },
  [ACTION_KEY.DAILY_NURSING_FLOW_SHEET]: {
    title: t('Daily Nursing Flow Sheet'),
    component: DailyNursingFlowSheet,
  },
  [ACTION_KEY.MED_RECONCILIATION]: {
    title: t('Med Reconciliation'),
    component: MedicationReconciliation,
    route: ROUTE_NAME.PRESCRIPTION,
  },
  [ACTION_KEY.CINA]: { title: t('CINA'), component: CINA, route: ROUTE_NAME.CINA },
  [ACTION_KEY.INTERNAL_ORDER]: {
    title: t('Internal Order'),
    component: InpatientInternalOrder,
    route: ROUTE_NAME.INTERNAL_ORDERS,
  },
  [ACTION_KEY.ADMISSION_ORDERS]: { title: t('Admission Orders'), component: AdmissionOrders },
  [ACTION_KEY.ANCILLARY_ORDERS]: { title: t('Ancillary Orders'), component: AncillaryOrders },
  [ACTION_KEY.ALERTS]: { title: t('Alerts'), component: AlertAction },
  [ACTION_KEY.ENGAGEMENT_SESSION_NOTE]: {
    title: t('Engagement Session Note'),
    component: EngagementSessionNote,
    route: ROUTE_NAME.ENGAGEMENT_SESSION_NOTE,
  },
  [ACTION_KEY.COMPREHENSIVE_NURSING_ASSESSMENT]: {
    title: t('Comprehensive Nursing Assessment'),
    component: ComprehensiveNursingAssessment,
  },
  [ACTION_KEY.DISCHARGE_PLAN]: {
    title: t('Discharge Plan'),
    component: DischargePlan,
    route: ROUTE_NAME.DISCHARGE_PLAN,
  },
  [ACTION_KEY.DEPENDENTS]: { title: t('Dependents'), component: Dependents },
  [ACTION_KEY.PSYCH_PROGRESS_NOTE]: {
    title: t('Psych Progress Note'),
    component: InpatientPsychNote,
    route: ROUTE_NAME.PSYCH_NOTE,
  },
  [ACTION_KEY.INPATIENT_NOTE]: {
    title: t('Inpatient Note'),
    component: InpatientNote,
  },
  [ACTION_KEY.SERVICE_PLAN]: {
    title: t('Service Plan'),
    component: ServicePlan,
    route: ROUTE_NAME.SERVICE_PLAN,
  },
  [ACTION_KEY.PSYCHOTHERAPY_PROGRESS_NOTE]: {
    title: t('Psychotherapy Progress Note'),
    component: PsychotherapyProgressNote,
    route: ROUTE_NAME.PSYCHOTHERAPY_PROGRESS_NOTE,
  },
  [ACTION_KEY.IMPORTANT_DATES]: {
    title: t('Important Dates'),
    component: ImportantDates,
  },
  [ACTION_KEY.PSYCHOSOCIAL_ASSESSMENT_NOTE]: {
    title: t('Psychosocial Assessment Note'),
    component: PsychosocialAssessmentNote,
    route: ROUTE_NAME.PSYCHOSOCIAL_ASSESSMENT_NOTE,
  },
  [ACTION_KEY.CONTRABAND_CHECK_RESIDENTIAL]: {
    title: t('Contraband Check'),
    component: ContrabandCheckResidential,
  },
  [ACTION_KEY.PSYCHIATRIC_EVALUATION]: {
    title: t('Psychiatric Evaluation Note'),
    component: InpatientPsychEval,
    route: ROUTE_NAME.PSYCH_EVALUATION_NOTE,
  },
  [ACTION_KEY.SCANED_DOCUMENTS]: {
    title: t('Scaned Documents'),
    component: ScanedDocuments,
    route: ROUTE_NAME.SCANNED_DOCUMENTS,
  },
  [ACTION_KEY.ADMIT_INFOR_RESIDENTIAL]: {
    title: t('Admit Info'),
    component: AdmitInforResidential,
  },
  [ACTION_KEY.NURSE_DAILY_NOTE]: {
    title: t('Nurse Daily Note'),
    component: NurseDailyNote,
  },
  [ACTION_KEY.DAILY_STAFF_NOTE]: {
    title: t('Daily Staff Note'),
    component: DailyStaffNote,
  },
  [ACTION_KEY.NURSING_ADMISSION_ASSESSMENT]: {
    title: t('Nursing Admission Assessment'),
    component: NursingAdmissionAssessment,
  },
  [ACTION_KEY.HEALTH_PHYSICAL_NOTE]: {
    title: t('Health Physical Note'),
    component: HealthPhysicalNote,
  },
  [ACTION_KEY.DYNAMIC_FORM]: {
    title: t('Dynamic Form'),
    component: DynamicForm,
    getFormName: (dynamicFormName) => dynamicFormName,
    route: ROUTE_NAME.DYNAMIC_FORM,
  },

  // HR Actions
  [ACTION_KEY.EDUCATION_LICENSE]: { title: t('Education/ License'), component: Education },
  [ACTION_KEY.EMPLOYMENT_HIRE_INFORMATION]: {
    title: t('Employment Hire Information'),
    component: EmploymentHireInformation,
  },
  [ACTION_KEY.COPY_ROLES]: {
    title: t('HR Role Transfer'),
    component: RoleTransfer,
  },
  [ACTION_KEY.COMMENT]: { title: t('Comments'), component: HRComments },
  [ACTION_KEY.SPECIALTIES]: { title: t('Specialties'), component: Specialties },
  [ACTION_KEY.POSITIONS]: { title: t('Positions'), component: Positions },
  [ACTION_KEY.IDENTIFIER]: { title: t('Identifiers'), component: Identifier },
  [ACTION_KEY.SIGNATURE]: { title: t('Signature & Initials'), component: Signature },
  [ACTION_KEY.PSYCHIATRIST]: { title: t('Psychiatrist Association'), component: Psychiatrist },
  [ACTION_KEY.ROLES]: { title: t('Roles'), component: Roles },
  [ACTION_KEY.PROGRAMS]: { title: t('Programs'), component: Programs },

  //Triage Actions
  [ACTION_KEY.STANDING_ORDERS]: { title: t('Standing Order'), component: '' },
  [ACTION_KEY.DC_ORDERS]: { title: t('Discharge Order'), component: '' },
  [ACTION_KEY.OBSERVATION_LOG]: { title: t('Observation Log'), component: '' },
  [ACTION_KEY.LAB]: { title: t('Laboratory'), component: Laboratory },
  [ACTION_KEY.ADMIT_INFO_TRIAGE]: { title: t('Admit Info'), component: AdmitInfoTriage },
};
