import { ROUTE_NAME } from './routes';

const FORM_ID = {
  PATIENT_ASSIGNMENT_PCP: 'patientAssignmentPCP',
  PATIENT_ASSIGNMENT_GROUP: 'patientAssignmentGroup',
  PATIENT_ASSIGNMENT_PRIORITY: 'patientAssignmentPriority',
  PATIENT_ASSIGNMENT_SITE: 'patientAssignmentSite',
  PATIENT_ASSIGNMENT_TEAM: 'patientAssignmentTeam',
  HR: 'HRForm',

  //INPATIENT
  INPATIENT_SITE_CHECK: 'inpatient-site-check',
  INPATIENT_ROOM: 'inpatient-room',
  INPATIENT_ADMISSION: 'inpatient-admission',
  INPATIENT_DISCHARGE: 'inpatient-discharge',
  INPATIENT_DISCHARGE_PLAN: 'inpatient-discharge-plan',
  INPATIENT_COMPREHENSIVE_NURSING_ASSESSMENT: 'inpatient-comprehensive-nursing-assessment',
  INPATIENT_ENGAGEMENT_SESSION_NOTE: 'inpatient-engagement-session-note',
  INPATIENT_HEALTH_PHYSICAL_NOTE: 'health-Physical-Note',
  INPATIENT_NOTE: 'inpatient-note',
  INPATIENT_NURSE_PROGRESS_NOTE: 'inpatient-nurse-progress-note',
  INPATIENT_NURSING_ADMISSION_ASSESSMENT: 'inpatient-nursing-admission-assessment',
  INPATIENT_PSYCHIATRIC_EVALUATION: 'inpatient-psychiatric-evaluation-note',
  INPATIENT_PSYCHOTHERAPY_PROGRESS_NOTE: 'inpatient-psyco-progress-note',
  INPATIENT_PSYCHOSOCIAL_ASSESSMENT_NOTE: 'inpatient-psychosocial-progress-note',
  INPATIENT_DAILY_STAFF_NOTE: 'inpatient-daily-staff-note',
  INPATIENT_ART: 'inpatient-child-and-family',
  INPATIENT_CIWA: 'inpatient-ciwa',
  INPATIENT_CINA: 'inpatient-cina',
  INPATIENT_COMMENTS: 'inpatient-comments',
  INPATIENT_CONTRABAND_CHECK: 'inpatient-contraband-check',
  INPATIENT_DAILY_NURSING_FLOW_SHEET: 'inpatient-daily-nursing-flow-sheet',
  INPATIENT_INTERNAL_ORDER: 'inpatient-internal-order',
  INPATIENT_ORDER_LIST: 'inpatient-order-list',
  INPATIENT_PATIENT_VALUABLES: 'inpatient-patient-valuables',
  INPATIENT_RESTRAINT_SECLUSION: 'inpatient-restraint-seclusion',
  INPATIENT_ROOM_CHECK: 'inpatient-room-check',

  //RESIDENTIAL
  RESIDENTIAL_ADMISSION: 'residential-admission',
  RESIDENTIAL_ADMIT_INFO: 'residential-admit-info',
  RESIDENTIAL_DISCHARGE: 'residential-discharge',
  RESIDENTIAL_ROOM: 'residential-room',
  RESIDENTIAL_SITE_CHECK: 'residential-site-check',
  RESIDENTIAL_ATTENDANCE: 'residential-attendance',
  RESIDENTIAL_CONTRABAND_CHECK: 'residential-contraband-check',
  RESIDENTIAL_DAILY_STAFF_NOTE: 'residential-daily-staff-note',
  RESIDENTIAL_DEPENDENTS: 'residential-dependents',
  RESIDENTIAL_IMPORTANT_DATES: 'residential-importants-date',
  RESIDENTIAL_NURSE_DAILY_NOTE: 'residential-nurse-daily-note',
  RESIDENTIAL_PATIENT_VALUABLES: 'residential-patient-valuables',
  RESIDENTIAL_ROOM_CHECK: 'residential-room-check',
  RESIDENTIAL_SCANED_DOCUMENTS: 'residential-scaned-documents',

  //APPOINMENT
  APPOINTMENT: 'appointment',
  APPOINTMENT_TIME_BLOCK: 'appointment-time-block',
  APPOINTMENT_AIMS: 'appointment-aims',

  //PAYMENT
  PAYMENT: 'payment',
  PAYMENT_INFO: 'payment-info',

  //PLAN
  SNCD_PLAN: 'sncd-plan',
  CRISIS_INTERVENTION_PLAN: 'crisis-intervention-plan',
  CRISIS_PREVENTION_PLAN: 'crisis-prevention-plan',
  SUPPORT_PLAN: 'support-plan',
  SERVICES_PLAN: 'services-plan',

  //NOTE
  DYNAMIC_PROGRESS_NOTE: 'dynamic-progress-note',
  PSYCH_NOTE: 'psych-note',
  PSYCH_EVALUATION_NOTE: 'psych-evaluation-note',
  CM_NOTE: 'cm-note',
  PCP_NOTE: 'pcp-note',
  GROUP_NOTE: 'group-note',

  //FORM
  PRESCRIPTION: 'prescription',
  SDOH: 'sdoh',
  EMAR: 'emar',
  ASAM: 'asam',
  ASSESSMENT: 'assessment',
  PCP_COMMUNICATION: 'pcp-communication',
  LABORATORY: 'laboratory',
  QUICK_ENROLLMENT: 'quick-enrollment',

  //BATCH
  BATCH_837: 'Batch837',
  RECONCILE_835: 'Batch835',
  RECONCILE_277: 'Reconcile277',
  PAYOR_EDITOR: 'PayorEditor',
  BATCH_834: 'Batch834',
  PAYOR_ASSIGNMENT: 'PayorAssignment',
  FEE_SCHEDULE: 'FeeScheduleService',
  ENCOUNTER: 'Encounter',
  REPORTING_UPLOAD: 'reporting-upload',
  PROVIDER: 'provider',
  RENDERING_PROVIDER: 'rendering-provider',
  REMIT_EOB: 'remit-eob',
  RCM_REPORT: 'rcp-report',

  //QUICK ACCESS
  PATIENT_NOTE: 'quick-access-patient-note',
  SPECIALIST_REFERRAL: 'specialist-referral',

  //CARD VIEW
  DEMOGRAPHIC: 'card-view-demographic',
  VITALS: 'card-view-vitals',
  ALLERGEN: 'card-view-allergen',
  PROBLEM_LIST: 'card-view-problem-list',
  INJECTION: 'card-view-injection',
  PCL5: 'card-view-pcl5',
  PHQ: 'card-view-phq',
  ACE: 'card-view-ace',
  PC_PTSD: 'card-view-pc-ptsd',
  AUDIT: 'card-view-audit',

  //COVER SHEET
  COVER_SHEET_PATIENT: 'coversheet-patient',
  COVER_SHEET_ADDRESS: 'coversheet-address',
  COVER_SHEET_CONTACT: 'coversheet-contact',
  COVER_SHEET_ADDITIONAL_INFO: 'coversheet-additional-info',
  COVER_SHEET_CONSENT: 'coversheet-consent',
  COVER_SHEET_PATIENT_PORTAL: 'coversheet-patient-portal',
  COVER_SHEET_INSURANCE: 'coversheet-insurance',

  //CYBHI CLIENT DATA
  CYBHI_CLIENT_DATA: 'cybhi-client-data',

  GROUP_EDITOR: 'group-editor',
  GROUP_EDITOR_PATIENT: 'group-editor-patient',

  SETUP_TRIGGER_MESSAGE: 'setup-trigger-message',
  SETUP_TRIGGER_EMAIL: 'setup-trigger-email',
  PHQ_A: 'phq-a',
  SUICIDE_SEVERITY_RATING_SCALE: 'suicideSeverityRatingScale',
  GAD_7: 'gad-7',
  PRAPARE: 'PRAPARE',
  COWS: 'COWS',
  ENCOUNTER_ENTRY: 'ENCOUNTER_ENTRY',
  ENGAGEMENT: 'ENGAGEMENT',
  ANNOUNCEMENT: 'ANNOUNCEMENT',
  FAMILY_HEALTH: 'FAMILY_HEALTH',
  PLACEMENT: 'PLACEMENT',
  IMPLANTABLE_DEVICES: 'IMPLANTABLE_DEVICES',

  COT: 'CourtOrderedTreatment',
  COT_AMENDMENT: 'cot-amendment',
  COT_JUDICIAL_REVIEW: 'cot-judicial-review',
  COT_STATUS_REPORT: 'cot-status-report',
  COT_COMMENT: 'cot-comment',
  NURSE_PROGRESS_NOTE: 'nurse-progress-note',
  INPATIENT_NOTE: 'inpatient-note',
  RE_ASSIGNMENT: 're-assignment',
  WHO_ASSIST: 'who-assist',
  DIRECT_MESSAGING: 'direct-messaging',
  SAFET: 'SAFET',
  SUICIDE_RISK_SCREENING: 'suicide-risk-screening',

  //IBHIS
  IBHIS_SERVICE_REQUEST_LOG_INPUT: 'ibhis-service-request-log-input',
  IBHIS_CANS_INPUT: 'ibhis-cans-input',
  IBHIS_LOCUS_INPUT: 'ibhis-locus-input',
  IBHIS_CLIENT_DATA: 'ibhis-client-data',
  IBHIS_PSC_INPUT: 'ibhis-psc-input',
  IBHIS_CAREGIVER_INPUT: 'ibhis-caregiver-input',
  IBHIS_CSI_ASSESSMENT_INPUT: 'ibhis-csi-assessment-input',

  //SUPPORT
  SP_PROGRESS_NOTES_ORG_FIELDS: 'sp-progress-notes-org-fields',
  SP_IBHIS_SITE_TO_PROGRAM_MAPPING: 'sp-ibhis-site-to-program-mapping',

  //FUNDING SOURCE
  FUNDING_SOURCE_INPUT: 'funding-source-input',
};

const FORM_LIST = [
  {
    rootPage: ROUTE_NAME.PATIENT_ASSIGNMENT,
    id: FORM_ID.PATIENT_ASSIGNMENT_GROUP,
    name: 'Patient Assignment Group',
  },
  {
    rootPage: ROUTE_NAME.PATIENT_ASSIGNMENT,
    id: FORM_ID.PATIENT_ASSIGNMENT_PCP,
    name: 'Patient Assignment PCP',
  },
  {
    rootPage: ROUTE_NAME.PATIENT_ASSIGNMENT,
    id: FORM_ID.PATIENT_ASSIGNMENT_PRIORITY,
    name: 'Patient Assignment Priority',
  },
  {
    rootPage: ROUTE_NAME.PATIENT_ASSIGNMENT,
    id: FORM_ID.PATIENT_ASSIGNMENT_SITE,
    name: 'Patient Assignment Site',
  },
  {
    rootPage: ROUTE_NAME.PATIENT_ASSIGNMENT,
    id: FORM_ID.PATIENT_ASSIGNMENT_TEAM,
    name: 'Patient Assignment Team',
  },
  { rootPage: ROUTE_NAME.HR, id: FORM_ID.HR, name: 'HR' },

  {
    rootPage: ROUTE_NAME.INPATIENT,
    id: FORM_ID.INPATIENT_SITE_CHECK,
    name: 'Inpatient Site Check',
  },
  { rootPage: ROUTE_NAME.INPATIENT, id: FORM_ID.INPATIENT_ROOM, name: 'Inpatient Room' },
  { rootPage: ROUTE_NAME.INPATIENT, id: FORM_ID.INPATIENT_ADMISSION, name: 'Inpatient Admission' },
  { rootPage: ROUTE_NAME.INPATIENT, id: FORM_ID.INPATIENT_DISCHARGE, name: 'Inpatient Discharge' },
  {
    rootPage: ROUTE_NAME.INPATIENT,
    id: FORM_ID.INPATIENT_DISCHARGE_PLAN,
    name: 'Inpatient Discharge Plan',
  },
  {
    rootPage: ROUTE_NAME.INPATIENT,
    id: FORM_ID.INPATIENT_COMPREHENSIVE_NURSING_ASSESSMENT,
    name: 'Comprehensive Nursing Assessment',
  },
  {
    rootPage: ROUTE_NAME.ENGAGEMENT_SESSION_NOTE,
    id: FORM_ID.INPATIENT_ENGAGEMENT_SESSION_NOTE,
    name: 'Engagement Session Note',
  },
  {
    rootPage: ROUTE_NAME.INPATIENT,
    id: FORM_ID.INPATIENT_HEALTH_PHYSICAL_NOTE,
    name: 'Health Physical Note',
  },
  { rootPage: ROUTE_NAME.INPATIENT, id: FORM_ID.INPATIENT_NOTE, name: 'Inpatient Note' },
  {
    rootPage: ROUTE_NAME.INPATIENT,
    id: FORM_ID.INPATIENT_NURSE_PROGRESS_NOTE,
    name: 'Nurse Progress Note',
  },

  {
    rootPage: ROUTE_NAME.INPATIENT,
    id: FORM_ID.INPATIENT_NURSING_ADMISSION_ASSESSMENT,
    name: 'Nursing Admission Assessment',
  },
  {
    rootPage: ROUTE_NAME.PSYCH_EVALUATION_NOTE,
    id: FORM_ID.INPATIENT_PSYCHIATRIC_EVALUATION,
    name: 'Psychiatric Evaluation Note',
  },
  {
    rootPage: ROUTE_NAME.PSYCHOTHERAPY_PROGRESS_NOTE,
    id: FORM_ID.INPATIENT_PSYCHOTHERAPY_PROGRESS_NOTE,
    name: 'Psychotherapy Progress Note',
  },
  {
    rootPage: ROUTE_NAME.PSYCHOSOCIAL_ASSESSMENT_NOTE,
    id: FORM_ID.INPATIENT_PSYCHOSOCIAL_ASSESSMENT_NOTE,
    name: 'Psychosocial Assessment Note',
  },
  {
    rootPage: ROUTE_NAME.INPATIENT,
    id: FORM_ID.INPATIENT_DAILY_STAFF_NOTE,
    name: 'Daily Staff Note',
  },
  { rootPage: ROUTE_NAME.INPATIENT, id: FORM_ID.INPATIENT_ART, name: 'ART / CFT' },
  { rootPage: ROUTE_NAME.INPATIENT, id: FORM_ID.INPATIENT_CIWA, name: 'CIWA' },
  { rootPage: ROUTE_NAME.INPATIENT, id: FORM_ID.INPATIENT_CINA, name: 'CINA' },
  { rootPage: ROUTE_NAME.INPATIENT, id: FORM_ID.INPATIENT_COMMENTS, name: 'Comment' },
  {
    rootPage: ROUTE_NAME.INPATIENT,
    id: FORM_ID.INPATIENT_CONTRABAND_CHECK,
    name: 'Contraband Check',
  },
  {
    rootPage: ROUTE_NAME.INPATIENT,
    id: FORM_ID.INPATIENT_DAILY_NURSING_FLOW_SHEET,
    name: 'Daily Nursing Flow Sheet',
  },
  { rootPage: ROUTE_NAME.INPATIENT, id: FORM_ID.INPATIENT_INTERNAL_ORDER, name: 'Internal Order' },
  { rootPage: ROUTE_NAME.INPATIENT, id: FORM_ID.INPATIENT_ORDER_LIST, name: 'Order List' },
  {
    rootPage: ROUTE_NAME.INPATIENT,
    id: FORM_ID.INPATIENT_PATIENT_VALUABLES,
    name: 'Patient Valuables',
  },
  {
    rootPage: ROUTE_NAME.INPATIENT,
    id: FORM_ID.INPATIENT_RESTRAINT_SECLUSION,
    name: 'Restraint And Seclusion',
  },
  { rootPage: ROUTE_NAME.INPATIENT, id: FORM_ID.INPATIENT_ROOM_CHECK, name: 'Room Check' },

  {
    rootPage: ROUTE_NAME.RESIDENTIAL,
    id: FORM_ID.RESIDENTIAL_ADMISSION,
    name: 'Residential Admission',
  },
  {
    rootPage: ROUTE_NAME.RESIDENTIAL,
    id: FORM_ID.RESIDENTIAL_ADMIT_INFO,
    name: 'Residential Admit Info',
  },
  {
    rootPage: ROUTE_NAME.RESIDENTIAL,
    id: FORM_ID.RESIDENTIAL_DISCHARGE,
    name: 'Residential Discharge',
  },
  { rootPage: ROUTE_NAME.RESIDENTIAL, id: FORM_ID.RESIDENTIAL_ROOM, name: 'Residential Room' },
  {
    rootPage: ROUTE_NAME.RESIDENTIAL,
    id: FORM_ID.RESIDENTIAL_SITE_CHECK,
    name: 'Residential Site Check',
  },
  {
    rootPage: ROUTE_NAME.RESIDENTIAL,
    id: FORM_ID.RESIDENTIAL_ATTENDANCE,
    name: 'Residential Attendance',
  },
  {
    rootPage: ROUTE_NAME.RESIDENTIAL,
    id: FORM_ID.RESIDENTIAL_CONTRABAND_CHECK,
    name: 'Residential Contraband Check',
  },
  {
    rootPage: ROUTE_NAME.RESIDENTIAL,
    id: FORM_ID.RESIDENTIAL_DAILY_STAFF_NOTE,
    name: 'Residential Daily Staff Note',
  },
  {
    rootPage: ROUTE_NAME.RESIDENTIAL,
    id: FORM_ID.RESIDENTIAL_DEPENDENTS,
    name: 'Residential Dependents',
  },
  {
    rootPage: ROUTE_NAME.RESIDENTIAL,
    id: FORM_ID.RESIDENTIAL_IMPORTANT_DATES,
    name: 'Residential Important Dates',
  },
  {
    rootPage: ROUTE_NAME.RESIDENTIAL,
    id: FORM_ID.RESIDENTIAL_NURSE_DAILY_NOTE,
    name: 'Residential Nurse Daily Note',
  },
  {
    rootPage: ROUTE_NAME.RESIDENTIAL,
    id: FORM_ID.RESIDENTIAL_PATIENT_VALUABLES,
    name: 'Residential Patient Valuables',
  },
  {
    rootPage: ROUTE_NAME.RESIDENTIAL,
    id: FORM_ID.RESIDENTIAL_ROOM_CHECK,
    name: 'Residential Room Check',
  },
  {
    rootPage: ROUTE_NAME.RESIDENTIAL,
    id: FORM_ID.RESIDENTIAL_SCANED_DOCUMENTS,
    name: 'Residential Scan Documents',
  },

  { rootPage: ROUTE_NAME.APPOINTMENT, id: FORM_ID.APPOINTMENT, name: 'Appointment' },
  {
    rootPage: ROUTE_NAME.APPOINTMENT,
    id: FORM_ID.APPOINTMENT_TIME_BLOCK,
    name: 'Appointment Time Block',
  },
  { rootPage: ROUTE_NAME.APPOINTMENT, id: FORM_ID.APPOINTMENT_AIMS, name: 'Appointment AIMS' },

  { rootPage: ROUTE_NAME.PAYMENT_MANAGEMENT, id: FORM_ID.PAYMENT, name: 'Payment' },
  {
    rootPage: ROUTE_NAME.PAYMENT_MANAGEMENT,
    id: FORM_ID.PAYMENT_INFO,
    name: 'Payment Information',
  },
  // {
  //   rootPage: ROUTE_NAME.DYNAMIC_PROGRESS_NOTE,
  //   id: FORM_ID.DYNAMIC_PROGRESS_NOTE,
  //   name: 'Dynamic Progress Note',
  // },
  { rootPage: ROUTE_NAME.SNCD, id: FORM_ID.SNCD_PLAN, name: 'SNCD Plan' },
  {
    rootPage: ROUTE_NAME.CRISIS_PLAN,
    id: FORM_ID.CRISIS_INTERVENTION_PLAN,
    name: 'Crisis Intervention Plan',
  },
  {
    rootPage: ROUTE_NAME.CRISIS_PREVENTION_PLAN,
    id: FORM_ID.CRISIS_PREVENTION_PLAN,
    name: 'Crisis Prevention Plan',
  },
  { rootPage: ROUTE_NAME.SUPPORT_PLAN, id: FORM_ID.SUPPORT_PLAN, name: 'Support & Safety Plan' },
  { rootPage: ROUTE_NAME.SERVICE_PLAN, id: FORM_ID.SERVICES_PLAN, name: 'Services Plan' },

  { rootPage: ROUTE_NAME.PSYCH_NOTE, id: FORM_ID.PSYCH_NOTE, name: 'Psych Note' },
  { rootPage: ROUTE_NAME.CM_NOTE, id: FORM_ID.CM_NOTE, name: 'CM Note' },
  { rootPage: ROUTE_NAME.PCP_NOTE, id: FORM_ID.PCP_NOTE, name: 'PCP Note' },
  { rootPage: ROUTE_NAME.GROUP_NOTE, id: FORM_ID.GROUP_NOTE, name: 'Group Note' },

  // { rootPage: ROUTE_NAME.PRESCRIPTION, id: FORM_ID.PRESCRIPTION, name: 'Precription' },
  { rootPage: ROUTE_NAME.SDOH, id: FORM_ID.SDOH, name: 'SDOH' },
  { rootPage: ROUTE_NAME.E_MAR, id: FORM_ID.EMAR, name: 'eMar' },
  { rootPage: ROUTE_NAME.ASAM, id: FORM_ID.ASAM, name: 'ASAM' },
  { rootPage: ROUTE_NAME.ASSESSMENT, id: FORM_ID.ASSESSMENT, name: 'Assessment' },
  {
    rootPage: ROUTE_NAME.PCP_COMMUNICATION,
    id: FORM_ID.PCP_COMMUNICATION,
    name: 'PCP Communication',
  },
  { rootPage: ROUTE_NAME.LAB_ORDER, id: FORM_ID.LABORATORY, name: 'Laboratory' },
  { rootPage: ROUTE_NAME.QUICK_ENROLLMENT, id: FORM_ID.QUICK_ENROLLMENT, name: 'Quick Enrollment' },

  { rootPage: ROUTE_NAME.BATCH, id: FORM_ID.BATCH_837, name: 'Batch 837' },
  { rootPage: ROUTE_NAME.BATCH, id: FORM_ID.BATCH_834, name: 'Batch 834' },
  { rootPage: ROUTE_NAME.BATCH, id: FORM_ID.REPORTING_UPLOAD, name: 'Reporting Upload' },
  { rootPage: ROUTE_NAME.BATCH, id: FORM_ID.RECONCILE_277, name: 'Reconcile 277' },
  { rootPage: ROUTE_NAME.BATCH, id: FORM_ID.ENCOUNTER, name: 'Encounter' },
  { rootPage: ROUTE_NAME.BATCH, id: FORM_ID.PAYOR_ASSIGNMENT, name: 'Payor Assignment' },
  { rootPage: ROUTE_NAME.BATCH, id: FORM_ID.PAYOR_EDITOR, name: 'Payors Editor' },
  // { rootPage: ROUTE_NAME.BATCH, id: FORM_ID.FEE_SCHEDULE, name: 'Fee Schedule' },
  // { rootPage: ROUTE_NAME.BATCH, id: FORM_ID.PROVIDER, name: 'Provider' },
  { rootPage: ROUTE_NAME.BATCH, id: FORM_ID.RENDERING_PROVIDER, name: 'Rendering Provider' },
  { rootPage: ROUTE_NAME.BATCH, id: FORM_ID.REMIT_EOB, name: 'Remit EOB' },
  { rootPage: ROUTE_NAME.BATCH, id: FORM_ID.RCM_REPORT, name: 'RCM Report' },

  { id: FORM_ID.PATIENT_NOTE, name: 'Patient Note' },
  // { id: FORM_ID.DEMOGRAPHIC, name: 'Card View Demographic' },
  { id: FORM_ID.VITALS, name: 'Card View Vitals' },
  { id: FORM_ID.ALLERGEN, name: 'Card View Allergen' },
  { id: FORM_ID.PROBLEM_LIST, name: 'Card View Problems List' },
  { id: FORM_ID.INJECTION, name: 'Card View Injection' },
  { id: FORM_ID.PCL5, name: 'Card View PCL5' },
  { id: FORM_ID.PHQ, name: 'Card View PHQ' },
  { id: FORM_ID.ACE, name: 'Card View ACE' },
  { id: FORM_ID.PC_PTSD, name: 'Card View PC PTSD' },
  { id: FORM_ID.AUDIT, name: 'Card View AUDIT' },

  { rootPage: ROUTE_NAME.CRM, id: FORM_ID.SETUP_TRIGGER_EMAIL, name: 'Setup Trigger Email' },
  { rootPage: ROUTE_NAME.CRM, id: FORM_ID.SETUP_TRIGGER_MESSAGE, name: 'Setup Trigger Message' },

  {
    rootPage: ROUTE_NAME.COVER_SHEET,
    id: FORM_ID.COVER_SHEET_PATIENT,
    name: 'Cover Sheet Patient',
  },
  {
    rootPage: ROUTE_NAME.COVER_SHEET,
    id: FORM_ID.COVER_SHEET_ADDRESS,
    name: 'Cover Sheet Address',
  },
  {
    rootPage: ROUTE_NAME.COVER_SHEET,
    id: FORM_ID.COVER_SHEET_CONTACT,
    name: 'Cover Sheet Contact',
  },
  {
    rootPage: ROUTE_NAME.COVER_SHEET,
    id: FORM_ID.COVER_SHEET_ADDITIONAL_INFO,
    name: 'Cover Sheet Additional Info',
  },
  {
    rootPage: ROUTE_NAME.COVER_SHEET,
    id: FORM_ID.COVER_SHEET_CONSENT,
    name: 'Cover Sheet Consent',
  },
  {
    rootPage: ROUTE_NAME.COVER_SHEET,
    id: FORM_ID.COVER_SHEET_INSURANCE,
    name: 'Cover Sheet Insurance',
  },
  {
    rootPage: ROUTE_NAME.CYBHI,
    id: FORM_ID.CYBHI_CLIENT_DATA,
    name: 'CYBHI Client Data',
  },
  {
    rootPage: ROUTE_NAME.SPECIALIST_REFERRAL,
    id: FORM_ID.SPECIALIST_REFERRAL,
    name: 'Specialist Referral',
  },

  {
    rootPage: ROUTE_NAME.GROUP_EDITOR,
    id: FORM_ID.GROUP_EDITOR,
    name: 'Group Editor Input',
  },
  {
    rootPage: ROUTE_NAME.GROUP_EDITOR,
    id: FORM_ID.GROUP_EDITOR_PATIENT,
    name: 'Group Editor Patient Input',
  },
  {
    rootPage: ROUTE_NAME.PHQA,
    id: FORM_ID.PHQ_A,
    name: 'PHQ-A',
  },
  {
    rootPage: ROUTE_NAME.GAD7,
    id: FORM_ID.GAD_7,
    name: 'GAD-7',
  },
  {
    rootPage: ROUTE_NAME.PRAPARE,
    id: FORM_ID.PRAPARE,
    name: 'PRAPARE',
  },
  {
    rootPage: ROUTE_NAME.COWS,
    id: FORM_ID.COWS,
    name: 'COWS',
  },
  {
    rootPage: ROUTE_NAME.ENGAGEMENT,
    id: FORM_ID.ENGAGEMENT,
    name: 'ENGAGEMENT',
  },
  {
    rootPage: ROUTE_NAME.COURT_ORDERED_TREATMENT,
    id: FORM_ID.COT,
    name: 'Court Ordered Treatment',
  },
  {
    rootPage: ROUTE_NAME.COURT_ORDERED_TREATMENT,
    id: FORM_ID.COT_AMENDMENT,
    name: 'Court Ordered Treatment - Amendments',
  },
  {
    rootPage: ROUTE_NAME.COURT_ORDERED_TREATMENT,
    id: FORM_ID.COT_JUDICIAL_REVIEW,
    name: 'Court Ordered Treatment - Judicial Review',
  },
  {
    rootPage: ROUTE_NAME.COURT_ORDERED_TREATMENT,
    id: FORM_ID.COT_STATUS_REPORT,
    name: 'Court Ordered Treatment - Status Report',
  },
  {
    rootPage: ROUTE_NAME.COURT_ORDERED_TREATMENT,
    id: FORM_ID.COT_COMMENT,
    name: 'Court Ordered Treatment - Comment',
  },
  {
    rootPage: ROUTE_NAME.ANNOUNCEMENTS,
    id: FORM_ID.ANNOUNCEMENT,
    name: 'Announcements',
  },
  {
    rootPage: ROUTE_NAME.IMPLANTABLE_DEVICES,
    id: FORM_ID.IMPLANTABLE_DEVICES,
    name: 'Implantable Devices',
  },
  {
    rootPage: ROUTE_NAME.RE_ASSIGNMENT,
    id: FORM_ID.RE_ASSIGNMENT,
    name: 'Re-Assignment',
  },
  {
    rootPage: ROUTE_NAME.WHO_ASSIST,
    id: FORM_ID.WHO_ASSIST,
    name: 'WHO - ASSIST',
  },
  {
    rootPage: ROUTE_NAME.DIRECT_MESSAGING,
    id: FORM_ID.DIRECT_MESSAGING,
    name: 'Direct Messaging',
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    id: FORM_ID.IBHIS_SERVICE_REQUEST_LOG_INPUT,
    name: 'IBHIS Service Request Log Input',
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    id: FORM_ID.IBHIS_CANS_INPUT,
    name: 'IBHIS CANS Input',
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    id: FORM_ID.IBHIS_LOCUS_INPUT,
    name: 'IBHIS LOCUS Input',
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    id: FORM_ID.IBHIS_CLIENT_DATA,
    name: 'IBHIS Client Data',
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    id: FORM_ID.IBHIS_PSC_INPUT,
    name: 'IBHIS PSC Input',
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    id: FORM_ID.IBHIS_CAREGIVER_INPUT,
    name: 'IBHIS Caregiver Input',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    id: FORM_ID.SP_PROGRESS_NOTES_ORG_FIELDS,
    name: 'Support Progress Note Organization Data',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    id: FORM_ID.SP_IBHIS_SITE_TO_PROGRAM_MAPPING,
    name: 'Support IBHIS Site to Program Mapping',
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    id: FORM_ID.IBHIS_CSI_ASSESSMENT_INPUT,
    name: 'IBHIS CSI Assessment Input',
  },
  {
    rootPage: ROUTE_NAME.FUNDING_SOURCE,
    id: FORM_ID.FUNDING_SOURCE_INPUT,
    name: 'Funding Source Input',
  },
];

export { FORM_ID, FORM_LIST };
