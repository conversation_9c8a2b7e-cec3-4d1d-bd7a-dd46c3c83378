import React, { lazy } from 'react';
import { t } from 'utils/string';
import { MODULE_NAME, ROUTE_NAME } from './routes';
import QUERY_KEY from './queryKey';

const EngagementSessionNoteInput = lazy(() =>
  import('modules/EngagementSessionNote/EngagementSessionNoteInput'),
);

const Inventory = lazy(() => import('modules/MedicationAssistedTreatment/Inventory'));
const MATReports = lazy(() => import('modules/MedicationAssistedTreatment/Reports'));
const PatientCenter = lazy(() => import('modules/MedicationAssistedTreatment/PatientCenter'));
const MATDashboard = lazy(() => import('modules/MedicationAssistedTreatment/Dashboard'));
const QueueCenter = lazy(() => import('modules/MedicationAssistedTreatment/QueueCenter'));

const AppointmentBySite = lazy(() => import('modules/AppointmentContent/BySite'));
const AppointmentHistory = lazy(() => import('modules/AppointmentContent/History'));
const TimeBlocked = lazy(() => import('modules/AppointmentContent/TimeBlock'));
const AIMS = lazy(() => import('modules/AppointmentContent/AIMS'));
const Compliance = lazy(() => import('modules/Dashboard/Compliance'));
const Enrollment = lazy(() => import('modules/Dashboard/Enrollment'));
const DynamicFormHistory = lazy(() => import('modules/DynamicForm/Content/History'));
const RecentForms = lazy(() => import('modules/DynamicForm/Content/Recent'));
const CybhiResponseFiles = lazy(() => import('modules/DynamicReport/CybhiResponseFiles'));

const DynamicProgressNoteHistory = lazy(() =>
  import('modules/ProgressNote/DynamicProgressNote/History'),
);
const DynamicProgressNoteViewer = lazy(() =>
  import('modules/ProgressNote/DynamicProgressNote/Viewer/Form'),
);
const DynamicProgressNoteViewerHistory = lazy(() =>
  import('modules/ProgressNote/DynamicProgressNote/Viewer/History'),
);
const DynamicProgressNoteViewerAutoSave = lazy(() =>
  import('modules/ProgressNote/DynamicProgressNote/Viewer/AutoSaveHistory'),
);

const DynamicServicePlanHistory = lazy(() => import('modules/DynamicServicePlan/History'));
const IntelligentAutomationHistory = lazy(() => import('modules/RPA/History'));
const RPAGreeting = lazy(() => import('modules/RPA/Greeting'));
const SDOHFormInput = lazy(() => import('modules/SDOH/SDOHFormInput'));
const SDOHHistory = lazy(() => import('modules/SDOH/History'));
const EMarHistory = lazy(() => import('modules/EMar/History'));
const EMarFormInput = lazy(() => import('modules/EMar/EMarFormInput'));
const LabHistory = lazy(() => import('modules/LabOrder/History'));
const LabOrderFormInput = lazy(() => import('modules/LabOrder/LabOrderFormInput'));
const ServicePlanHistory = lazy(() => import('modules/ServicePlanNew/History'));
const ServicePlanInput = lazy(() => import('modules/ServicePlanNew/Form'));
const ServicePlanAutoSave = lazy(() => import('modules/ServicePlanNew/AutoSaveHistory'));
const DynamicProgressNoteSelection = lazy(() =>
  import('modules/ProgressNote/DynamicProgressNote/Selection'),
);

const ReportingUpload = React.lazy(() => import('modules/Batch/ReportingUpload'));
const Batch837 = React.lazy(() => import('modules/Batch/Batch837InvoiceHCFA'));
const Reconcile277 = React.lazy(() => import('modules/Batch/Reconcile277'));
const Batch834 = React.lazy(() => import('modules/Batch/Batch834'));
const Encounters = React.lazy(() => import('modules/Batch/Encounters'));
const Reconcile835 = React.lazy(() => import('modules/Batch/Reconcile835'));
const FeeSchedule = React.lazy(() => import('modules/Batch/FeeSchedule'));
const PayorAssignment = React.lazy(() => import('modules/Batch/PayorAssignment'));
const RenderingProviders = React.lazy(() => import('modules/Batch/RenderingProviders'));
const Providers = React.lazy(() => import('modules/Batch/Providers'));
const PayorEditor = React.lazy(() => import('modules/Batch/PayorEditor'));
const RemitEOB = React.lazy(() => import('modules/Batch/RemitEOB'));
const RCMReport = React.lazy(() => import('modules/Batch/RCMReport'));

const SNCDPlanFormInput = lazy(() => import('modules/SNCDPlan/SNCDPlanFormInput'));
const SNCDHistory = lazy(() => import('modules/SNCDPlan/History'));

const SupportPlanFormInput = lazy(() => import('modules/SupportPlan/SupportPlanFormInput'));
const SupportPlanHistory = lazy(() => import('modules/SupportPlan/History'));

const PaymentHistorySection = lazy(() =>
  import('pages/ContentPage/PaymentManagement/PaymentHistorySection'),
);
const PaymentInput = lazy(() => import('pages/ContentPage/PaymentManagement/PaymentInput'));

const MetricPerformance = lazy(() => import('modules/Analytic/MetricPerformance'));
const ClinicalAnalytic = lazy(() => import('modules/Analytic/ClinicalAnalytic'));
const HeathPopulationAnalytic = lazy(() => import('modules/Analytic/HealthPopulationAnalytic'));
const AxiomInsight = lazy(() => import('modules/Analytic/AxiomInsight'));
const FinancialAnalytic = lazy(() => import('modules/Analytic/FinancialAnalytic'));

const DynamicFormViewerModule = lazy(() => import('pages/ContentPage/DynamicForm/ViewerModule'));
const PatientAssignmentGroup = lazy(() => import('modules/PatientAssignment/Group'));

const PatientAssignmentPharmacy = lazy(() => import('modules/PatientAssignment/Pharmacy'));
const PatientAssignmentPriority = lazy(() => import('modules/PatientAssignment/Priority'));
const PatientAssignmentSite = lazy(() => import('modules/PatientAssignment/Site'));
const PatientAssignmentTeam = lazy(() => import('modules/PatientAssignment/Team'));

const QuickFormContent = lazy(() => import('modules/QuickEnrollment/Form'));
const SearchPatientForm = lazy(() => import('modules/QuickEnrollment/SearchPatientForm'));
const EnrollmentPatientInfo = lazy(() => import('modules/QuickEnrollment/PatientInfo'));
const EnrollmentHistory = lazy(() => import('modules/QuickEnrollment/History'));

const EmployeeEnrollmentForm = lazy(() => import('modules/HREnrollment/EmployeeEnrollmentForm'));
const SearchEmployee = lazy(() => import('modules/HREnrollment/SearchEmployee'));

const AdditionalInfo = lazy(() => import('modules/CoverSheet/AdditionalInfo'));
const CoverSheetAddress = lazy(() => import('modules/CoverSheet/Address'));
const CoverSheetConsent = lazy(() => import('modules/CoverSheet/Consent'));
const CoverSheetContact = lazy(() => import('modules/CoverSheet/Contact'));
const CoverSheetInsurance = lazy(() => import('modules/CoverSheet/Insurance'));
const CoverSheetPatient = lazy(() => import('modules/CoverSheet/Patient'));
const CoverSheetPatientPortal = lazy(() => import('modules/CoverSheet/PatientPortal'));

const ProblemListHistoryCollapse = lazy(() =>
  import('modules/ProblemList/ProblemHistory/ProblemHistoryCollapse'),
);
const ProblemFormCollapse = lazy(() =>
  import('modules/ProblemList/ProblemForm/ProblemFormCollapse'),
);
const DiagnosisHistoryCollapse = lazy(() =>
  import('modules/ProblemList/DiagnosisHistory/DiagnosisHistoryCollapse'),
);
const ProgramHistoryCollapse = lazy(() =>
  import('modules/ProblemList/ProgramHistory/ProgramHistoryCollapse'),
);
const ProgramFormCollapse = lazy(() =>
  import('modules/ProblemList/ProgramForm/ProgramFormCollapse'),
);

const ScanDocumentHistory = lazy(() => import('modules/ScannedDocuments/History'));
const ScanDocumentForm = lazy(() => import('modules/ScannedDocuments/Form'));

const SpecialistReferralHistory = lazy(() => import('modules/SpecialistReferral/History'));
const SpecialistReferralForm = lazy(() => import('modules/SpecialistReferral/Form'));
const DailyExtract = lazy(() => import('modules/DailyExtract'));
const ServiceSite = lazy(() => import('modules/EncounterFormManagement/ServiceSite'));
const ServiceCode = lazy(() => import('modules/EncounterFormManagement/ServiceCode'));
const ServiceGrid = lazy(() => import('modules/EncounterFormManagement/ServiceGrid'));
const EncounterFormManagementProgram = lazy(() =>
  import('modules/EncounterFormManagement/Program'),
);
const FosterHomeSearchPatient = lazy(() => import('modules/FosterHome/SearchPatient'));
const CompleteFosterHomeRegister = lazy(() =>
  import('modules/FosterHome/CompleteFosterHomeRegistration'),
);
const EditFosterHome = lazy(() => import('modules/FosterHome/EditFosterHome'));
const ChildAssignmentFosterHome = lazy(() => import('modules/FosterHome/ChildAssignment'));

const GroupNoteForm = lazy(() => import('pages/ContentPage/ProgressNote/GroupNote/Form'));
const PsychNoteInput = lazy(() =>
  import('modules/ProgressNote/ProgressNoteContent/PsychNoteNew/PsychNoteInput'),
);
const ProgressNoteHistory = lazy(() => import('modules/ProgressNote/ProgressNoteContent/History'));
const ProgressNoteAutoSave = lazy(() =>
  import('modules/ProgressNote/ProgressNoteContent/AutoSaveHistory'),
);
const Contacts = lazy(() =>
  import('modules/ProgressNote/ProgressNoteContent/CMNote/components/Contacts'),
);
const Appointments = lazy(() =>
  import('modules/ProgressNote/ProgressNoteContent/CMNote/components/Appointments'),
);
const Groups = lazy(() =>
  import('modules/ProgressNote/ProgressNoteContent/CMNote/components/Groups'),
);
const PatientDemographics = lazy(() =>
  import('modules/ProgressNote/ProgressNoteContent/CMNote/components/PatientDemographics'),
);
const CurrentPrescriptions = lazy(() =>
  import('modules/ProgressNote/ProgressNoteContent/CMNote/components/CurrentPrescriptions'),
);

const DemographicEnrollment = lazy(() => import('modules/Demographic/Enrollment'));
const DemographicInput = lazy(() => import('modules/Demographic/Form'));
const DemographicHistory = lazy(() => import('modules/Demographic/History'));

const FosterCareCaseNote = lazy(() => import('modules/FosterHome/FosterCareCaseNote'));
const GroupEditorHistory = lazy(() => import('modules/GroupEditor/History'));
const GroupEditorForm = lazy(() => import('modules/GroupEditor/Form'));
const GroupEditorPatientHistory = lazy(() => import('modules/GroupEditor/Patient'));
const PatientInputForm = lazy(() => import('modules/GroupEditor/PatientInput'));

const ImmunizationHistory = lazy(() => import('modules/Immunization/ImmunizationHistory'));
const ImmunizationRegistrySetting = lazy(() =>
  import('modules/Immunization/ImmunizationRegistrySetting'),
);
const ImmunizationInputCollapse = lazy(() => import('modules/Immunization/ImmunizationInput'));
const ImmunizationTimeLineCollapse = lazy(() =>
  import('modules/Immunization/ImmunizationTimeLine/ImmunizationTimeLineCollapse'),
);

const AutoSaveDynamicForm = lazy(() => import('modules/Support/AutoSaveDynamicForm'));
const AutosaveHx = lazy(() => import('modules/Support/AutosaveHx'));
const EnrollRef = lazy(() => import('modules/Support/EnrollRef'));
const MenuManagement = lazy(() => import('modules/Support/MenuManagement'));
const NewAccount = lazy(() => import('modules/Support/NewAccount'));
const PageSecurities = lazy(() => import('modules/Support/PageSecurities'));
const Prescriptions = lazy(() => import('modules/Support/Prescriptions'));
const ProgressNotes = lazy(() => import('modules/Support/ProgressNotes'));
const ResetPassword = lazy(() => import('modules/Support/ResetPassword'));
const RightTransfer = lazy(() => import('modules/Support/RightTransfer'));
const SystemConfiguration = lazy(() => import('modules/Support/SystemConfiguration'));
const LookupTable = lazy(() => import('modules/Support/LookupTable'));
const ByPassResetMFA = lazy(() => import('modules/Support/ByPassResetMFA'));
const DiagnosisCodes = lazy(() => import('modules/Support/DiagnosisCodes'));
const ServicePlanSupport = lazy(() => import('modules/Support/ServicePlan'));
const SupportIbhis = lazy(() => import('modules/Support/IBHIS'));

const DataImportHistory = lazy(() => import('modules/DataImport/History'));
const DataImportRunsHistory = lazy(() => import('modules/DataImport/ImportRunsHistory'));
const TableList = lazy(() => import('modules/DataImport/TableList'));
const Upload = lazy(() => import('modules/DataImport/Upload'));
const Diagram = lazy(() => import('modules/DataImport/Diagram'));

const UserRolesManagement = lazy(() => import('modules/SpecificSecurity/UserRolesManagement'));
const RolesManagement = lazy(() => import('modules/SpecificSecurity/RolesManagement'));
const SensitivityClassification = lazy(() =>
  import('modules/SpecificSecurity/SensitivityClassification'),
);

const ServicePlanMappingFields = lazy(() =>
  import('modules/ServicePlanFieldManager/ServicePlanMappingFields'),
);
const ServicePlanMappingFieldInput = lazy(() =>
  import('modules/ServicePlanFieldManager/ServicePlanMappingFieldInput'),
);
const ServicePlanFieldInput = lazy(() =>
  import('modules/ServicePlanFieldManager/ServicePlanFieldInput'),
);

const ClosureEnrollmentHistory = lazy(() => import('modules/Closure/ClosureEnrollmentHistory'));
const ClosureHistory = lazy(() => import('modules/Closure/ClosureHistory'));
const ClosureExtend = lazy(() => import('modules/Closure/ClosureExtend'));

const ReportForm = lazy(() => import('modules/DynamicReport/Form'));
const RecentReports = lazy(() => import('modules/DynamicReport/RecentReports'));
const ReportModule = lazy(() => import('modules/DynamicReport/ReportModule'));

const ExamOptionsTypes = lazy(() => import('modules/ExamOptions/ExamOptionsTypes'));
const ExamOptionsEdit = lazy(() => import('modules/ExamOptions/ExamOptionsEdit'));
const ExamOptionsOptions = lazy(() => import('modules/ExamOptions/ExamOptionsOptions'));
const ExamOptionsOptionsEdit = lazy(() => import('modules/ExamOptions/ExamOptionsOptionsEdit'));

const ServicePlanServiceCodes = lazy(() =>
  import('modules/ServicePlanFieldManager/ServicePlanServiceCodes'),
);
const ServicePlanServiceCodeInput = lazy(() =>
  import('modules/ServicePlanFieldManager/ServicePlanServiceCodeInput'),
);

const ServicePlanFields = lazy(() => import('modules/ServicePlanFieldManager/ServicePlanFields'));
const ServicePlanSignatureInput = lazy(() =>
  import('modules/ServicePlanFieldManager/ServicePlanSignatureInput'),
);
const ServicePlanSignatureHistory = lazy(() =>
  import('modules/ServicePlanFieldManager/ServicePlanSignatureHistory'),
);

const ASAMHistory = lazy(() => import('modules/ASAM/ASAMHistory'));
const ASAMFormInput = lazy(() => import('modules/ASAM/ASAMFormInput'));

const RadiologyInput = lazy(() => import('modules/Radiology/Input'));
const RadiologyHistory = lazy(() => import('modules/Radiology/History'));

const MedicalRecordsReleases = lazy(() =>
  import('modules/HealthInformation/MedicalRecordsReleases'),
);
const Releases = lazy(() => import('modules/HealthInformation/Releases'));
const HealthInformationReleaseInput = lazy(() =>
  import('modules/HealthInformation/HealthInformationReleaseInput'),
);

const IncidentReportSearch = lazy(() => import('modules/QualityManagement/IncidentReportSearch'));
const IncidentReportInput = lazy(() => import('modules/QualityManagement/IncidentReportInput'));
const CGASearch = lazy(() => import('modules/QualityManagement/CGASearch'));
const CGAInput = lazy(() => import('modules/QualityManagement/CGAInput'));

const SearchCriteria = lazy(() => import('modules/ClinicalFormsSearch/SearchCriteria'));
const Encounter = lazy(() => import('modules/ClinicalFormsSearch/Encounter'));
const Employees = lazy(() => import('modules/ClinicalFormsSearch/Employees'));
const Patients = lazy(() => import('modules/ClinicalFormsSearch/Patients'));
const DynamicForms = lazy(() => import('modules/ClinicalFormsSearch/DynamicForms'));

const FosterHomeDashboard = lazy(() => import('modules/FosterHome/FosterHomeDashboard'));

const ReAssignment = lazy(() => import('modules/ReAssignment'));

const PsychEvaluationNoteInput = lazy(() =>
  import('modules/PsychEvaluationNote/PsychEvaluationNoteInput'),
);
const PsychotherapyProgressNoteInput = lazy(() =>
  import('modules/PsychtherapyProgressNote/PsychotherapyProgressNoteInput'),
);

const PrescriptionTimeLine = lazy(() => import('modules/Prescription/MainPage/Timeline'));
const RxHistory = lazy(() => import('modules/Prescription/MainPage/RxHistory'));
const CurrentRxTable = lazy(() =>
  import('modules/Prescription/MainPage/Medication/CurrentRxTable'),
);
const ExpiredRxTable = lazy(() =>
  import('modules/Prescription/MainPage/Medication/ExpiredRxTable'),
);
const OtherAgencyRxTable = lazy(() =>
  import('modules/Prescription/MainPage/Medication/OtherAgencyTable'),
);
const MedicationReconciliation = lazy(() =>
  import('modules/Prescription/MedicationReconciliation'),
);

const PayorAssignmentHistoryCollapse = lazy(() =>
  import('modules/PayorAssignment/PayorAssignmentHistory/PayorAssignmentHistoryCollapse'),
);
const PayorAssignmentInformationCollapseMain = lazy(() =>
  import(
    'modules/PayorAssignment/PayorAssignmentInformation/PayorAssignmentInformationCollapseMain'
  ),
);

const CRMDashboard = lazy(() => import('modules/CRM/Dashboard'));
const CRMClients = lazy(() => import('modules/CRM/Clients'));
const CRMAnalytics = lazy(() => import('modules/CRM/Analytics'));

const PsychosocialAssessmentNoteInput = lazy(() =>
  import('modules/PsychosocialAssessmentNote/PsychosocialAssessmentNoteInput'),
);

const InformedConsentHistory = lazy(() => import('modules/InformedConsent/InformedConsentHistory'));
const InformedConsentInfo = lazy(() => import('modules/InformedConsent/InformedConsentInfo'));

const CrisisPlanHistory = lazy(() => import('modules/CrisisPlan/History'));
const ChildHistory = lazy(() => import('modules/CrisisPlan/ChildHistory'));
const CrisisPlanFormInput = lazy(() => import('modules/CrisisPlan/CrisisPlanFormInput'));

const CrisisPreventionPlanHistory = lazy(() => import('modules/CrisisPreventionPlan/History'));
const CrisisPreventionPlanInput = lazy(() =>
  import('modules/CrisisPreventionPlan/CrisisPreventionPlanInput'),
);

const PCPCommunicationHeader = lazy(() =>
  import('modules/PcpCommunication/PCPCommunicationHeader'),
);
const PCPCommunicationInput = lazy(() => import('modules/PcpCommunication/PCPCommunicationInput'));
const PcpCommunicationHistory = lazy(() =>
  import('modules/PcpCommunication/PcpCommunicationHistory'),
);
const CurrentDiagnosis = lazy(() =>
  import('modules/PcpCommunication/PcpCommunicationHistory/CurrentDiagnosis'),
);
const ActiveMedications = lazy(() =>
  import('modules/PcpCommunication/PcpCommunicationHistory/ActiveMedications'),
);

const PHQAHistory = lazy(() => import('modules/PHQA/PHQAHistory'));
const PHQAForm = lazy(() => import('modules/PHQA/PHQAForm'));

const FundingSourceHistory = lazy(() => import('modules/FundingSource/FundingSourceHistory'));
const FundingSourceInput = lazy(() => import('modules/FundingSource/FundingSourceInput'));

const PHQHistory = lazy(() => import('modules/PHQ/PHQHistory'));
const PHQForm = lazy(() => import('modules/PHQ/PHQForm'));
const PHQChart = lazy(() => import('modules/PHQ/PHQChart'));

const VitalsHistory = lazy(() => import('modules/Vitals/VitalsHistory'));
const VitalsForm = lazy(() => import('modules/Vitals/VitalsForm'));
const VitalsChart = lazy(() => import('modules/Vitals/VitalsChart'));

const ACEHistory = lazy(() => import('modules/ACE/ACEHistory'));
const ACEForm = lazy(() => import('modules/ACE/ACEForm'));
const ACEChart = lazy(() => import('modules/ACE/ACEChart'));

const PCL5History = lazy(() => import('modules/PCL5/PCL5History'));
const PCL5Form = lazy(() => import('modules/PCL5/PCL5Form'));
const PCL5Chart = lazy(() => import('modules/PCL5/PCL5Chart'));

const GAD7History = lazy(() => import('modules/GAD7/GAD7History'));
const GAD7Form = lazy(() => import('modules/GAD7/GAD7Form'));
const GAD7Chart = lazy(() => import('modules/GAD7/GAD7Chart'));

const AuditHistory = lazy(() => import('modules/Audit/AuditHistory'));
const AuditForm = lazy(() => import('modules/Audit/AuditForm'));
const AuditChart = lazy(() => import('modules/Audit/AuditChart'));

const PCPTSDHistory = lazy(() => import('modules/PCPTSD/PCPTSDHistory'));
const PCPTSDForm = lazy(() => import('modules/PCPTSD/PCPTSDForm'));
const PCPTSDChart = lazy(() => import('modules/PCPTSD/PCPTSDChart'));

const COTHistory = lazy(() => import('modules/CourtOrderedTreatment/COTHistory'));
const COTForm = lazy(() => import('modules/CourtOrderedTreatment/COTForm'));
const COTAmendments = lazy(() => import('modules/CourtOrderedTreatment/COTAmendments'));
const COTJudicialReview = lazy(() => import('modules/CourtOrderedTreatment/COTJudicialReview'));
const COTStatusReport = lazy(() => import('modules/CourtOrderedTreatment/COTStatusReport'));

const CINAHistory = lazy(() => import('modules/CINA/CINAHistory'));
const CINAInput = lazy(() => import('modules/CINA/CINAForm'));

const CIWAHistory = lazy(() => import('modules/CIWA/CIWAHistory'));
const CIWAForm = lazy(() => import('modules/CIWA/CIWAForm'));

const SuicideSeverityRatingScaleHistory = lazy(() =>
  import('modules/SuicideSeverityRatingScale/SuicideSeverityRatingScaleHistory'),
);
const SuicideSeverityRatingScaleForm = lazy(() =>
  import('modules/SuicideSeverityRatingScale/SuicideSeverityRatingScaleForm'),
);

const PRAPAREHistory = lazy(() => import('modules/PRAPARE/PRAPAREHistory'));
const PRAPAREForm = lazy(() => import('modules/PRAPARE/PRAPAREForm'));

const COWSHistory = lazy(() => import('modules/COWS/COWSHistory'));
const COWSForm = lazy(() => import('modules/COWS/COWSForm'));

const InjectionHistoryWrapper = lazy(() => import('modules/Injection/History'));
const InjectionInput = lazy(() => import('modules/Injection/Form'));

const AllergyHistoryWrapper = lazy(() => import('modules/Allergy/History'));
const AllergyInput = lazy(() => import('modules/Allergy/Form'));

const EngagementHistory = lazy(() => import('modules/Engagement/EngagementHistory'));
const EngagementForm = lazy(() => import('modules/Engagement/EngagementForm'));

const AnnouncementsHistory = lazy(() => import('modules/Announcements/AnnouncementsHistory'));
const AnnouncementsForm = lazy(() => import('modules/Announcements/AnnouncementsForm'));

const FamilyHealthHistory = lazy(() => import('modules/FamilyHealth/FamilyHealthHistory'));
const FamilyHealthForm = lazy(() => import('modules/FamilyHealth/FamilyHealthForm'));

const CALOCUSHistory = lazy(() => import('modules/CALOCUS/CALOCUSHistory'));
const CALOCUSForm = lazy(() => import('modules/CALOCUS/CALOCUSForm'));
const CALOCUSLOC = lazy(() => import('modules/CALOCUS/CALOCUSLOC'));

const LOCUSHistory = lazy(() => import('modules/LOCUS/LOCUSHistory'));
const LOCUSForm = lazy(() => import('modules/LOCUS/LOCUSForm'));
const LOCUSLOC = lazy(() => import('modules/LOCUS/LOCUSLOC'));

const UpcomingVisit = lazy(() => import('modules/MedicationAssistedTreatment/UpcomingVisit'));
const PendingIntake = lazy(() => import('modules/MedicationAssistedTreatment/PendingIntake'));
const MonitorDisplayEditor = lazy(() =>
  import('modules/MedicationAssistedTreatment/MonitorDisplayEditor'),
);
const FacilitySchedule = lazy(() => import('modules/MedicationAssistedTreatment/FacilitySchedule'));
const AutoScheduleCarePlans = lazy(() =>
  import('modules/MedicationAssistedTreatment/AutoScheduleCarePlans'),
);

const ARTMeetingSummaries = lazy(() => import('modules/ART/ARTMeetingSummaries'));
const ARTStaffSummaries = lazy(() => import('modules/ART/ARTStaffSummaries'));
const ARTForm = lazy(() => import('modules/ART/ARTForm'));
const ARTAutoSave = lazy(() => import('modules/ART/ARTAutoSave'));

const DischargePlanHistory = lazy(() => import('modules/DischargePlan/DischargePlanHistory'));
const InputClinicalDischarge = lazy(() => import('modules/DischargePlan/InputClinicalDischarge'));

const LabResultsErrors = lazy(() => import('modules/LabResultsErrors/LabResultsErrors'));
const LabResultsErrorsLab = lazy(() => import('modules/LabResultsErrors/LabSearch'));
const LabResultsErrorsPatient = lazy(() => import('modules/LabResultsErrors/Patients'));

const ScheduleReasonList = lazy(() => import('modules/TimeBlockReason/ScheduleReasonList'));
const TimeBlockedReasonHistory = lazy(() =>
  import('modules/TimeBlockReason/TimeBlockedReasonHistory'),
);

const InternalOrdersEnrollment = lazy(() =>
  import('modules/InternalOrders/InternalOrdersEnrollment'),
);
const InternalOrdersHistory = lazy(() => import('modules/InternalOrders/InternalOrdersHistory'));
const InternalOrdersInput = lazy(() => import('modules/InternalOrders/InternalOrdersInput'));
const EncounterEntryInput = lazy(() => import('modules/EncounterEntry/EncounterEntryInput'));
const EncounterEntryPatient = lazy(() => import('modules/EncounterEntry/EncounterEntryPatient'));

const CommunicationsCenter = lazy(() => import('modules/CommunicationsCenter'));
const CMNoteInput = lazy(() =>
  import('modules/ProgressNote/ProgressNoteContent/CMNote/CMNoteInput'),
);

const PatientRoleRestriction = lazy(() =>
  import('modules/PatientEmployeeRestriction/PatientRoleRestriction'),
);

const PatientEmployeeRestriction = lazy(() =>
  import('modules/PatientEmployeeRestriction/PatientEmployeeRestriction'),
);

const CANS = lazy(() => import('modules/IBHIS/CANS'));
const PSC = lazy(() => import('modules/IBHIS/PSC'));
const LOCUS = lazy(() => import('modules/IBHIS/LOCUS'));
const ClientData = lazy(() => import('modules/IBHIS/ClientData'));
const ServiceRequest = lazy(() => import('modules/IBHIS/ServiceRequest'));
const Reports = lazy(() => import('modules/IBHIS/Reports'));
const SearchClientRecord = lazy(() => import('modules/IBHIS/SearchClientRecord'));
const SyncHistory = lazy(() => import('modules/IBHIS/SyncHistory'));
const UpdateAxiomClient = lazy(() => import('modules/IBHIS/UpdateAxiomClient'));
const CSIAssessment = lazy(() => import('modules/IBHIS/CSIAssessment'));

const CybhiClientData = lazy(() => import('modules/CYBHI/ClientData'));

const PendingPatientRequestsHistory = lazy(() => import('modules/PendingPatientRequests/History'));

const NurseProgressNoteInput = lazy(() =>
  import('modules/ProgressNote/ProgressNoteContent/NurseProgressNote/NurseProgressNoteInput'),
);

const InpatientNoteInput = lazy(() => import('modules/InpatientNote/InpatientNoteInput'));

const PlacementHistory = lazy(() => import('modules/Placement/PlacementHistory'));
const PlacementForm = lazy(() => import('modules/Placement/PlacementForm'));

const ImplantableDeviceHistory = lazy(() =>
  import('modules/ImplantableDevices/ImplantableDeviceHistory'),
);
const ImplantableDeviceEntry = lazy(() =>
  import('modules/ImplantableDevices/ImplantableDeviceEntry'),
);

const PatientProcedureHistory = lazy(() =>
  import('modules/PatientProcedure/PatientProcedureHistory'),
);

const PatientProcedureInput = lazy(() => import('modules/PatientProcedure/PatientProcedureInput'));

const PCPNoteInput = lazy(() =>
  import('modules/ProgressNote/ProgressNoteContent/PCPNoteHookForm/PCPNoteInput'),
);

const WHOAssistHistory = lazy(() => import('modules/WHOAssist/WHOAssistHistory'));
const WHOAssistForm = lazy(() => import('modules/WHOAssist/WHOAssistForm'));

const DirectMessages = lazy(() => import('modules/DirectMessaging/DirectMessages'));
const AddNewMessage = lazy(() => import('modules/DirectMessaging/AddNewMessage'));
const SuicideRiskScreeningFormInput = lazy(() =>
  import('modules/SuicideRiskScreening/SuicideRiskScreeningFormInput'),
);
const SuicideRiskScreeningHistory = lazy(() => import('modules/SuicideRiskScreening/History'));

const SuicideAssessmentFiveStepEvaluationAndTriageHistory = lazy(() =>
  import('modules/Safef/SuicideAssessmentFiveStepEvaluationAndTriageHistory'),
);
const SuicideAssessmentFiveStepEvaluationAndTriageInput = lazy(() =>
  import('modules/Safef/SuicideAssessmentFiveStepEvaluationAndTriageInput'),
);

const moduleMapping = [
  {
    rootPage: ROUTE_NAME.APPOINTMENT,
    key: MODULE_NAME.APPOINTMENT_HISTORY,
    text: t('Appointment History'),
    Component: AppointmentHistory,
  },
  {
    rootPage: ROUTE_NAME.APPOINTMENT,
    key: MODULE_NAME.APPOINTMENT_BY_SITE,
    text: t('Appointment By Site'),
    Component: AppointmentBySite,
  },
  { rootPage: ROUTE_NAME.APPOINTMENT, key: MODULE_NAME.AIMS, text: t('AIMS'), Component: AIMS },
  {
    rootPage: ROUTE_NAME.APPOINTMENT,
    key: MODULE_NAME.TIME_BLOCKED_HISTORY,
    text: t('Time Blocked History'),
    Component: TimeBlocked,
  },
  {
    rootPage: ROUTE_NAME.ASAM,
    key: MODULE_NAME.ASAM_HISTORY,
    text: t('ASAM History'),
    icon: 'assets/images/modern-history.png',
    Component: ASAMHistory,
  },
  {
    rootPage: ROUTE_NAME.ASAM,
    key: MODULE_NAME.ASAM_INPUT,
    text: t('ASAM Input'),
    icon: 'assets/images/modern-edit.png',
    Component: ASAMFormInput,
  },
  {
    rootPage: ROUTE_NAME.CRISIS_PLAN,
    key: MODULE_NAME.CRISIS_HISTORY,
    text: t('Crisis Plan History'),
    icon: 'assets/images/modern-history.png',
    Component: CrisisPlanHistory,
  },
  {
    rootPage: ROUTE_NAME.CRISIS_PLAN,
    key: MODULE_NAME.CRISIS_CHILD_HISTORY,
    text: t('Child Crisis Plan History'),
    icon: 'assets/images/modern-history.png',
    Component: ChildHistory,
  },
  {
    rootPage: ROUTE_NAME.CRISIS_PLAN,
    key: MODULE_NAME.CRISIS_INPUT,
    text: t('Crisis Plan Input'),
    icon: 'assets/images/modern-edit.png',
    Component: CrisisPlanFormInput,
  },
  {
    rootPage: ROUTE_NAME.CRISIS_PREVENTION_PLAN,
    key: MODULE_NAME.CRISIS_PREVENTION_HISTORY,
    text: t('Crisis Prevention Plan History'),
    icon: 'assets/images/modern-history.png',
    Component: CrisisPreventionPlanHistory,
  },
  {
    rootPage: ROUTE_NAME.CRISIS_PREVENTION_PLAN,
    key: MODULE_NAME.CRISIS_PREVENTION_INPUT,
    text: t('Crisis Plan Input'),
    icon: 'assets/images/modern-edit.png',
    Component: CrisisPreventionPlanInput,
  },
  {
    rootPage: ROUTE_NAME.SNCD,
    key: MODULE_NAME.SNCD_HISTORY,
    text: t('SNCD History'),
    icon: 'assets/images/modern-history.png',
    Component: SNCDHistory,
  },
  {
    rootPage: ROUTE_NAME.SNCD,
    key: MODULE_NAME.SNCD_INPUT,
    text: t('Add/Edit SNCD'),
    icon: 'assets/images/modern-edit.png',
    Component: SNCDPlanFormInput,
  },
  {
    rootPage: ROUTE_NAME.SUPPORT_PLAN,
    key: MODULE_NAME.SUPPORT_PLAN_HISTORY,
    text: t('Support Plan History'),
    icon: 'assets/images/modern-history.png',
    Component: SupportPlanHistory,
  },
  {
    rootPage: ROUTE_NAME.SUPPORT_PLAN,
    key: MODULE_NAME.SUPPORT_PLAN_INPUT,
    text: t('Support Plan Input'),
    icon: 'assets/images/modern-edit.png',
    Component: SupportPlanFormInput,
  },
  {
    rootPage: ROUTE_NAME.DASHBOARD,
    key: MODULE_NAME.DASHBOARD_COMPLIANCE,
    text: t('Compliance Chart'),
    icon: 'assets/images/crm-chart.png',
    Component: Compliance,
  },
  {
    rootPage: ROUTE_NAME.DASHBOARD,
    key: MODULE_NAME.DASHBOARD_ENROLLMENT,
    text: t('Grid'),
    icon: 'assets/images/encounter-grid.png',
    Component: Enrollment,
  },
  {
    rootPage: ROUTE_NAME.DYNAMIC_FORM,
    key: MODULE_NAME.DF_RECENT_FORMS,
    text: t('Recent Form'),
    Component: RecentForms,
  },
  {
    rootPage: ROUTE_NAME.DYNAMIC_FORM,
    key: MODULE_NAME.DF_HISTORY,
    text: t('Dynamic Form History'),
    Component: DynamicFormHistory,
  },
  {
    rootPage: ROUTE_NAME.DYNAMIC_FORM_VIEWER,
    key: MODULE_NAME.DF_VIEWER,
    text: t('Dynamic Form Viewer'),
    Component: DynamicFormViewerModule,
  },
  {
    rootPage: ROUTE_NAME.DYNAMIC_SERVICE_PLAN,
    key: MODULE_NAME.DYNAMIC_SERVICE_PLAN_HISTORY,
    text: t('Dynamic Service Plan History'),
    Component: DynamicServicePlanHistory,
  },
  // { key: MODULE_NAME.DYNAMIC_SERVICE_PLAN_AUTO_SAVE, Component: DynamicServicePlanAutoSave },
  {
    rootPage: ROUTE_NAME.PSYCH_NOTE,
    key: MODULE_NAME.PROGRESS_NOTE_PSYCH,
    text: t('Psych Note Input'),
    icon: 'assets/images/modern-edit.png',
    Component: PsychNoteInput,
  },
  {
    rootPage: ROUTE_NAME.PCP_NOTE,
    key: MODULE_NAME.PROGRESS_NOTE_PCP,
    text: t('PCP Note'),
    Component: PCPNoteInput,
  },
  {
    rootPage: ROUTE_NAME.GROUP_NOTE,
    key: MODULE_NAME.PROGRESS_NOTE_GROUP,
    text: t('Group Note'),
    Component: GroupNoteForm,
    icon: 'assets/images/modern-edit.png',
  },
  {
    rootPage: ROUTE_NAME.NOTE,
    key: MODULE_NAME.PROGRESS_NOTE_CM,
    text: t('CM Note'),
    Component: CMNoteInput,
  },
  {
    rootPage: ROUTE_NAME.NOTE,
    key: MODULE_NAME.PROGRESS_NOTE_AUTO_SAVE,
    text: t('Progress Note Auto-Save'),
    Component: ProgressNoteAutoSave,
    icon: 'assets/images/modern-save.png',
  },
  {
    rootPage: ROUTE_NAME.NOTE,
    key: MODULE_NAME.PROGRESS_NOTE_HISTORY,
    text: t('Progress Note History'),
    Component: ProgressNoteHistory,
    icon: 'assets/images/modern-history.png',
  },
  {
    rootPage: ROUTE_NAME.CM_NOTE,
    key: MODULE_NAME.CM3_CONTACT,
    text: t('Contacts'),
    Component: Contacts,
  },
  {
    rootPage: ROUTE_NAME.CM_NOTE,
    key: MODULE_NAME.CM3_APPOINTMENT,
    text: t('Appointments'),
    Component: Appointments,
  },
  {
    rootPage: ROUTE_NAME.CM_NOTE,
    key: MODULE_NAME.CM3_PRESCIPTION,
    text: t('Current Prescriptions'),
    Component: CurrentPrescriptions,
  },
  {
    rootPage: ROUTE_NAME.CM_NOTE,
    key: MODULE_NAME.CM3_GROUP,
    text: t('Groups'),
    Component: Groups,
  },
  {
    rootPage: ROUTE_NAME.CM_NOTE,
    key: MODULE_NAME.CM3_DEMOGRAPHIC,
    text: t('Patient Demographics'),
    Component: PatientDemographics,
  },
  {
    rootPage: ROUTE_NAME.DYNAMIC_PROGRESS_NOTE,
    key: MODULE_NAME.DYNAMIC_PROGRESS_NOTE_SELECTION,
    text: t('Dynamic Progress Note Selection'),
    Component: DynamicProgressNoteSelection,
  },
  {
    rootPage: ROUTE_NAME.DYNAMIC_PROGRESS_NOTE,
    key: MODULE_NAME.DYNAMIC_PROGRESS_NOTE_HISTORY,
    text: t('Dynamic Progress Note History'),
    Component: DynamicProgressNoteHistory,
  },
  {
    rootPage: ROUTE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER,
    key: MODULE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER,
    text: t('Dynamic Progress Note Input'),
    Component: DynamicProgressNoteViewer,
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER,
    key: MODULE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER_HISTORY,
    text: t('Dynamic Progress Note History'),
    Component: DynamicProgressNoteViewerHistory,
    depends: MODULE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER,
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER,
    key: MODULE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER_AUTO_SAVE,
    text: t('Dynamic Progress Note Auto Save'),
    Component: DynamicProgressNoteViewerAutoSave,
    depends: MODULE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER,
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.RPA,
    key: MODULE_NAME.RPA_RECORD,
    text: t('What task would you like to automate'),
    Component: RPAGreeting,
  },
  {
    rootPage: ROUTE_NAME.RPA,
    key: MODULE_NAME.RPA_HISTORY,
    text: t('Automation History'),
    Component: IntelligentAutomationHistory,
  },
  {
    rootPage: ROUTE_NAME.SDOH,
    key: MODULE_NAME.SDOH_HISTORY,
    text: t('SDOH History'),
    Component: SDOHHistory,
    icon: 'assets/images/modern-history.png',
  },
  {
    rootPage: ROUTE_NAME.SDOH,
    key: MODULE_NAME.SDOH_INPUT,
    text: t('SDOH Input'),
    Component: SDOHFormInput,
    icon: 'assets/images/modern-edit.png',
  },
  {
    rootPage: ROUTE_NAME.LAB_ORDER,
    key: MODULE_NAME.LAB_HISTORY,
    text: t('Lab History'),
    icon: 'assets/images/modern-history.png',
    Component: LabHistory,
  },
  {
    rootPage: ROUTE_NAME.LAB_ORDER,
    key: MODULE_NAME.LAB_INPUT,
    text: t('Lab Order Input'),
    icon: 'assets/images/modern-edit.png',
    Component: LabOrderFormInput,
  },
  {
    rootPage: ROUTE_NAME.E_MAR,
    key: MODULE_NAME.EMAR_HISTORY,
    text: t('Allergy History'),
    Component: EMarHistory,
  },
  {
    rootPage: ROUTE_NAME.E_MAR,
    key: MODULE_NAME.EMAR_INPUT,
    text: t('Medication Administration Record Form'),
    Component: EMarFormInput,
  },
  {
    rootPage: ROUTE_NAME.SERVICE_PLAN,
    key: MODULE_NAME.SERVICE_PLAN_HISTORY,
    text: t('Service Plan History'),
    Component: ServicePlanHistory,
  },
  {
    rootPage: ROUTE_NAME.SERVICE_PLAN,
    key: MODULE_NAME.SERVICE_PLAN_AUTO_SAVE,
    text: t('Service Plan Auto Save'),
    Component: ServicePlanAutoSave,
  },
  {
    rootPage: ROUTE_NAME.SERVICE_PLAN,
    key: MODULE_NAME.SERVICE_PLAN_INPUT,
    text: t('Service Plan Input'),
    Component: ServicePlanInput,
  },
  {
    rootPage: ROUTE_NAME.PCP_COMMUNICATION,
    key: MODULE_NAME.PCP_COMMUNICATION_HISTORY,
    text: t('Communication History'),
    Component: PcpCommunicationHistory,
  },
  {
    rootPage: ROUTE_NAME.PCP_COMMUNICATION,
    key: MODULE_NAME.PCP_COMMUNICATION_INPUT,
    text: t('Communication Input'),
    Component: PCPCommunicationInput,
  },
  {
    rootPage: ROUTE_NAME.PCP_COMMUNICATION,
    key: MODULE_NAME.PCP_COMMUNICATION_DIAGNOSIS,
    text: t('Current Diagnosis'),
    Component: CurrentDiagnosis,
  },
  {
    rootPage: ROUTE_NAME.PCP_COMMUNICATION,
    key: MODULE_NAME.PCP_COMMUNICATION_MEDICATIONS,
    text: t('Active Medications'),
    Component: ActiveMedications,
  },
  {
    rootPage: ROUTE_NAME.PCP_COMMUNICATION,
    key: MODULE_NAME.PCP_COMMUNICATION_HEADER,
    text: t('Communication Header'),
    Component: PCPCommunicationHeader,
  },
  // Batch
  {
    rootPage: ROUTE_NAME.BATCH,
    key: MODULE_NAME.BATCH_837,
    text: t('Batch 837 - Invoice - HCFA'),
    Component: Batch837,
  },
  {
    rootPage: ROUTE_NAME.BATCH,
    key: MODULE_NAME.BATCH_834,
    text: t('Batch 834'),
    Component: Batch834,
  },
  {
    rootPage: ROUTE_NAME.BATCH,
    key: MODULE_NAME.ENCOUNTERS,
    text: t('Encounters'),
    Component: Encounters,
  },
  {
    rootPage: ROUTE_NAME.BATCH,
    key: MODULE_NAME.RECONCILE_277,
    text: t('Reconcile 277'),
    Component: Reconcile277,
  },
  {
    rootPage: ROUTE_NAME.BATCH,
    key: MODULE_NAME.RECONCILE_835,
    text: t('Reconcile 835/EOB'),
    Component: Reconcile835,
  },
  {
    rootPage: ROUTE_NAME.BATCH,
    key: MODULE_NAME.REPORTING_UPLOAD,
    text: t('Reporting Upload'),
    Component: ReportingUpload,
  },
  {
    rootPage: ROUTE_NAME.BATCH,
    key: MODULE_NAME.FEE_SCHEDULE,
    text: t('Fee Schedule'),
    Component: FeeSchedule,
  },
  {
    rootPage: ROUTE_NAME.BATCH,
    key: MODULE_NAME.PAYOR_ASSIGNMENT,
    text: t('Payor Assignment'),
    Component: PayorAssignment,
  },
  {
    rootPage: ROUTE_NAME.BATCH,
    key: MODULE_NAME.RENDERING_PROVIDERS,
    text: t('Rendering Providers'),
    Component: RenderingProviders,
  },
  {
    rootPage: ROUTE_NAME.BATCH,
    key: MODULE_NAME.PROVIDERS,
    text: t('Providers'),
    Component: Providers,
  },
  {
    rootPage: ROUTE_NAME.BATCH,
    key: MODULE_NAME.PAYOR_EDITOR,
    text: t('Payors Editor'),
    Component: PayorEditor,
  },
  {
    rootPage: ROUTE_NAME.BATCH,
    key: MODULE_NAME.REMIT_EOB,
    text: t('Remit EOB'),
    Component: RemitEOB,
  },
  {
    rootPage: ROUTE_NAME.BATCH,
    key: MODULE_NAME.RCM_REPORT,
    text: t('RCM Report'),
    Component: RCMReport,
  },
  {
    rootPage: ROUTE_NAME.DYNAMIC_REPORT,
    key: MODULE_NAME.RECENT_REPORTS,
    text: t('Recent Reports'),
    Component: RecentReports,
    icon: 'assets/images/recent-reports.png',
  },
  {
    rootPage: ROUTE_NAME.DYNAMIC_REPORT,
    key: MODULE_NAME.RESPONSE_FILES,
    text: t('Carelon Response Files'),
    Component: CybhiResponseFiles,
    icon: 'assets/images/recent-reports.png',
  },
  {
    rootPage: ROUTE_NAME.DYNAMIC_REPORT,
    key: MODULE_NAME.REPORT_MODULE,
    text: t('Report Module'),
    Component: ReportModule,
    icon: 'assets/images/modern-history.png',
  },
  {
    rootPage: ROUTE_NAME.DYNAMIC_REPORT,
    key: MODULE_NAME.DYNAMIC_REPORT_FORM,
    text: t('Dynamic Report Builder'),
    Component: ReportForm,
    icon: 'assets/images/new-dynamic-reports.png',
  },
  {
    rootPage: ROUTE_NAME.PAYMENT_MANAGEMENT,
    key: MODULE_NAME.PAYMENT_HISTORY,
    text: t('Payment History'),
    icon: 'assets/images/modern-history.png',
    Component: PaymentHistorySection,
  },
  {
    rootPage: ROUTE_NAME.PAYMENT_MANAGEMENT,
    key: MODULE_NAME.PAYMENT_INPUT,
    text: t('Payment Input'),
    icon: 'assets/images/modern-edit.png',
    Component: PaymentInput,
  },
  //Analytic
  {
    rootPage: ROUTE_NAME.ANALYTIC,
    key: MODULE_NAME.METRIC_PERFORMANCE,
    text: t('Value-Based Partnering Quality Metric Performance Scorecard'),
    Component: MetricPerformance,
  },
  {
    rootPage: ROUTE_NAME.ANALYTIC,
    key: MODULE_NAME.CLINICAL_ANALYTIC,
    text: t('Clinical Analytics'),
    Component: ClinicalAnalytic,
  },
  {
    rootPage: ROUTE_NAME.ANALYTIC,
    key: MODULE_NAME.HEALTH_POPULATION_ANALYTIC,
    text: t('Health Population Analytics'),
    Component: HeathPopulationAnalytic,
  },
  {
    rootPage: ROUTE_NAME.ANALYTIC,
    key: MODULE_NAME.AXIOM_INSIGHT,
    text: t('Insights'),
    Component: AxiomInsight,
  },
  {
    rootPage: ROUTE_NAME.ANALYTIC,
    key: MODULE_NAME.FINANCIAL_ANALYTIC,
    text: t('Financial Analytics'),
    Component: FinancialAnalytic,
  },
  {
    rootPage: ROUTE_NAME.PATIENT_ASSIGNMENT,
    key: MODULE_NAME.PATIENT_ASSIGNMENT_GROUP,
    text: t('Group'),
    Component: PatientAssignmentGroup,
    icon: 'assets/images/patient-assignment-group.png',
  },
  {
    rootPage: ROUTE_NAME.PATIENT_ASSIGNMENT,
    key: MODULE_NAME.PATIENT_ASSIGNMENT_PHARMACY,
    text: t('Pharmacy'),
    Component: PatientAssignmentPharmacy,
    icon: 'assets/images/patient-assignment-pcp.png',
  },
  {
    rootPage: ROUTE_NAME.PATIENT_ASSIGNMENT,
    key: MODULE_NAME.PATIENT_ASSIGNMENT_PRIORITY,
    text: t('Priority'),
    Component: PatientAssignmentPriority,
    icon: 'assets/images/patient-assignment-priority.png',
  },
  {
    rootPage: ROUTE_NAME.PATIENT_ASSIGNMENT,
    key: MODULE_NAME.PATIENT_ASSIGNMENT_SITE,
    text: t('Site'),
    Component: PatientAssignmentSite,
    icon: 'assets/images/patient-assignment-site.png',
  },
  {
    rootPage: ROUTE_NAME.PATIENT_ASSIGNMENT,
    key: MODULE_NAME.PATIENT_ASSIGNMENT_TEAM,
    text: t('Team'),
    Component: PatientAssignmentTeam,
    icon: 'assets/images/patient-assignment-team.png',
  },
  {
    rootPage: ROUTE_NAME.QUICK_ENROLLMENT,
    key: MODULE_NAME.QUICK_ENROLLMENT_SEARCH,
    text: t('Search Enrollment Patient'),
    Component: SearchPatientForm,
    icon: 'assets/images/modern-search.png',
  },
  {
    rootPage: ROUTE_NAME.QUICK_ENROLLMENT,
    key: MODULE_NAME.QUICK_ENROLLMENT_INPUT,
    text: t('Enrollment Form'),
    Component: QuickFormContent,
    icon: 'assets/images/modern-add.png',
    depends: MODULE_NAME.QUICK_ENROLLMENT_SEARCH,
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.HR,
    key: MODULE_NAME.HR_ENROLLMENT_SEARCH,
    text: t('Search Employee Enrollment'),
    Component: SearchEmployee,
    icon: 'assets/images/modern-search.png',
  },
  {
    rootPage: ROUTE_NAME.HR,
    key: MODULE_NAME.HR_ENROLLMENT_FORM,
    text: t('Employee Form'),
    Component: EmployeeEnrollmentForm,
    depends: MODULE_NAME.HR_ENROLLMENT_SEARCH,
    defaultShow: false,
    icon: 'assets/images/modern-add.png',
  },
  {
    rootPage: ROUTE_NAME.COVER_SHEET,
    key: MODULE_NAME.COVER_SHEET_PATIENT,
    text: t('Client'),
    Component: CoverSheetPatient,
    icon: 'assets/images/avatar.png',
  },
  {
    rootPage: ROUTE_NAME.COVER_SHEET,
    key: MODULE_NAME.COVER_SHEET_ADDRESS,
    text: t('Contact Information'),
    Component: CoverSheetAddress,
    icon: 'assets/images/patient-assignment-site.png',
  },
  {
    rootPage: ROUTE_NAME.COVER_SHEET,
    key: MODULE_NAME.COVER_SHEET_CONTACT,
    text: t('Support Contacts'),
    Component: CoverSheetContact,
    icon: 'assets/images/coversheet-contact.png',
  },
  {
    rootPage: ROUTE_NAME.COVER_SHEET,
    key: MODULE_NAME.COVER_SHEET_ADDITIONAL_INFO,
    text: t('Additional Info'),
    Component: AdditionalInfo,
    icon: 'assets/images/coversheet-additional-info.png',
  },
  {
    rootPage: ROUTE_NAME.COVER_SHEET,
    key: MODULE_NAME.COVER_SHEET_CONSENT,
    text: t('Consent'),
    Component: CoverSheetConsent,
    icon: 'assets/images/coversheet-consent.png',
  },
  {
    rootPage: ROUTE_NAME.COVER_SHEET,
    key: MODULE_NAME.COVER_SHEET_PATIENT_PORTAL,
    text: t('Patient Portal'),
    Component: CoverSheetPatientPortal,
    icon: 'assets/images/coversheet-patient-portal.png',
  },
  {
    rootPage: ROUTE_NAME.COVER_SHEET,
    key: MODULE_NAME.COVER_SHEET_INSURANCE,
    text: t('Insurance'),
    Component: CoverSheetInsurance,
    icon: 'assets/images/coversheet-insurance.png',
  },
  {
    rootPage: ROUTE_NAME.PROBLEM_LIST,
    key: MODULE_NAME.PROBLEM_LIST_HISTORY,
    text: t('Problems List History'),
    Component: ProblemListHistoryCollapse,
  },
  {
    rootPage: ROUTE_NAME.PROBLEM_LIST,
    key: MODULE_NAME.PROBLEM_INPUT,
    text: t('Problems List Input'),
    Component: ProblemFormCollapse,
  },
  {
    rootPage: ROUTE_NAME.PROBLEM_LIST,
    key: MODULE_NAME.BEHAVIORAL_DIAGNOSIS_HISTORY,
    text: t('Behavioral Diagnosis History'),
    Component: DiagnosisHistoryCollapse,
  },
  {
    rootPage: ROUTE_NAME.ADMIN_SPECIFIC_SECURITY,
    key: MODULE_NAME.SECURITY_USER_ROLES_MANAGEMENT,
    text: t('Users Management'),
    Component: UserRolesManagement,
  },
  {
    rootPage: ROUTE_NAME.ADMIN_SPECIFIC_SECURITY,
    key: MODULE_NAME.SECURITY_ROLES_MANAGEMENT,
    text: t('Roles Management'),
    Component: RolesManagement,
  },
  {
    rootPage: ROUTE_NAME.ADMIN_SPECIFIC_SECURITY,
    key: MODULE_NAME.SECURITY_SENSITIVITY_CLASSIFICATION,
    text: t('Sensitivity Classification'),
    Component: SensitivityClassification,
  },
  {
    rootPage: ROUTE_NAME.PROBLEM_LIST,
    key: MODULE_NAME.PROGRAM_HISTORY,
    text: t('Program History'),
    Component: ProgramHistoryCollapse,
  },
  {
    rootPage: ROUTE_NAME.PROBLEM_LIST,
    key: MODULE_NAME.PROGRAM_INPUT,
    text: t('Program Input'),
    Component: ProgramFormCollapse,
  },
  {
    rootPage: ROUTE_NAME.SCANNED_DOCUMENTS,
    key: MODULE_NAME.SCAN_DOCUMENTS_HISTORY,
    text: t('Scanned Documents History'),
    icon: 'assets/images/modern-history.png',
    Component: ScanDocumentHistory,
  },
  {
    rootPage: ROUTE_NAME.SCANNED_DOCUMENTS,
    key: MODULE_NAME.SCAN_DOCUMENTS_INPUT,
    text: t('Scanned Documents Input'),
    icon: 'assets/images/modern-edit.png',
    Component: ScanDocumentForm,
  },
  {
    rootPage: ROUTE_NAME.ENROLLMENT_HISTORY,
    key: MODULE_NAME.PATIENT_ENROLLMENT_INFO,
    text: t('Patient Info'),
    Component: EnrollmentPatientInfo,
  },
  {
    rootPage: ROUTE_NAME.ENROLLMENT_HISTORY,
    key: MODULE_NAME.ENROLLMENT_HISTORY,
    text: t('Enrollment History'),
    Component: EnrollmentHistory,
    depends: MODULE_NAME.PATIENT_ENROLLMENT_INFO,
  },
  {
    rootPage: ROUTE_NAME.SPECIALIST_REFERRAL,
    key: MODULE_NAME.SPECIALIST_REFERRAL_HISTORY,
    text: t('Specialist Referral History'),
    Component: SpecialistReferralHistory,
  },
  {
    rootPage: ROUTE_NAME.SPECIALIST_REFERRAL,
    key: MODULE_NAME.SPECIALIST_REFERRAL_INPUT,
    text: t('Specialist Referral Input'),
    Component: SpecialistReferralForm,
  },
  {
    rootPage: ROUTE_NAME.DAILY_EXTRACT,
    key: MODULE_NAME.DAILY_EXTRACT,
    text: t('Daily Extract'),
    Component: DailyExtract,
  },
  {
    rootPage: ROUTE_NAME.ENCOUNTER_FORM_MANAGEMENT,
    key: MODULE_NAME.ENCOUNTER_FORM_MANAGEMENT_SITE,
    text: t('Encounter Form Service Site'),
    Component: ServiceSite,
    icon: 'assets/images/encounter-site.png',
  },
  {
    rootPage: ROUTE_NAME.ENCOUNTER_FORM_MANAGEMENT,
    key: MODULE_NAME.ENCOUNTER_FORM_MANAGEMENT_CODE,
    text: t('Encounter Form Service Code'),
    Component: ServiceCode,
    icon: 'assets/images/encounter-code.png',
  },
  {
    rootPage: ROUTE_NAME.ENCOUNTER_FORM_MANAGEMENT,
    key: MODULE_NAME.ENCOUNTER_FORM_MANAGEMENT_GRID,
    text: t('Encounter Form Service Grid Category'),
    icon: 'assets/images/encounter-grid.png',
    Component: ServiceGrid,
  },
  {
    rootPage: ROUTE_NAME.ENCOUNTER_FORM_MANAGEMENT,
    key: MODULE_NAME.ENCOUNTER_FORM_MANAGEMENT_PROGRAM,
    text: t('Encounter Form Program'),
    // icon: 'assets/images/encounter-program.png',
    Component: EncounterFormManagementProgram,
  },
  {
    rootPage: ROUTE_NAME.FOSTER_HOME,
    key: MODULE_NAME.FOSTER_HOME_SEARCH,
    text: t('Foster Home Search'),
    icon: 'assets/images/modern-search.png',
    Component: FosterHomeSearchPatient,
  },
  {
    rootPage: ROUTE_NAME.FOSTER_HOME,
    key: MODULE_NAME.FOSTER_HOME_REGISTER,
    text: t('Complete Foster Home Registration'),
    icon: 'assets/images/modern-edit.png',
    Component: CompleteFosterHomeRegister,
    depends: MODULE_NAME.FOSTER_HOME_SEARCH,
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.FOSTER_HOME,
    key: MODULE_NAME.FOSTER_HOME_EDIT,
    text: t('Edit Foster Home'),
    icon: 'assets/images/modern-edit.png',
    Component: EditFosterHome,
    depends: MODULE_NAME.FOSTER_HOME_SEARCH,
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.FOSTER_HOME,
    key: MODULE_NAME.FOSTER_CHILD_ASSIGNMENT,
    text: t('Child Assignment'),
    icon: 'assets/images/modern-edit.png',
    Component: ChildAssignmentFosterHome,
    depends: MODULE_NAME.FOSTER_HOME_SEARCH,
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.FOSTER_HOME,
    key: MODULE_NAME.FOSTER_CARE_CASE_NOTE,
    text: t('Foster Care Case Note'),
    icon: 'assets/images/foster-home-case-note.png',
    Component: FosterCareCaseNote,
    depends: MODULE_NAME.FOSTER_CHILD_ASSIGNMENT,
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.FOSTER_HOME,
    key: MODULE_NAME.FOSTER_HOME_DASHBOARD,
    text: t('Foster Home Dashboard'),
    icon: 'assets/images/menu-management.png',
    Component: FosterHomeDashboard,
    depends: MODULE_NAME.FOSTER_HOME_SEARCH,
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.DEMOGRAPHIC,
    key: MODULE_NAME.DEMOGRAPHIC_HISTORY,
    text: t('History'),
    Component: DemographicHistory,
  },
  {
    rootPage: ROUTE_NAME.DEMOGRAPHIC,
    key: MODULE_NAME.DEMOGRAPHIC_ENROLLMENT,
    text: t('Enrollment'),
    Component: DemographicEnrollment,
  },
  {
    rootPage: ROUTE_NAME.DEMOGRAPHIC,
    key: MODULE_NAME.DEMOGRAPHIC_INPUT,
    text: t('Add/Edit'),
    Component: DemographicInput,
  },
  {
    rootPage: ROUTE_NAME.GROUP_EDITOR,
    key: MODULE_NAME.GROUP_EDITOR_HISTORY,
    text: t('Group Editor History'),
    Component: GroupEditorHistory,
  },
  {
    rootPage: ROUTE_NAME.GROUP_EDITOR,
    key: MODULE_NAME.GROUP_EDITOR_INPUT,
    text: t('Group Editor Input'),
    Component: GroupEditorForm,
  },
  {
    rootPage: ROUTE_NAME.GROUP_EDITOR,
    key: MODULE_NAME.GROUP_EDITOR_PATIENT,
    text: t('Patients Assigned By Group'),
    Component: GroupEditorPatientHistory,
  },
  {
    rootPage: ROUTE_NAME.GROUP_EDITOR,
    key: MODULE_NAME.GROUP_EDITOR_PATIENT_INPUT,
    text: t('Patient Input'),
    Component: PatientInputForm,
  },
  {
    rootPage: ROUTE_NAME.IMMUNIZATION,
    key: MODULE_NAME.IMMUNIZATION_HISTORY,
    text: t('Immunization History'),
    Component: ImmunizationHistory,
    icon: 'assets/images/modern-history.png',
  },
  {
    rootPage: ROUTE_NAME.IMMUNIZATION,
    key: MODULE_NAME.IMMUNIZATION_REGISTRY_SETTINGS,
    text: t('Immunization Registry Settings'),
    Component: ImmunizationRegistrySetting,
    icon: 'assets/images/modern-setting.png',
  },
  {
    rootPage: ROUTE_NAME.IMMUNIZATION,
    key: MODULE_NAME.IMMUNIZATION_INPUT,
    text: t('Immunization Input'),
    Component: ImmunizationInputCollapse,
    icon: 'assets/images/modern-edit.png',
  },
  {
    rootPage: ROUTE_NAME.IMMUNIZATION,
    key: MODULE_NAME.IMMUNIZATION_TIMELINE,
    text: t('Immunization Timeline'),
    Component: ImmunizationTimeLineCollapse,
    icon: 'assets/images/timeline.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_AUTOSAVE_DYNAMIC_FORM,
    text: t('Autosave Dynamic Form'),
    Component: AutoSaveDynamicForm,
    icon: 'assets/images/modern-save.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_AUTOSAVE_HX,
    text: t('Autosave HX'),
    Component: AutosaveHx,
    icon: 'assets/images/modern-history.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_ENROLL_REF,
    text: t('Enroll/Ref'),
    Component: EnrollRef,
    icon: 'assets/images/enrollment.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_MENU_MANAGEMENT,
    text: t('Menu Management'),
    Component: MenuManagement,
    icon: 'assets/images/menu-management.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_NEW_ACCOUNT,
    text: t('New Account'),
    Component: NewAccount,
    icon: 'assets/images/avatar.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_PAGE_SECURITIES,
    text: t('Page Securities'),
    Component: PageSecurities,
    icon: 'assets/images/security.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_PRESCRIPTIONS,
    text: t('Prescriptions'),
    Component: Prescriptions,
    icon: 'assets/images/prescriptions.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_PROGRESS_NOTES,
    text: t('Progress Notes'),
    Component: ProgressNotes,
    icon: 'assets/images/progress-note.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_RESET_PASSWORD,
    text: t('Reset Password'),
    Component: ResetPassword,
    icon: 'assets/images/reset-password.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_RIGHT_TRANSFER,
    text: t('Right Transfer'),
    Component: RightTransfer,
    icon: 'assets/images/right-transfer.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_SYSTEM_CONFIGURATION,
    text: t('System Configuration'),
    Component: SystemConfiguration,
    icon: 'assets/images/system-config.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_LOOKUP_TABLE,
    text: t('Lookup Table'),
    Component: LookupTable,
    icon: 'assets/images/support-lookup-table.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_BYPASS_RESET_MFA,
    text: t('Bypass/Reset MFA'),
    Component: ByPassResetMFA,
    icon: 'assets/images/modern-reset.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_DIAGNOSIS_CODE,
    text: t('Diagnosis Codes'),
    Component: DiagnosisCodes,
    icon: 'assets/images/support-lookup-table.png',
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_SERVICE_PLAN,
    text: t('Service Plan'),
    Component: ServicePlanSupport,
  },
  {
    rootPage: ROUTE_NAME.SUPPORT,
    key: MODULE_NAME.SUPPORT_IBHIS,
    text: t('IBHIS'),
    Component: SupportIbhis,
  },
  {
    rootPage: ROUTE_NAME.DATA_IMPORT,
    key: MODULE_NAME.DATA_IMPORT_HISTORY,
    text: t('Import History'),
    Component: DataImportHistory,
    icon: 'assets/images/modern-history.png',
  },
  {
    rootPage: ROUTE_NAME.DATA_IMPORT,
    key: MODULE_NAME.DATA_IMPORT_RUNS_HISTORY,
    text: t('Import Runs History'),
    Component: DataImportRunsHistory,
    icon: 'assets/images/modern-history.png',
  },
  {
    rootPage: ROUTE_NAME.DATA_IMPORT,
    key: MODULE_NAME.DATA_IMPORT_UPLOAD,
    text: t('Data Import'),
    Component: Upload,
    icon: 'assets/images/data-import.png',
  },
  {
    rootPage: ROUTE_NAME.DATA_IMPORT,
    key: MODULE_NAME.DATA_IMPORT_TABLE,
    text: t('Table List'),
    Component: TableList,
    icon: 'assets/images/data-import-table.png',
  },
  {
    rootPage: ROUTE_NAME.DATA_IMPORT,
    key: MODULE_NAME.DATA_IMPORT_DIAGRAM,
    text: t('Table Model Diagram'),
    Component: Diagram,
    icon: 'assets/images/data-import-diagram.png',
  },
  {
    rootPage: ROUTE_NAME.CLOSURE,
    key: MODULE_NAME.CLOSURE_ENROLLMENT_HISTORY,
    text: t('Enrollment History'),
    Component: ClosureEnrollmentHistory,
  },
  {
    rootPage: ROUTE_NAME.CLOSURE,
    key: MODULE_NAME.CLOSURE_HISTORY,
    text: t('Closure History'),
    Component: ClosureHistory,
  },
  {
    rootPage: ROUTE_NAME.CLOSURE,
    key: MODULE_NAME.CLOSURE_EXTEND,
    text: t('Closure Extend'),
    Component: ClosureExtend,
  },

  {
    rootPage: ROUTE_NAME.EXAM_OPTIONS,
    key: MODULE_NAME.EXAM_OPTIONS_TYPES,
    text: t('Types'),
    Component: ExamOptionsTypes,
    icon: 'assets/images/exam-options-types.png',
  },
  {
    rootPage: ROUTE_NAME.EXAM_OPTIONS,
    key: MODULE_NAME.EXAM_OPTIONS_EDIT,
    text: t('Edit'),
    Component: ExamOptionsEdit,
    icon: 'assets/images/modern-edit.png',
  },
  {
    rootPage: ROUTE_NAME.EXAM_OPTIONS,
    key: MODULE_NAME.EXAM_OPTIONS_OPTIONS,
    text: t('Options'),
    Component: ExamOptionsOptions,
    icon: 'assets/images/exam-options-options.png',
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.EXAM_OPTIONS,
    key: MODULE_NAME.EXAM_OPTIONS_OPTIONS_EDIT,
    text: t('Options Edit'),
    Component: ExamOptionsOptionsEdit,
    icon: 'assets/images/exam-options-options-edit.png',
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.SERVICE_PLAN_FIELD_MANAGER,
    key: MODULE_NAME.SERVICE_PLAN_MAPPING_FIELDS,
    text: t('Service Plan Mapping Fields'),
    Component: ServicePlanMappingFields,
    icon: 'assets/images/service-plan-mapping-fields.png',
  },
  {
    rootPage: ROUTE_NAME.SERVICE_PLAN_FIELD_MANAGER,
    key: MODULE_NAME.SERVICE_PLAN_MAPPING_FIELD_INPUT,
    text: t('Service Plan Mapping Field Input'),
    Component: ServicePlanMappingFieldInput,
    icon: 'assets/images/service-plan-mapping-field-input.png',
  },
  {
    rootPage: ROUTE_NAME.SERVICE_PLAN_FIELD_MANAGER,
    key: MODULE_NAME.SERVICE_PLAN_SERVICE_CODES,
    text: t('Service Plan Service Codes'),
    Component: ServicePlanServiceCodes,
    icon: 'assets/images/service-plan-service-codes.png',
  },
  {
    rootPage: ROUTE_NAME.SERVICE_PLAN_FIELD_MANAGER,
    key: MODULE_NAME.SERVICE_PLAN_SERVICE_CODE_INPUT,
    text: t('Service Plan Service Code Input'),
    Component: ServicePlanServiceCodeInput,
    icon: 'assets/images/service-plan-service-code-input.png',
  },
  {
    rootPage: ROUTE_NAME.SERVICE_PLAN_FIELD_MANAGER,
    key: MODULE_NAME.SERVICE_PLAN_FIELDS,
    text: t('Service Plan Fields'),
    Component: ServicePlanFields,
    icon: 'assets/images/service-plan-fields.png',
  },
  {
    rootPage: ROUTE_NAME.SERVICE_PLAN_FIELD_MANAGER,
    key: MODULE_NAME.SERVICE_PLAN_FIELD_INPUT,
    text: t('Service Plan Field Input'),
    Component: ServicePlanFieldInput,
    icon: 'assets/images/service-plan-fields.png',
  },
  {
    rootPage: ROUTE_NAME.SERVICE_PLAN_FIELD_MANAGER,
    key: MODULE_NAME.SERVICE_PLAN_SIGNATURE_INPUT,
    text: t('Service Plan Signature Input'),
    Component: ServicePlanSignatureInput,
  },
  {
    rootPage: ROUTE_NAME.SERVICE_PLAN_FIELD_MANAGER,
    key: MODULE_NAME.SERVICE_PLAN_SIGNATURE_HISTORY,
    text: t('Service Plan Signature History'),
    Component: ServicePlanSignatureHistory,
  },
  {
    rootPage: ROUTE_NAME.RADIOLOGY,
    key: MODULE_NAME.RADIOLOGY_HISTORY,
    text: t('Radiology History'),
    Component: RadiologyHistory,
    icon: 'assets/images/modern-history.png',
  },
  {
    rootPage: ROUTE_NAME.RADIOLOGY,
    key: MODULE_NAME.RADIOLOGY_INPUT,
    text: t('Radiology Input'),
    Component: RadiologyInput,
    icon: 'assets/images/modern-edit.png',
    depends: MODULE_NAME.RADIOLOGY_HISTORY,
  },
  {
    rootPage: ROUTE_NAME.HEALTH_INFORMATION,
    key: MODULE_NAME.HEALTH_INFORMATION_MEDICAL_RECORDS_RELEASES,
    text: t('Medical Records Releases'),
    Component: MedicalRecordsReleases,
    icon: 'assets/images/medical-records-releases.png',
  },
  {
    rootPage: ROUTE_NAME.HEALTH_INFORMATION,
    key: MODULE_NAME.HEALTH_INFORMATION_RELEASES,
    text: t('Releases'),
    Component: Releases,
    icon: 'assets/images/releases.png',
  },
  {
    rootPage: ROUTE_NAME.HEALTH_INFORMATION,
    key: MODULE_NAME.HEALTH_INFORMATION_RELEASE_INPUT,
    text: t('Health Information Release Input'),
    Component: HealthInformationReleaseInput,
    icon: 'assets/images/health-information-input.png',
  },

  {
    rootPage: ROUTE_NAME.QUALITY_MANAGEMENT,
    key: MODULE_NAME.QUALITY_MANAGEMENT_REPORT_SEARCH,
    text: t('Incident Report Search'),
    Component: IncidentReportSearch,
    icon: 'assets/images/modern-search.png',
  },
  {
    rootPage: ROUTE_NAME.QUALITY_MANAGEMENT,
    key: MODULE_NAME.QUALITY_MANAGEMENT_REPORT_INPUT,
    text: t('Incident Report Input'),
    Component: IncidentReportInput,
    icon: 'assets/images/service-plan-field-input.png',
  },
  {
    rootPage: ROUTE_NAME.QUALITY_MANAGEMENT,
    key: MODULE_NAME.QUALITY_MANAGEMENT_CGA_SEARCH,
    text: t('CGA Search'),
    Component: CGASearch,
    icon: 'assets/images/search-detail.png',
  },
  {
    rootPage: ROUTE_NAME.QUALITY_MANAGEMENT,
    key: MODULE_NAME.QUALITY_MANAGEMENT_CGA_INPUT,
    text: t('CGA Input'),
    Component: CGAInput,
    icon: 'assets/images/health-information-input.png',
  },
  {
    rootPage: ROUTE_NAME.CLINICAL_FORMS_SEARCH,
    key: MODULE_NAME.CLINICAL_FORMS_SEARCH,
    text: t('Search Criteria'),
    Component: SearchCriteria,
    icon: 'assets/images/modern-search.png',
  },
  {
    rootPage: ROUTE_NAME.CLINICAL_FORMS_SEARCH,
    key: MODULE_NAME.CLINICAL_FORMS_ENCOUNTERS,
    text: t('Encounter'),
    Component: Encounter,
    icon: 'assets/images/encounter.png',
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.CLINICAL_FORMS_SEARCH,
    key: MODULE_NAME.CLINICAL_FORMS_EMPLOYEES,
    text: t('Employees'),
    Component: Employees,
    icon: 'assets/images/users.png',
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.CLINICAL_FORMS_SEARCH,
    key: MODULE_NAME.CLINICAL_FORMS_PATIENTS,
    text: t('Patients'),
    Component: Patients,
    icon: 'assets/images/users.png',
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.CLINICAL_FORMS_SEARCH,
    key: MODULE_NAME.CLINICAL_FORMS_DYNAMIC_FORMS,
    text: t('Dynamic Forms'),
    Component: DynamicForms,
    icon: 'assets/images/dynamic-form.png',
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.RE_ASSIGNMENT,
    key: MODULE_NAME.RE_ASSIGNMENT_FORMS,
    text: t('Re-Assignment'),
    Component: ReAssignment,
  },
  {
    rootPage: ROUTE_NAME.PSYCH_EVALUATION_NOTE,
    key: MODULE_NAME.PSYCH_EVALUATION_NOTE_INPUT,
    text: t('Psych Evaluation Note Input'),
    Component: PsychEvaluationNoteInput,
  },
  {
    rootPage: ROUTE_NAME.PSYCHOTHERAPY_PROGRESS_NOTE,
    key: MODULE_NAME.PSYCHOTHERAPY_NOTE_INPUT,
    text: t('Psychotherapy Note Input'),
    Component: PsychotherapyProgressNoteInput,
  },
  {
    rootPage: ROUTE_NAME.PRESCRIPTION,
    key: MODULE_NAME.CURRENT_RX,
    text: t('Current Rx'),
    Component: CurrentRxTable,
    icon: 'assets/images/prescriptions.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.PRESCRIPTION,
    key: MODULE_NAME.EXPIRED_RX,
    text: t('Expired Rx'),
    Component: ExpiredRxTable,
    icon: 'assets/images/expired-rx.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.PRESCRIPTION,
    key: MODULE_NAME.OTHER_AGENCY_RX,
    text: t('Other Agency Rx'),
    Component: OtherAgencyRxTable,
    icon: 'assets/images/other-agency-rx.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.PRESCRIPTION,
    key: MODULE_NAME.TIMELINE_RX,
    text: t('Timeline'),
    Component: PrescriptionTimeLine,
    icon: 'assets/images/timeline-rx.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.PRESCRIPTION,
    key: MODULE_NAME.RX_HISTORY,
    text: t('Rx History'),
    Component: RxHistory,
    icon: 'assets/images/modern-history.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.PRESCRIPTION,
    key: MODULE_NAME.MEDICATION_RECONCILIATION,
    text: t('Medication Reconciliation'),
    Component: MedicationReconciliation,
    icon: 'assets/images/medication-reconciliation.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.PAYOR_ASSIGNMENT,
    key: MODULE_NAME.PAYOR_ASSIGNMENT_HISTORY,
    text: t('Payor Assignment History'),
    Component: PayorAssignmentHistoryCollapse,
  },
  {
    rootPage: ROUTE_NAME.PAYOR_ASSIGNMENT,
    key: MODULE_NAME.PAYOR_ASSIGNMENT_INFORMATION,
    text: t('Payor Assignment Information'),
    Component: PayorAssignmentInformationCollapseMain,
  },
  {
    rootPage: ROUTE_NAME.CRM,
    key: MODULE_NAME.CRM_DASHBOARD,
    text: t('CRM Dashboard'),
    Component: CRMDashboard,
    icon: 'assets/images/crm-dashboard.png',
  },
  {
    rootPage: ROUTE_NAME.CRM,
    key: MODULE_NAME.CRM_CLIENT,
    text: t('Clients'),
    Component: CRMClients,
    icon: 'assets/images/crm-client.png',
  },
  {
    rootPage: ROUTE_NAME.CRM,
    key: MODULE_NAME.CRM_ANALYTICS,
    text: t('Analytics'),
    Component: CRMAnalytics,
    icon: 'assets/images/crm-chart.png',
  },
  {
    rootPage: ROUTE_NAME.PSYCHOSOCIAL_ASSESSMENT_NOTE,
    key: MODULE_NAME.PSYCHOSOCIAL_ASSESSMENT_NOTE_INPUT,
    text: t('Psychosocial Assessment Note Input'),
    Component: PsychosocialAssessmentNoteInput,
  },
  {
    rootPage: ROUTE_NAME.INFORMED_CONSENT,
    key: MODULE_NAME.INFORMED_CONSENT_HISTORY,
    text: t('Informed Consent History'),
    Component: InformedConsentHistory,
  },
  {
    rootPage: ROUTE_NAME.INFORMED_CONSENT,
    key: MODULE_NAME.INFORMED_CONSENT_INFO,
    text: t('Informed Consent Info'),
    Component: InformedConsentInfo,
  },
  {
    rootPage: ROUTE_NAME.GAD7,
    key: MODULE_NAME.GAD_7_HISTORY,
    text: t('Generalized Anxiety Disorder 7 History'),
    icon: 'assets/images/modern-history.png',
    Component: GAD7History,
  },
  {
    rootPage: ROUTE_NAME.GAD7,
    key: MODULE_NAME.GAD_7_FORM,
    text: t('GAD-7 Input'),
    icon: 'assets/images/modern-edit.png',
    Component: GAD7Form,
  },
  {
    rootPage: ROUTE_NAME.GAD7,
    key: MODULE_NAME.GAD_7_CHART,
    text: t('Graph/Outcome'),
    icon: 'assets/images/crm-chart.png',
    Component: GAD7Chart,
  },
  {
    rootPage: ROUTE_NAME.VITALS,
    key: MODULE_NAME.VITAL_HISTORY,
    text: t('Vital History'),
    icon: 'assets/images/modern-history.png',
    Component: VitalsHistory,
  },
  {
    rootPage: ROUTE_NAME.VITALS,
    key: MODULE_NAME.VITAL_FORM,
    text: t('Vital Input'),
    icon: 'assets/images/modern-edit.png',
    Component: VitalsForm,
  },
  {
    rootPage: ROUTE_NAME.VITALS,
    key: MODULE_NAME.VITAL_CHART,
    text: t('Graph/Outcome'),
    icon: 'assets/images/crm-chart.png',
    Component: VitalsChart,
  },
  {
    rootPage: ROUTE_NAME.PHQ,
    key: MODULE_NAME.PHQ_HISTORY,
    text: t('PHQ History'),
    icon: 'assets/images/modern-history.png',
    Component: PHQHistory,
  },
  {
    rootPage: ROUTE_NAME.PHQ,
    key: MODULE_NAME.PHQ_FORM,
    text: t('PHQ Input'),
    icon: 'assets/images/modern-edit.png',
    Component: PHQForm,
  },
  {
    rootPage: ROUTE_NAME.PHQ,
    key: MODULE_NAME.PHQ_CHART,
    text: t('Graph/Outcome'),
    icon: 'assets/images/crm-chart.png',
    Component: PHQChart,
  },
  {
    rootPage: ROUTE_NAME.PCL5,
    key: MODULE_NAME.PCL5_HISTORY,
    text: t('Post-traumatic Checklist for DSM-5 History'),
    icon: 'assets/images/modern-history.png',
    Component: PCL5History,
  },
  {
    rootPage: ROUTE_NAME.PCL5,
    key: MODULE_NAME.PCL5_FORM,
    text: t('Post-traumatic Checklist for DSM-5 Input'),
    icon: 'assets/images/modern-edit.png',
    Component: PCL5Form,
  },
  {
    rootPage: ROUTE_NAME.PCL5,
    key: MODULE_NAME.PCL5_CHART,
    text: t('Graph/Outcome'),
    icon: 'assets/images/crm-chart.png',
    Component: PCL5Chart,
  },
  {
    rootPage: ROUTE_NAME.ACE,
    key: MODULE_NAME.ACE_HISTORY,
    text: t('Adverse Childhood Experience History'),
    icon: 'assets/images/modern-history.png',
    Component: ACEHistory,
  },
  {
    rootPage: ROUTE_NAME.ACE,
    key: MODULE_NAME.ACE_FORM,
    text: t('Adverse Childhood Experience Input'),
    icon: 'assets/images/modern-edit.png',
    Component: ACEForm,
  },
  {
    rootPage: ROUTE_NAME.ACE,
    key: MODULE_NAME.ACE_CHART,
    text: t('Graph/Outcome'),
    icon: 'assets/images/crm-chart.png',
    Component: ACEChart,
  },
  {
    rootPage: ROUTE_NAME.AUDIT,
    key: MODULE_NAME.AUDIT_HISTORY,
    text: t('Alcohol Use Disorders Identification Tests History'),
    icon: 'assets/images/modern-history.png',
    Component: AuditHistory,
  },
  {
    rootPage: ROUTE_NAME.AUDIT,
    key: MODULE_NAME.AUDIT_FORM,
    text: t('Alcohol Use Disorders Identification Tests Input'),
    icon: 'assets/images/modern-edit.png',
    Component: AuditForm,
  },
  {
    rootPage: ROUTE_NAME.AUDIT,
    key: MODULE_NAME.AUDIT_CHART,
    text: t('Graph/Outcome'),
    icon: 'assets/images/crm-chart.png',
    Component: AuditChart,
  },
  {
    rootPage: ROUTE_NAME.PC_PTSD,
    key: MODULE_NAME.PC_PTSD_HISTORY,
    text: t('Primary Care PTSD History'),
    icon: 'assets/images/modern-history.png',
    Component: PCPTSDHistory,
  },
  {
    rootPage: ROUTE_NAME.PC_PTSD,
    key: MODULE_NAME.PC_PTSD_FORM,
    text: t('Primary Care PTSD Input'),
    icon: 'assets/images/modern-edit.png',
    Component: PCPTSDForm,
  },
  {
    rootPage: ROUTE_NAME.PC_PTSD,
    key: MODULE_NAME.PC_PTSD_CHART,
    text: t('Graph/Outcome'),
    icon: 'assets/images/crm-chart.png',
    Component: PCPTSDChart,
  },
  {
    rootPage: ROUTE_NAME.PHQA,
    key: MODULE_NAME.PHQ_A_HISTORY,
    text: t('PHQ-A History'),
    icon: 'assets/images/modern-history.png',
    Component: PHQAHistory,
  },
  {
    rootPage: ROUTE_NAME.PHQA,
    key: MODULE_NAME.PHQ_A_FORM,
    text: t('PHQ-9 modified for Adolescents (PHQ-A)'),
    icon: 'assets/images/modern-edit.png',
    Component: PHQAForm,
  },
  {
    rootPage: ROUTE_NAME.FUNDING_SOURCE,
    key: MODULE_NAME.FUNDING_SOURCE_HISTORY,
    text: t('Funding Source History'),
    icon: 'assets/images/modern-history.png',
    Component: FundingSourceHistory,
  },
  {
    rootPage: ROUTE_NAME.FUNDING_SOURCE,
    key: MODULE_NAME.FUNDING_SOURCE_INPUT,
    text: t('Funding Source Input'),
    icon: 'assets/images/modern-edit.png',
    Component: FundingSourceInput,
  },
  {
    rootPage: ROUTE_NAME.PRAPARE,
    key: MODULE_NAME.PRAPARE_HISTORY,
    text: t('PRAPARE History'),
    icon: 'assets/images/modern-history.png',
    Component: PRAPAREHistory,
  },
  {
    rootPage: ROUTE_NAME.PRAPARE,
    key: MODULE_NAME.PRAPARE_INPUT,
    text: t('Questionnaire Panel'),
    icon: 'assets/images/modern-edit.png',
    Component: PRAPAREForm,
  },
  {
    rootPage: ROUTE_NAME.COWS,
    key: MODULE_NAME.COWS_HISTORY,
    text: t('Clinical Opiate Withdrawal Scale (COWS) Questionnaire​'),
    icon: 'assets/images/modern-history.png',
    Component: COWSHistory,
  },
  {
    rootPage: ROUTE_NAME.COWS,
    key: MODULE_NAME.COWS_INPUT,
    text: t('Clinical Opiate Withdrawal Scale'),
    icon: 'assets/images/modern-edit.png',
    Component: COWSForm,
  },
  {
    rootPage: ROUTE_NAME.ENGAGEMENT,
    key: MODULE_NAME.ENGAGEMENT_HISTORY,
    text: t('Engagement History​'),
    icon: 'assets/images/modern-history.png',
    Component: EngagementHistory,
  },
  {
    rootPage: ROUTE_NAME.ENGAGEMENT,
    key: MODULE_NAME.ENGAGEMENT_INPUT,
    text: t('Engagement'),
    icon: 'assets/images/modern-edit.png',
    Component: EngagementForm,
  },
  {
    rootPage: ROUTE_NAME.CALOCUS,
    key: MODULE_NAME.CALOCUS_HISTORY,
    text: t('CALOCUS Evaluation History'),
    icon: 'assets/images/modern-history.png',
    Component: CALOCUSHistory,
    api: QUERY_KEY.CALOCUS_HISTORY,
  },
  {
    rootPage: ROUTE_NAME.CALOCUS,
    key: MODULE_NAME.CALOCUS_INPUT,
    text: t('CALOCUS Evaluation Input'),
    icon: 'assets/images/modern-edit.png',
    Component: CALOCUSForm,
  },
  {
    rootPage: ROUTE_NAME.CALOCUS,
    key: MODULE_NAME.CALOCUS_LEVEL_OF_CARE,
    text: t('Level of Care Definitions'),
    icon: 'assets/images/details.png',
    Component: CALOCUSLOC,
  },
  {
    rootPage: ROUTE_NAME.LOCUS,
    key: MODULE_NAME.LOCUS_HISTORY,
    text: t('LOCUS History'),
    icon: 'assets/images/modern-history.png',
    Component: LOCUSHistory,
    api: QUERY_KEY.LOCUS_HISTORY,
  },
  {
    rootPage: ROUTE_NAME.LOCUS,
    key: MODULE_NAME.LOCUS_INPUT,
    text: t('LOCUS Input'),
    icon: 'assets/images/modern-edit.png',
    Component: LOCUSForm,
  },
  {
    rootPage: ROUTE_NAME.LOCUS,
    key: MODULE_NAME.LOCUS_LEVEL_OF_CARE,
    text: t('Level of Care Definitions'),
    icon: 'assets/images/details.png',
    Component: LOCUSLOC,
  },
  {
    rootPage: ROUTE_NAME.COURT_ORDERED_TREATMENT,
    key: MODULE_NAME.COT_HISTORY,
    icon: 'assets/images/modern-history.png',
    text: t('Court Ordered Treatment History'),
    Component: COTHistory,
  },
  {
    rootPage: ROUTE_NAME.COURT_ORDERED_TREATMENT,
    key: MODULE_NAME.COT_FORM,
    icon: 'assets/images/modern-edit.png',
    text: t('Add/Edit COT'),
    Component: COTForm,
  },
  {
    rootPage: ROUTE_NAME.COURT_ORDERED_TREATMENT,
    key: MODULE_NAME.COT_AMENDMENTS,
    text: t('Amendments'),
    Component: COTAmendments,
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.COURT_ORDERED_TREATMENT,
    key: MODULE_NAME.COT_JUDICIAL_REVIEWS,
    text: t('Judicial Review'),
    Component: COTJudicialReview,
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.COURT_ORDERED_TREATMENT,
    key: MODULE_NAME.COT_STATUS_REPORTS,
    text: t('Status Report'),
    Component: COTStatusReport,
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.MEDICATION_ASSISTED_TREATMENT,
    key: MODULE_NAME.UPCOMING_VISIT,
    text: t('Upcoming Visits'),
    Component: UpcomingVisit,
    icon: 'ColorCalendar',
    api: QUERY_KEY.MAT_ADMISSION,
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.MEDICATION_ASSISTED_TREATMENT,
    key: MODULE_NAME.PENDING_INTAKE,
    text: t('Pending Intakes'),
    Component: PendingIntake,
    icon: 'assets/images/pending-intake.png',
    api: QUERY_KEY.MAT_PENDING_INTAKE,
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.MEDICATION_ASSISTED_TREATMENT,
    key: MODULE_NAME.MAT_MONITOR_DISPLAY_EDITOR,
    text: t('Monitor Display Editor'),
    Component: MonitorDisplayEditor,
    icon: 'assets/images/monitor-display.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.MEDICATION_ASSISTED_TREATMENT,
    key: MODULE_NAME.MAT_REPORTS,
    text: t('Reports'),
    Component: MATReports,
    icon: 'assets/images/dynamic-form.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.MEDICATION_ASSISTED_TREATMENT,
    key: MODULE_NAME.MAT_PATIENT_CENTER,
    text: t('Patient Center'),
    Component: PatientCenter,
    icon: 'assets/images/patient.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.MEDICATION_ASSISTED_TREATMENT,
    key: MODULE_NAME.MAT_INVENTORY,
    text: t('Inventory'),
    Component: Inventory,
    icon: 'assets/images/inventory.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.MEDICATION_ASSISTED_TREATMENT,
    key: MODULE_NAME.MAT_FACILITY_SCHEDULE,
    text: t('Facility Schedule'),
    Component: FacilitySchedule,
    icon: 'assets/images/mat-facility-schedule.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.MEDICATION_ASSISTED_TREATMENT,
    key: MODULE_NAME.MAT_AUTO_SCHEDULE_CARE_PLANS,
    text: t('Auto Schedule Care Plans'),
    Component: AutoScheduleCarePlans,
    icon: 'assets/images/mat-auto-schedule.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.MEDICATION_ASSISTED_TREATMENT,
    key: MODULE_NAME.MAT_DASHBOARD,
    text: t('Dashboard'),
    Component: MATDashboard,
    icon: 'ColorWelcomePage',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.MEDICATION_ASSISTED_TREATMENT,
    key: MODULE_NAME.MAT_QUEUE_CENTER,
    text: t('Queue Center'),
    Component: QueueCenter,
    icon: 'assets/images/mat-queue-center.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.DISCHARGE_PLAN,
    key: MODULE_NAME.DISCHARGE_PLAN_HISTORY,
    text: t('Discharge Plan History'),
    icon: 'assets/images/modern-history.png',
    Component: DischargePlanHistory,
  },
  {
    rootPage: ROUTE_NAME.DISCHARGE_PLAN,
    key: MODULE_NAME.INPUT_CLINICAL_DISCHARGE,
    text: t('Input Clinical Discharge'),
    icon: 'assets/images/modern-edit.png',
    Component: InputClinicalDischarge,
  },
  {
    rootPage: ROUTE_NAME.CINA,
    key: MODULE_NAME.CINA_INPUT,
    text: t('Narcotic Assessment Input'),
    Component: CINAInput,
  },
  {
    rootPage: ROUTE_NAME.CINA,
    key: MODULE_NAME.CINA_REPORTS,
    text: t('Narcotic Assessment Report'),
    Component: CINAHistory,
  },
  {
    rootPage: ROUTE_NAME.CIWA,
    key: MODULE_NAME.CIWA_INPUT,
    text: t('CIWA Of Alcohol Scale, Revised'),
    Component: CIWAForm,
  },
  {
    rootPage: ROUTE_NAME.CIWA,
    key: MODULE_NAME.CIWA_REPORTS,
    text: t('CIWA Report'),
    Component: CIWAHistory,
  },
  {
    rootPage: ROUTE_NAME.ART_CFT,
    key: MODULE_NAME.ART_CFT_MEETING_SUMMARIES,
    text: t('ART/CFT Meeting Summaries'),
    icon: 'assets/images/modern-history.png',
    Component: ARTMeetingSummaries,
  },
  {
    rootPage: ROUTE_NAME.ART_CFT,
    key: MODULE_NAME.ART_CFT_AUTO_SAVE,
    text: t('ART/CFT Meeting Summaries Auto Save'),
    icon: 'assets/images/modern-save.png',
    Component: ARTAutoSave,
  },
  {
    rootPage: ROUTE_NAME.ART_CFT,
    key: MODULE_NAME.ART_CFT_STAFF_SUMMARIES,
    text: t('ART/CFT Staff Summaries'),
    icon: 'assets/images/details.png',
    Component: ARTStaffSummaries,
  },
  {
    rootPage: ROUTE_NAME.ART_CFT,
    key: MODULE_NAME.ART_CFT_INPUT,
    text: t('Meeting Summary'),
    icon: 'assets/images/modern-edit.png',
    Component: ARTForm,
  },
  {
    rootPage: ROUTE_NAME.LAB_RESULT_ERROR,
    key: MODULE_NAME.LAB_RESULT_ERROR_HISTORY,
    text: t('Lab Results Errors'),
    icon: 'assets/images/modern-history.png',
    Component: LabResultsErrors,
  },
  {
    rootPage: ROUTE_NAME.LAB_RESULT_ERROR,
    key: MODULE_NAME.LAB_RESULT_ERROR_LAB_SEARCH,
    text: t('Lab Search'),
    icon: 'assets/images/modern-search.png',
    Component: LabResultsErrorsLab,
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.LAB_RESULT_ERROR,
    key: MODULE_NAME.LAB_RESULT_ERROR_PATIENT_SEARCH,
    text: t('Patients'),
    icon: 'assets/images/patient.png',
    Component: LabResultsErrorsPatient,
    defaultShow: false,
  },
  {
    rootPage: ROUTE_NAME.INTERNAL_ORDERS,
    key: MODULE_NAME.INTERNAL_ORDER_ENROLLMENT,
    text: t('Enrollment'),
    icon: 'assets/images/patient.png',
    Component: InternalOrdersEnrollment,
  },
  {
    rootPage: ROUTE_NAME.INTERNAL_ORDERS,
    key: MODULE_NAME.INTERNAL_ORDER_HISTORY,
    text: t('Internal Orders History'),
    icon: 'assets/images/modern-history.png',
    Component: InternalOrdersHistory,
  },
  {
    rootPage: ROUTE_NAME.INTERNAL_ORDERS,
    key: MODULE_NAME.INTERNAL_ORDER_INPUT,
    text: t('Internal Order Form'),
    icon: 'assets/images/modern-edit.png',
    Component: InternalOrdersInput,
  },
  {
    rootPage: ROUTE_NAME.ENCOUNTER_ENTRY,
    key: MODULE_NAME.ENCOUNTER_ENTRY_PATIENT,
    text: t('Patient Information'),
    icon: 'assets/images/patient.png',
    Component: EncounterEntryPatient,
  },
  {
    rootPage: ROUTE_NAME.ENCOUNTER_ENTRY,
    key: MODULE_NAME.ENCOUNTER_ENTRY_INPUT,
    text: t('Encounter Input'),
    icon: 'assets/images/modern-edit.png',
    Component: EncounterEntryInput,
  },
  {
    rootPage: ROUTE_NAME.ENGAGEMENT_SESSION_NOTE,
    key: MODULE_NAME.ENGAGEMENT_SESSION_NOTE_INPUT,
    text: t('Engagement Session Note Input'),
    Component: EngagementSessionNoteInput,
  },
  {
    rootPage: ROUTE_NAME.COMMUNICATIONS_CENTER,
    key: MODULE_NAME.COMMUNICATIONS_CENTER,
    text: t('Messaging'),
    icon: 'assets/images/messaging.png',
    Component: CommunicationsCenter,
  },
  {
    rootPage: ROUTE_NAME.PATIENT_EMPLOYEE_RESTRICTION,
    key: MODULE_NAME.PATIENT_ROLE_RESTRICTION,
    text: t('Patient Role Restriction'),
    Component: PatientRoleRestriction,
  },
  {
    rootPage: ROUTE_NAME.PATIENT_EMPLOYEE_RESTRICTION,
    key: MODULE_NAME.PATIENT_EMPLOYEE_RESTRICTION,
    text: t('Patient/Employee Restriction'),
    Component: PatientEmployeeRestriction,
  },
  {
    rootPage: ROUTE_NAME.INJECTION,
    key: MODULE_NAME.INJECTION_HISTORY,
    text: t('Injection History'),
    icon: 'assets/images/modern-history.png',
    Component: InjectionHistoryWrapper,
  },
  {
    rootPage: ROUTE_NAME.INJECTION,
    key: MODULE_NAME.INJECTION_INPUT,
    text: t('Injection Form'),
    icon: 'assets/images/modern-edit.png',
    Component: InjectionInput,
  },
  {
    rootPage: ROUTE_NAME.PENDING_PATIENT_REQUESTS,
    key: MODULE_NAME.PENDING_PATIENT_HISTORY,
    text: t('Pending Patient Requests'),
    Component: PendingPatientRequestsHistory,
    icon: 'assets/images/modern-history.png',
  },
  {
    rootPage: ROUTE_NAME.ALLERGY,
    key: MODULE_NAME.ALLERGY_HISTORY,
    text: t('Allergy History'),
    icon: 'assets/images/modern-history.png',
    Component: AllergyHistoryWrapper,
  },
  {
    rootPage: ROUTE_NAME.ALLERGY,
    key: MODULE_NAME.ALLERGY_INPUT,
    text: t('Allergy Input'),
    icon: 'assets/images/modern-edit.png',
    Component: AllergyInput,
  },
  {
    rootPage: ROUTE_NAME.TIME_BLOCK_REASON,
    key: MODULE_NAME.TIME_BLOCKED_REASON_HISTORY,
    text: t('Time Blocked Reason History'),
    icon: 'assets/images/modern-history.png',
    Component: TimeBlockedReasonHistory,
  },
  {
    rootPage: ROUTE_NAME.TIME_BLOCK_REASON,
    key: MODULE_NAME.SCHEDULE_REASON_LIST,
    text: t('Schedule Reason List'),
    icon: 'assets/images/modern-history.png',
    Component: ScheduleReasonList,
  },
  {
    rootPage: ROUTE_NAME.CYBHI,
    key: MODULE_NAME.CYBHI_CLIENT_DATA,
    text: t('CYBHI Client Data'),
    icon: 'assets/images/ibhis-patient-info.png',
    Component: CybhiClientData,
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    key: MODULE_NAME.IBHIS_CANS,
    text: t('CANS'),
    icon: 'assets/images/details.png',
    Component: CANS,
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    key: MODULE_NAME.IBHIS_PSC,
    text: t('PSC'),
    icon: 'assets/images/details.png',
    Component: PSC,
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    key: MODULE_NAME.IBHIS_CLIENT_DATA,
    text: t('Client Data'),
    icon: 'assets/images/ibhis-patient-info.png',
    Component: ClientData,
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    key: MODULE_NAME.IBHIS_SERVICE_REQUEST,
    text: t('Service Request'),
    icon: 'assets/images/additional.png',
    Component: ServiceRequest,
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    key: MODULE_NAME.IBHIS_REPORT,
    text: t('Reports'),
    icon: 'assets/images/ibhis-report.png',
    Component: Reports,
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    key: MODULE_NAME.IBHIS_SEARCH,
    text: t('Episodes'),
    icon: 'assets/images/ibhis-espiso.png',
    Component: SearchClientRecord,
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    key: MODULE_NAME.IBHIS_UPDATE,
    text: t('Update Episode'),
    icon: 'assets/images/ibhis-refresh.png',
    Component: UpdateAxiomClient,
    defaultShow: false,
    depends: MODULE_NAME.IBHIS_SEARCH,
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    key: MODULE_NAME.IBHIS_SYNC_HISTORY,
    text: t('IBHIS Sync History'),
    icon: 'assets/images/modern-history.png',
    Component: SyncHistory,
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    key: MODULE_NAME.IBHIS_LOCUS,
    text: t('LOCUS'),
    icon: 'assets/images/details.png',
    Component: LOCUS,
  },
  {
    rootPage: ROUTE_NAME.IBHIS,
    key: MODULE_NAME.IBHIS_CSI_ASSESSMENT,
    text: t('CSI Assessment'),
    icon: 'assets/images/details.png',
    Component: CSIAssessment,
  },
  {
    rootPage: ROUTE_NAME.ANNOUNCEMENTS,
    key: MODULE_NAME.ANNOUNCEMENTS_HISTORY,
    text: t('Announcements History'),
    icon: 'assets/images/modern-history.png',
    Component: AnnouncementsHistory,
  },
  {
    rootPage: ROUTE_NAME.ANNOUNCEMENTS,
    key: MODULE_NAME.ANNOUNCEMENTS_INPUT,
    text: t('Announcements Input'),
    icon: 'assets/images/modern-edit.png',
    Component: AnnouncementsForm,
  },
  {
    rootPage: ROUTE_NAME.SUICIDE_SEVERITY_RATING_SCALE,
    key: MODULE_NAME.SUICIDE_SEVERITY_RATING_SCALE_HISTORY,
    text: t('Detailed Columbia - Suicide Severity Rating Scale History'),
    icon: 'assets/images/modern-history.png',
    Component: SuicideSeverityRatingScaleHistory,
  },
  {
    rootPage: ROUTE_NAME.SUICIDE_SEVERITY_RATING_SCALE,
    key: MODULE_NAME.SUICIDE_SEVERITY_RATING_SCALE_FORM,
    text: t('Detailed Columbia - Suicide Severity Rating Scale'),
    icon: 'assets/images/modern-edit.png',
    Component: SuicideSeverityRatingScaleForm,
  },
  {
    rootPage: ROUTE_NAME.NURSE_PROGRESS_NOTE,
    key: MODULE_NAME.NURSE_PROGRESS_NOTE,
    text: t('Nurse Progress Note'),
    Component: NurseProgressNoteInput,
  },
  {
    rootPage: ROUTE_NAME.FAMILY_HEALTH,
    key: MODULE_NAME.FAMILY_HEALTH_HISTORY,
    text: t('Family Health History'),
    icon: 'assets/images/modern-history.png',
    Component: FamilyHealthHistory,
  },
  {
    rootPage: ROUTE_NAME.FAMILY_HEALTH,
    key: MODULE_NAME.FAMILY_HEALTH_INPUT,
    text: t('Family Health Input'),
    icon: 'assets/images/modern-edit.png',
    Component: FamilyHealthForm,
  },
  {
    rootPage: ROUTE_NAME.PLACEMENT,
    key: MODULE_NAME.PLACEMENT_HISTORY,
    text: t('Out of Home Placement History'),
    icon: 'assets/images/modern-history.png',
    Component: PlacementHistory,
  },
  {
    rootPage: ROUTE_NAME.PLACEMENT,
    key: MODULE_NAME.PLACEMENT_INPUT,
    text: t('Out of Home Placement Input'),
    icon: 'assets/images/modern-edit.png',
    Component: PlacementForm,
  },
  {
    rootPage: ROUTE_NAME.INPATIENT_NOTE,
    key: MODULE_NAME.INPATIENT_NOTE_INPUT,
    text: t('Inpatient Note Input'),
    Component: InpatientNoteInput,
  },
  {
    rootPage: ROUTE_NAME.IMPLANTABLE_DEVICES,
    key: MODULE_NAME.IMPLANTABLE_DEVICE_HISTORY,
    text: t('Implantable Devices History'),
    icon: 'assets/images/modern-history.png',
    Component: ImplantableDeviceHistory,
  },
  {
    rootPage: ROUTE_NAME.IMPLANTABLE_DEVICES,
    key: MODULE_NAME.IMPLANTABLE_DEVICE_ENTRY,
    text: t('Implantable Devices Entry'),
    icon: 'assets/images/modern-edit.png',
    Component: ImplantableDeviceEntry,
  },
  {
    rootPage: ROUTE_NAME.PATIENT_PROCEDURE,
    key: MODULE_NAME.PATIENT_PROCEDURES_HISTORY,
    text: t('Procedures History'),
    Component: PatientProcedureHistory,
  },
  {
    rootPage: ROUTE_NAME.PATIENT_PROCEDURE,
    key: MODULE_NAME.PATIENT_PROCEDURE_INPUT,
    text: t('Procedure Input'),
    Component: PatientProcedureInput,
  },
  {
    rootPage: ROUTE_NAME.WHO_ASSIST,
    key: MODULE_NAME.WHO_ASSIST_HISTORY,
    text: t('Assist History'),
    icon: 'assets/images/modern-history.png',
    Component: WHOAssistHistory,
  },
  {
    rootPage: ROUTE_NAME.WHO_ASSIST,
    key: MODULE_NAME.WHO_ASSIST_FORM,
    text: t('WHO - Assist V3.0'),
    icon: 'assets/images/modern-edit.png',
    Component: WHOAssistForm,
  },
  {
    rootPage: ROUTE_NAME.DIRECT_MESSAGING,
    key: MODULE_NAME.DIRECT_MESSAGING,
    text: t('Direct Messages'),
    icon: 'assets/images/modern-history.png',
    Component: DirectMessages,
  },
  {
    rootPage: ROUTE_NAME.DIRECT_MESSAGING,
    key: MODULE_NAME.DIRECT_MESSAGING_ADD,
    text: t('Add New Message'),
    icon: 'assets/images/modern-edit.png',
    Component: AddNewMessage,
  },
  {
    rootPage: ROUTE_NAME.SAFET,
    key: MODULE_NAME.SUICIDE_ASSESSMENT_FIVE_STEP_EVALUATION_AND_TRIAGE_HISTORY,
    text: t('Suicide Assessment Five-step Evaluation and Triage History'),
    Component: SuicideAssessmentFiveStepEvaluationAndTriageHistory,
  },
  {
    rootPage: ROUTE_NAME.SAFET,
    key: MODULE_NAME.SUICIDE_ASSESSMENT_FIVE_STEP_EVALUATION_AND_TRIAGE_INPUT,
    text: t('Suicide Assessment Five-step Evaluation and Triage Input'),
    Component: SuicideAssessmentFiveStepEvaluationAndTriageInput,
  },
  {
    rootPage: ROUTE_NAME.SUICIDE_RISK_SCREENING,
    key: MODULE_NAME.SUICIDE_RISK_SCREENING_FORM,
    text: t('Suicide Risk Screening'),
    icon: 'assets/images/modern-edit.png',
    Component: SuicideRiskScreeningFormInput,
  },
  {
    rootPage: ROUTE_NAME.SUICIDE_RISK_SCREENING,
    key: MODULE_NAME.SUICIDE_RISK_SCREENING_HISTORY,
    text: t('Suicide Risk Screening History'),
    icon: 'assets/images/modern-history.png',
    Component: SuicideRiskScreeningHistory,
  },
];

const mockBatchSidebar = [
  { key: MODULE_NAME.BATCH_837, isSubPath: true, text: t('Batch 837 - Invoice - HCFA') },
  { key: MODULE_NAME.BATCH_834, isSubPath: true, text: t('Batch 834') },
  { key: MODULE_NAME.REPORTING_UPLOAD, isSubPath: true, text: t('Reporting Upload') },
  { key: MODULE_NAME.RECONCILE_277, isSubPath: true, text: t('Reconcile 277') },
  { key: MODULE_NAME.RECONCILE_835, isSubPath: true, text: t('Reconcile 835/EOB') },
  { key: MODULE_NAME.ENCOUNTERS, isSubPath: true, text: t('Encounters') },
  // { key: MODULE_NAME.PAYOR_ASSIGNMENT, isSubPath: true, text: t('Payor Assignment') },
  { key: MODULE_NAME.PAYOR_EDITOR, isSubPath: true, text: t('Payors Editor') },
  { key: MODULE_NAME.FEE_SCHEDULE, isSubPath: true, text: t('Fee Schedule') },
  { key: MODULE_NAME.PROVIDERS, isSubPath: true, text: t('Providers') },
  { key: MODULE_NAME.RENDERING_PROVIDERS, isSubPath: true, text: t('Rendering Providers') },
  { key: MODULE_NAME.REMIT_EOB, isSubPath: true, text: t('Remit EOB') },
  { key: MODULE_NAME.RCM_REPORT, isSubPath: true, text: t('RCM Report') },
];

const MODULE_DATA = moduleMapping.reduce((acc, item) => ({ ...acc, [item.key]: item }), {});
const generatePageSections = (sections = []) =>
  sections.map((key) => ({
    key: MODULE_DATA[key].key,
    pageKey: `${MODULE_DATA[key].rootPage}/${MODULE_DATA[key].key}-component`,
    text: MODULE_DATA[key].text,
    icon: MODULE_DATA[key].icon,
    rootPage: MODULE_DATA[key].rootPage,
    depends: MODULE_DATA[key].depends,
    strict: MODULE_DATA[key].strict,
    api: MODULE_DATA[key].api,
  }));

const mockNavigatorItems = [
  {
    key: ROUTE_NAME.PAYMENT_MANAGEMENT,
    text: t('Payment Management'),
    iconProps: { iconName: 'PaymentCard' },
    subMenu: generatePageSections([MODULE_NAME.PAYMENT_HISTORY, MODULE_NAME.PAYMENT_INPUT]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.BATCH,
    text: t('Revenue Cycle Management'),
    iconProps: { iconName: 'Flag' },
    subMenu: mockBatchSidebar,
    showCardView: false,
    noSplitScreen: true,
  },
  {
    key: ROUTE_NAME.PRESCRIPTION,
    text: t('Prescription'),
    iconProps: { iconName: 'FavoriteStar' },
    showCardView: true,
    subMenu: generatePageSections([
      MODULE_NAME.CURRENT_RX,
      MODULE_NAME.EXPIRED_RX,
      MODULE_NAME.OTHER_AGENCY_RX,
      MODULE_NAME.TIMELINE_RX,
      MODULE_NAME.RX_HISTORY,
      MODULE_NAME.MEDICATION_RECONCILIATION,
    ]),
  },
  {
    key: ROUTE_NAME.RPA,
    text: t('Automation'),
    iconProps: { iconName: 'AxiamRobot' },
    subMenu: generatePageSections([MODULE_NAME.RPA_HISTORY, MODULE_NAME.RPA_RECORD]),
    showCardView: false,
  },
  {
    key: ROUTE_NAME.ASAM,
    text: t('ASAM'),
    iconProps: { iconName: 'Hospital' },
    subMenu: generatePageSections([MODULE_NAME.ASAM_HISTORY, MODULE_NAME.ASAM_INPUT]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.CRISIS_PLAN,
    text: t('Crisis Intervention Relapse Plan'),
    iconProps: { iconName: 'DrillExpand' },
    subMenu: generatePageSections([
      MODULE_NAME.CRISIS_HISTORY,
      MODULE_NAME.CRISIS_CHILD_HISTORY,
      MODULE_NAME.CRISIS_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.CRISIS_PREVENTION_PLAN,
    text: t('Crisis Prevention Plan'),
    iconProps: { iconName: 'DrillExpand' },
    subMenu: generatePageSections([
      MODULE_NAME.CRISIS_PREVENTION_HISTORY,
      MODULE_NAME.CRISIS_PREVENTION_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.SNCD,
    text: t('SNCD Plan'),
    iconProps: { iconName: 'Assign' },
    subMenu: generatePageSections([MODULE_NAME.SNCD_HISTORY, MODULE_NAME.SNCD_INPUT]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.SUPPORT_PLAN,
    text: t('Support & Safety Plan'),
    iconProps: { iconName: 'Assign' },
    subMenu: generatePageSections([
      MODULE_NAME.SUPPORT_PLAN_HISTORY,
      MODULE_NAME.SUPPORT_PLAN_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.DASHBOARD,
    text: t('Dashboard'),
    iconProps: { iconName: 'ViewAll' },
    subMenu: generatePageSections([
      MODULE_NAME.DASHBOARD_COMPLIANCE,
      MODULE_NAME.DASHBOARD_ENROLLMENT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.DYNAMIC_FORM,
    text: t('Dynamic Form Builder'),
    iconProps: { iconName: 'PageEdit' },
    subMenu: generatePageSections([MODULE_NAME.DF_RECENT_FORMS, MODULE_NAME.DF_HISTORY]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.APPOINTMENT,
    text: t('Appointment'),
    iconProps: { iconName: 'Calendar' },
    subMenu: generatePageSections([
      MODULE_NAME.APPOINTMENT_HISTORY,
      MODULE_NAME.APPOINTMENT_BY_SITE,
      MODULE_NAME.TIME_BLOCKED_HISTORY,
      MODULE_NAME.AIMS,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.ANALYTIC,
    text: t('Analytics'),
    iconProps: { iconName: 'Analytic' },
    subMenu: generatePageSections([
      MODULE_NAME.METRIC_PERFORMANCE,
      MODULE_NAME.CLINICAL_ANALYTIC,
      MODULE_NAME.HEALTH_POPULATION_ANALYTIC,
      MODULE_NAME.AXIOM_INSIGHT,
      MODULE_NAME.FINANCIAL_ANALYTIC,
    ]),
    showCardView: false,
    noSplitScreen: true,
  },
  {
    key: ROUTE_NAME.PSYCH_NOTE,
    text: t('Psych Progress Note'),
    iconProps: { iconName: 'QuickNote' },
    subMenu: generatePageSections([
      MODULE_NAME.PROGRESS_NOTE_AUTO_SAVE,
      MODULE_NAME.PROGRESS_NOTE_HISTORY,
      MODULE_NAME.PROGRESS_NOTE_PSYCH,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.PCP_NOTE,
    text: t('PCP Progress Note'),
    iconProps: { iconName: 'QuickNote' },
    subMenu: generatePageSections([
      MODULE_NAME.PROGRESS_NOTE_AUTO_SAVE,
      MODULE_NAME.PROGRESS_NOTE_HISTORY,
      MODULE_NAME.PROGRESS_NOTE_PCP,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.GROUP_NOTE,
    text: t('Group Note'),
    subMenu: generatePageSections([
      MODULE_NAME.PROGRESS_NOTE_AUTO_SAVE,
      MODULE_NAME.PROGRESS_NOTE_HISTORY,
      MODULE_NAME.PROGRESS_NOTE_GROUP,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.CM_NOTE,
    text: t('CM Progress Note'),
    iconProps: { iconName: 'QuickNote' },
    subMenu: generatePageSections([
      MODULE_NAME.PROGRESS_NOTE_CM,
      MODULE_NAME.PROGRESS_NOTE_AUTO_SAVE,
      MODULE_NAME.PROGRESS_NOTE_HISTORY,
      MODULE_NAME.CM3_CONTACT,
      MODULE_NAME.CM3_APPOINTMENT,
      MODULE_NAME.CM3_PRESCIPTION,
      MODULE_NAME.CM3_GROUP,
      MODULE_NAME.CM3_DEMOGRAPHIC,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.DYNAMIC_PROGRESS_NOTE,
    text: t('Dynamic Progress Notes'),
    iconProps: { iconName: 'NoteForward' },
    subMenu: generatePageSections([
      MODULE_NAME.DYNAMIC_PROGRESS_NOTE_HISTORY,
      MODULE_NAME.DYNAMIC_PROGRESS_NOTE_SELECTION,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.SERVICE_PLAN,
    text: t('Service Plan'),
    iconProps: { iconName: 'ServicePlan' },
    subMenu: generatePageSections([
      MODULE_NAME.SERVICE_PLAN_HISTORY,
      MODULE_NAME.SERVICE_PLAN_AUTO_SAVE,
      MODULE_NAME.SERVICE_PLAN_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.DYNAMIC_SERVICE_PLAN,
    text: t('Dynamic Service Plan'),
    iconProps: { iconName: 'ServicePlan' },
    subMenu: generatePageSections([MODULE_NAME.DYNAMIC_SERVICE_PLAN_HISTORY]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.SDOH,
    text: t('SDOH'),
    iconProps: { iconName: 'ServicePlan' },
    subMenu: generatePageSections([MODULE_NAME.SDOH_HISTORY, MODULE_NAME.SDOH_INPUT]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.PCP_COMMUNICATION,
    text: t('PCP Communication'),
    iconProps: { iconName: 'ServicePlan' },
    subMenu: generatePageSections([
      MODULE_NAME.PCP_COMMUNICATION_HEADER,
      MODULE_NAME.PCP_COMMUNICATION_HISTORY,
      MODULE_NAME.PCP_COMMUNICATION_INPUT,
      MODULE_NAME.PCP_COMMUNICATION_DIAGNOSIS,
      MODULE_NAME.PCP_COMMUNICATION_MEDICATIONS,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.INPATIENT,
    text: t('Inpatient Module'),
    iconProps: { iconName: 'CheckListText' },
    showCardView: true,
  },
  {
    key: ROUTE_NAME.RESIDENTIAL,
    text: t('Residential'),
    iconProps: { iconName: 'Group' },
    showCardView: true,
  },
  {
    key: ROUTE_NAME.LAB_ORDER,
    text: t('Laboratory'),
    iconProps: { iconName: 'BucketColor' },
    showCardView: true,
    subMenu: generatePageSections([MODULE_NAME.LAB_HISTORY, MODULE_NAME.LAB_INPUT]),
  },
  {
    key: ROUTE_NAME.E_MAR,
    text: t('eMar'),
    iconProps: { iconName: 'SingleColumnEdit' },
    showCardView: true,
    subMenu: generatePageSections([MODULE_NAME.EMAR_HISTORY, MODULE_NAME.EMAR_INPUT]),
  },
  {
    key: ROUTE_NAME.DYNAMIC_REPORT,
    text: t('Dynamic Reports'),
    iconProps: { iconName: 'PageEdit' },
    subMenu: generatePageSections([
      MODULE_NAME.REPORT_MODULE,
      MODULE_NAME.DYNAMIC_REPORT_FORM,
      MODULE_NAME.RECENT_REPORTS,
      MODULE_NAME.RESPONSE_FILES,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.ADMIN_SPECIFIC_SECURITY,
    text: t('Specific Security'),
    iconProps: { iconName: 'PageData' },
    subMenu: generatePageSections([
      MODULE_NAME.SECURITY_USER_ROLES_MANAGEMENT,
      MODULE_NAME.SECURITY_ROLES_MANAGEMENT,
      MODULE_NAME.SECURITY_SENSITIVITY_CLASSIFICATION,
    ]),
  },
  {
    key: ROUTE_NAME.QUICK_ENROLLMENT,
    text: t('Quick Enrollment'),
    iconProps: { iconName: 'PageEdit' },
    subMenu: generatePageSections([
      MODULE_NAME.QUICK_ENROLLMENT_SEARCH,
      MODULE_NAME.QUICK_ENROLLMENT_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.HR,
    text: t('HR - Employee Enrollment'),
    iconProps: { iconName: 'OpenEnrollment' },
    subMenu: generatePageSections([
      MODULE_NAME.HR_ENROLLMENT_SEARCH,
      MODULE_NAME.HR_ENROLLMENT_FORM,
    ]),
  },
  {
    key: ROUTE_NAME.PATIENT_ASSIGNMENT,
    text: t('Patient Assignment'),
    iconProps: { iconName: 'OpenEnrollment' },
    subMenu: generatePageSections([
      MODULE_NAME.PATIENT_ASSIGNMENT_GROUP,
      MODULE_NAME.PATIENT_ASSIGNMENT_PHARMACY,
      MODULE_NAME.PATIENT_ASSIGNMENT_PRIORITY,
      MODULE_NAME.PATIENT_ASSIGNMENT_SITE,
      MODULE_NAME.PATIENT_ASSIGNMENT_TEAM,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.CYBHI,
    text: t('CYBHI'),
    iconProps: { iconName: 'OpenEnrollment' },
    subMenu: generatePageSections([MODULE_NAME.CYBHI_CLIENT_DATA]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.COVER_SHEET,
    text: t('Cover Sheet'),
    iconProps: { iconName: 'OpenEnrollment' },
    subMenu: generatePageSections([
      MODULE_NAME.COVER_SHEET_PATIENT,
      MODULE_NAME.COVER_SHEET_ADDRESS,
      MODULE_NAME.COVER_SHEET_CONTACT,
      MODULE_NAME.COVER_SHEET_ADDITIONAL_INFO,
      MODULE_NAME.COVER_SHEET_CONSENT,
      MODULE_NAME.COVER_SHEET_PATIENT_PORTAL,
      // Pending
      // MODULE_NAME.COVER_SHEET_INSURANCE,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.PROBLEM_LIST,
    text: t('Problems List'),
    iconProps: { iconName: 'Hospital' },
    subMenu: generatePageSections([
      MODULE_NAME.PROBLEM_LIST_HISTORY,
      MODULE_NAME.PROBLEM_INPUT,
      MODULE_NAME.BEHAVIORAL_DIAGNOSIS_HISTORY,
      MODULE_NAME.PROGRAM_HISTORY,
      MODULE_NAME.PROGRAM_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.SCANNED_DOCUMENTS,
    text: t('Scanned Documents'),
    iconProps: { iconName: 'ServicePlan' },
    subMenu: generatePageSections([
      MODULE_NAME.SCAN_DOCUMENTS_HISTORY,
      MODULE_NAME.SCAN_DOCUMENTS_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.ENROLLMENT_HISTORY,
    text: t('Enrollment History'),
    iconProps: { iconName: 'OpenEnrollment' },
    subMenu: generatePageSections([
      MODULE_NAME.PATIENT_ENROLLMENT_INFO,
      MODULE_NAME.ENROLLMENT_HISTORY,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.SPECIALIST_REFERRAL,
    text: t('Specialist Referral'),
    iconProps: { iconName: 'FavoriteStar' },
    subMenu: generatePageSections([
      MODULE_NAME.SPECIALIST_REFERRAL_HISTORY,
      MODULE_NAME.SPECIALIST_REFERRAL_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.DAILY_EXTRACT,
    text: t('Daily Extract'),
    iconProps: { iconName: 'FavoriteStar' },
    subMenu: generatePageSections([MODULE_NAME.DAILY_EXTRACT]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.ENCOUNTER_FORM_MANAGEMENT,
    text: t('Encounter Form Service Editor'),
    subMenu: generatePageSections([
      MODULE_NAME.ENCOUNTER_FORM_MANAGEMENT_SITE,
      MODULE_NAME.ENCOUNTER_FORM_MANAGEMENT_PROGRAM,
      MODULE_NAME.ENCOUNTER_FORM_MANAGEMENT_CODE,
      MODULE_NAME.ENCOUNTER_FORM_MANAGEMENT_GRID,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.FOSTER_HOME,
    text: t('Foster Home'),
    subMenu: generatePageSections([
      MODULE_NAME.FOSTER_HOME_SEARCH,
      MODULE_NAME.FOSTER_HOME_REGISTER,
      MODULE_NAME.FOSTER_HOME_EDIT,
      MODULE_NAME.FOSTER_CHILD_ASSIGNMENT,
      MODULE_NAME.FOSTER_CARE_CASE_NOTE,
      MODULE_NAME.FOSTER_HOME_DASHBOARD,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.DEMOGRAPHIC,
    text: t('Demographic'),
    subMenu: generatePageSections([
      MODULE_NAME.DEMOGRAPHIC_HISTORY,
      MODULE_NAME.DEMOGRAPHIC_ENROLLMENT,
      MODULE_NAME.DEMOGRAPHIC_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.GROUP_EDITOR,
    text: t('Group Editor'),
    subMenu: generatePageSections([
      MODULE_NAME.GROUP_EDITOR_HISTORY,
      MODULE_NAME.GROUP_EDITOR_INPUT,
      MODULE_NAME.GROUP_EDITOR_PATIENT,
      MODULE_NAME.GROUP_EDITOR_PATIENT_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.IMMUNIZATION,
    text: t('Immunization'),
    subMenu: generatePageSections([
      MODULE_NAME.IMMUNIZATION_HISTORY,
      MODULE_NAME.IMMUNIZATION_REGISTRY_SETTINGS,
      MODULE_NAME.IMMUNIZATION_INPUT,
      MODULE_NAME.IMMUNIZATION_TIMELINE,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.SUPPORT,
    text: t('Support'),
    subMenu: generatePageSections([
      MODULE_NAME.SUPPORT_AUTOSAVE_DYNAMIC_FORM,
      MODULE_NAME.SUPPORT_AUTOSAVE_HX,
      MODULE_NAME.SUPPORT_ENROLL_REF,
      MODULE_NAME.SUPPORT_MENU_MANAGEMENT,
      MODULE_NAME.SUPPORT_NEW_ACCOUNT,
      MODULE_NAME.SUPPORT_PAGE_SECURITIES,
      MODULE_NAME.SUPPORT_PRESCRIPTIONS,
      MODULE_NAME.SUPPORT_PROGRESS_NOTES,
      MODULE_NAME.SUPPORT_RESET_PASSWORD,
      MODULE_NAME.SUPPORT_RIGHT_TRANSFER,
      MODULE_NAME.SUPPORT_SYSTEM_CONFIGURATION,
      MODULE_NAME.SUPPORT_LOOKUP_TABLE,
      MODULE_NAME.SUPPORT_BYPASS_RESET_MFA,
      MODULE_NAME.SUPPORT_DIAGNOSIS_CODE,
      MODULE_NAME.SUPPORT_SERVICE_PLAN,
      MODULE_NAME.SUPPORT_IBHIS,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.DATA_IMPORT,
    text: t('Data Import'),
    subMenu: generatePageSections([
      MODULE_NAME.DATA_IMPORT_HISTORY,
      MODULE_NAME.DATA_IMPORT_UPLOAD,
      MODULE_NAME.DATA_IMPORT_TABLE,
      MODULE_NAME.DATA_IMPORT_DIAGRAM,
      MODULE_NAME.DATA_IMPORT_RUNS_HISTORY,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.CLOSURE,
    text: t('Closure'),
    subMenu: generatePageSections([
      MODULE_NAME.CLOSURE_ENROLLMENT_HISTORY,
      MODULE_NAME.CLOSURE_HISTORY,
      MODULE_NAME.CLOSURE_EXTEND,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.EXAM_OPTIONS,
    text: t('Exam Options'),
    subMenu: generatePageSections([
      MODULE_NAME.EXAM_OPTIONS_TYPES,
      MODULE_NAME.EXAM_OPTIONS_EDIT,
      MODULE_NAME.EXAM_OPTIONS_OPTIONS,
      MODULE_NAME.EXAM_OPTIONS_OPTIONS_EDIT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.SERVICE_PLAN_FIELD_MANAGER,
    text: t('Service Plan Field Manager'),
    subMenu: generatePageSections([
      MODULE_NAME.SERVICE_PLAN_MAPPING_FIELDS,
      MODULE_NAME.SERVICE_PLAN_MAPPING_FIELD_INPUT,
      MODULE_NAME.SERVICE_PLAN_SERVICE_CODES,
      MODULE_NAME.SERVICE_PLAN_SERVICE_CODE_INPUT,
      MODULE_NAME.SERVICE_PLAN_FIELDS,
      MODULE_NAME.SERVICE_PLAN_FIELD_INPUT,
      MODULE_NAME.SERVICE_PLAN_SIGNATURE_INPUT,
      MODULE_NAME.SERVICE_PLAN_SIGNATURE_HISTORY,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.RADIOLOGY,
    text: t('Radiology'),
    subMenu: generatePageSections([MODULE_NAME.RADIOLOGY_HISTORY, MODULE_NAME.RADIOLOGY_INPUT]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.HEALTH_INFORMATION,
    text: t('Health Information'),
    subMenu: generatePageSections([
      MODULE_NAME.HEALTH_INFORMATION_MEDICAL_RECORDS_RELEASES,
      MODULE_NAME.HEALTH_INFORMATION_RELEASES,
      MODULE_NAME.HEALTH_INFORMATION_RELEASE_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.QUALITY_MANAGEMENT,
    text: t('Quality Management'),
    subMenu: generatePageSections([
      MODULE_NAME.QUALITY_MANAGEMENT_REPORT_SEARCH,
      MODULE_NAME.QUALITY_MANAGEMENT_REPORT_INPUT,
      MODULE_NAME.QUALITY_MANAGEMENT_CGA_SEARCH,
      MODULE_NAME.QUALITY_MANAGEMENT_CGA_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.CLINICAL_FORMS_SEARCH,
    text: t('Clinical Forms Search'),
    subMenu: generatePageSections([
      MODULE_NAME.CLINICAL_FORMS_SEARCH,
      MODULE_NAME.CLINICAL_FORMS_EMPLOYEES,
      MODULE_NAME.CLINICAL_FORMS_PATIENTS,
      MODULE_NAME.CLINICAL_FORMS_ENCOUNTERS,
      MODULE_NAME.CLINICAL_FORMS_DYNAMIC_FORMS,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.RE_ASSIGNMENT,
    text: t('Re-Assignment'),
    iconProps: { iconName: 'FavoriteStar' },
    subMenu: generatePageSections([MODULE_NAME.RE_ASSIGNMENT_FORMS]),
  },
  {
    key: ROUTE_NAME.PSYCH_EVALUATION_NOTE,
    text: t('Psych Evaluation Note'),
    subMenu: generatePageSections([
      MODULE_NAME.PSYCH_EVALUATION_NOTE_INPUT,
      MODULE_NAME.PROGRESS_NOTE_HISTORY,
      MODULE_NAME.PROGRESS_NOTE_AUTO_SAVE,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.PSYCHOTHERAPY_PROGRESS_NOTE,
    text: t('Psychotherapy Progress Note'),
    subMenu: generatePageSections([
      MODULE_NAME.PROGRESS_NOTE_HISTORY,
      MODULE_NAME.PSYCHOTHERAPY_NOTE_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.PAYOR_ASSIGNMENT,
    text: t('Payor Assignment'),
    subMenu: generatePageSections([
      MODULE_NAME.PAYOR_ASSIGNMENT_HISTORY,
      MODULE_NAME.PAYOR_ASSIGNMENT_INFORMATION,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.CRM,
    text: t('CRM - Client Relationship Management'),
    subMenu: generatePageSections([
      MODULE_NAME.CRM_DASHBOARD,
      MODULE_NAME.CRM_CLIENT,
      MODULE_NAME.CRM_ANALYTICS,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.PSYCHOSOCIAL_ASSESSMENT_NOTE,
    text: t('Psychosocial Assessment Note'),
    subMenu: generatePageSections([
      MODULE_NAME.PROGRESS_NOTE_HISTORY,
      MODULE_NAME.PSYCHOSOCIAL_ASSESSMENT_NOTE_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.INFORMED_CONSENT,
    text: t('Informed Consent'),
    subMenu: generatePageSections([
      MODULE_NAME.INFORMED_CONSENT_HISTORY,
      MODULE_NAME.INFORMED_CONSENT_INFO,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.GAD7,
    text: t('GAD-7'),
    subMenu: generatePageSections([
      MODULE_NAME.GAD_7_HISTORY,
      MODULE_NAME.GAD_7_FORM,
      MODULE_NAME.GAD_7_CHART,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.COURT_ORDERED_TREATMENT,
    text: t('COT - Court Ordered Treatment'),
    subMenu: generatePageSections([
      MODULE_NAME.COT_HISTORY,
      MODULE_NAME.COT_FORM,
      MODULE_NAME.COT_AMENDMENTS,
      MODULE_NAME.COT_JUDICIAL_REVIEWS,
      MODULE_NAME.COT_STATUS_REPORTS,
    ]),
    showCardView: false,
  },
  {
    key: ROUTE_NAME.PHQA,
    text: t('PHQ-A'),
    subMenu: generatePageSections([MODULE_NAME.PHQ_A_HISTORY, MODULE_NAME.PHQ_A_FORM]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.FUNDING_SOURCE,
    text: t('Funding Source'),
    subMenu: generatePageSections([
      MODULE_NAME.FUNDING_SOURCE_HISTORY,
      MODULE_NAME.FUNDING_SOURCE_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.MEDICATION_ASSISTED_TREATMENT,
    text: t('MAT'),
    subMenu: generatePageSections([
      MODULE_NAME.MAT_DASHBOARD,
      MODULE_NAME.UPCOMING_VISIT,
      MODULE_NAME.MAT_QUEUE_CENTER,
      MODULE_NAME.PENDING_INTAKE,
      MODULE_NAME.MAT_REPORTS,
      MODULE_NAME.MAT_PATIENT_CENTER,
      MODULE_NAME.MAT_INVENTORY,
      MODULE_NAME.MAT_FACILITY_SCHEDULE,
      MODULE_NAME.MAT_AUTO_SCHEDULE_CARE_PLANS,
      MODULE_NAME.MAT_MONITOR_DISPLAY_EDITOR,
    ]),
    showCardView: false,
  },
  {
    key: ROUTE_NAME.DISCHARGE_PLAN,
    text: t('Discharge Plan'),
    subMenu: generatePageSections([
      MODULE_NAME.DISCHARGE_PLAN_HISTORY,
      MODULE_NAME.INPUT_CLINICAL_DISCHARGE,
    ]),
  },
  {
    key: ROUTE_NAME.CINA,
    text: t('CINA'),
    subMenu: generatePageSections([MODULE_NAME.CINA_INPUT, MODULE_NAME.CINA_REPORTS]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.CIWA,
    text: t('CIWA'),
    subMenu: generatePageSections([MODULE_NAME.CIWA_INPUT, MODULE_NAME.CIWA_REPORTS]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.PRAPARE,
    text: t('PRAPARE'),
    subMenu: generatePageSections([MODULE_NAME.PRAPARE_HISTORY, MODULE_NAME.PRAPARE_INPUT]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.COWS,
    text: t('COWS'),
    subMenu: generatePageSections([MODULE_NAME.COWS_HISTORY, MODULE_NAME.COWS_INPUT]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.CALOCUS,
    text: t('CALOCUS - Child or Adolescent Evaluation'),
    subMenu: generatePageSections([
      MODULE_NAME.CALOCUS_HISTORY,
      MODULE_NAME.CALOCUS_INPUT,
      MODULE_NAME.CALOCUS_LEVEL_OF_CARE,
    ]),
  },
  {
    key: ROUTE_NAME.LOCUS,
    text: t('LOCUS'),
    subMenu: generatePageSections([
      MODULE_NAME.LOCUS_HISTORY,
      MODULE_NAME.LOCUS_INPUT,
      MODULE_NAME.LOCUS_LEVEL_OF_CARE,
    ]),
  },
  {
    key: ROUTE_NAME.ART_CFT,
    text: t('ART/CFT'),
    subMenu: generatePageSections([
      MODULE_NAME.ART_CFT_MEETING_SUMMARIES,
      MODULE_NAME.ART_CFT_AUTO_SAVE,
      MODULE_NAME.ART_CFT_STAFF_SUMMARIES,
      MODULE_NAME.ART_CFT_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.LAB_RESULT_ERROR,
    text: t('Lab Results Errors'),
    subMenu: generatePageSections([
      MODULE_NAME.LAB_RESULT_ERROR_HISTORY,
      MODULE_NAME.LAB_RESULT_ERROR_LAB_SEARCH,
      MODULE_NAME.LAB_RESULT_ERROR_PATIENT_SEARCH,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.INTERNAL_ORDERS,
    text: t('Internal Orders'),
    subMenu: generatePageSections([
      MODULE_NAME.INTERNAL_ORDER_ENROLLMENT,
      MODULE_NAME.INTERNAL_ORDER_HISTORY,
      MODULE_NAME.INTERNAL_ORDER_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.ENCOUNTER_ENTRY,
    text: t('Encounter Entry'),
    subMenu: generatePageSections([
      MODULE_NAME.ENCOUNTER_ENTRY_PATIENT,
      MODULE_NAME.ENCOUNTER_ENTRY_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.ENGAGEMENT_SESSION_NOTE,
    text: t('Engagement Session Note'),
    subMenu: generatePageSections([
      MODULE_NAME.PROGRESS_NOTE_HISTORY,
      MODULE_NAME.PROGRESS_NOTE_AUTO_SAVE,
      MODULE_NAME.ENGAGEMENT_SESSION_NOTE_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.COMMUNICATIONS_CENTER,
    text: t('Communications Center'),
    subMenu: generatePageSections([MODULE_NAME.COMMUNICATIONS_CENTER]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.ENGAGEMENT,
    text: t('Engagement'),
    subMenu: generatePageSections([MODULE_NAME.ENGAGEMENT_HISTORY, MODULE_NAME.ENGAGEMENT_INPUT]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.PATIENT_EMPLOYEE_RESTRICTION,
    text: t('Patient/Employee Restriction'),
    subMenu: generatePageSections([
      MODULE_NAME.PATIENT_ROLE_RESTRICTION,
      MODULE_NAME.PATIENT_EMPLOYEE_RESTRICTION,
    ]),
  },
  {
    key: ROUTE_NAME.INJECTION,
    text: t('Injection'),
    subMenu: generatePageSections([MODULE_NAME.INJECTION_HISTORY, MODULE_NAME.INJECTION_INPUT]),
  },
  {
    key: ROUTE_NAME.PENDING_PATIENT_REQUESTS,
    text: t('Pending Patient Requests'),
    subMenu: generatePageSections([MODULE_NAME.PENDING_PATIENT_HISTORY]),
  },
  {
    key: ROUTE_NAME.ALLERGY,
    text: t('Allergy'),
    subMenu: generatePageSections([MODULE_NAME.ALLERGY_HISTORY, MODULE_NAME.ALLERGY_INPUT]),
  },
  {
    key: ROUTE_NAME.PHQ,
    text: t('PHQ'),
    subMenu: generatePageSections([
      MODULE_NAME.PHQ_HISTORY,
      MODULE_NAME.PHQ_FORM,
      MODULE_NAME.PHQ_CHART,
    ]),
  },
  {
    key: ROUTE_NAME.VITALS,
    text: t('Vitals'),
    subMenu: generatePageSections([
      MODULE_NAME.VITAL_HISTORY,
      MODULE_NAME.VITAL_FORM,
      MODULE_NAME.VITAL_CHART,
    ]),
  },
  {
    key: ROUTE_NAME.PCL5,
    text: t('PCL-5'),
    subMenu: generatePageSections([
      MODULE_NAME.PCL5_HISTORY,
      MODULE_NAME.PCL5_FORM,
      MODULE_NAME.PCL5_CHART,
    ]),
  },
  {
    key: ROUTE_NAME.ACE,
    text: t('ACE'),
    subMenu: generatePageSections([
      MODULE_NAME.ACE_HISTORY,
      MODULE_NAME.ACE_FORM,
      MODULE_NAME.ACE_CHART,
    ]),
  },
  {
    key: ROUTE_NAME.AUDIT,
    text: t('AUDIT'),
    subMenu: generatePageSections([
      MODULE_NAME.AUDIT_HISTORY,
      MODULE_NAME.AUDIT_FORM,
      MODULE_NAME.AUDIT_CHART,
    ]),
  },
  {
    key: ROUTE_NAME.PC_PTSD,
    text: t('PC-PTSD'),
    subMenu: generatePageSections([
      MODULE_NAME.PC_PTSD_HISTORY,
      MODULE_NAME.PC_PTSD_FORM,
      MODULE_NAME.PC_PTSD_CHART,
    ]),
  },
  {
    key: ROUTE_NAME.SUICIDE_SEVERITY_RATING_SCALE,
    text: t('Detailed Columbia - Suicide Severity Rating Scale'),
    subMenu: generatePageSections([
      MODULE_NAME.SUICIDE_SEVERITY_RATING_SCALE_HISTORY,
      MODULE_NAME.SUICIDE_SEVERITY_RATING_SCALE_FORM,
    ]),
  },
  {
    key: ROUTE_NAME.TIME_BLOCK_REASON,
    text: t('Time Block Reason'),
    subMenu: generatePageSections([
      MODULE_NAME.TIME_BLOCKED_REASON_HISTORY,
      MODULE_NAME.SCHEDULE_REASON_LIST,
    ]),
  },
  {
    key: ROUTE_NAME.IBHIS,
    text: t('IBHIS'),
    subMenu: generatePageSections([
      MODULE_NAME.IBHIS_SEARCH,
      MODULE_NAME.IBHIS_UPDATE,
      MODULE_NAME.IBHIS_SERVICE_REQUEST,
      MODULE_NAME.IBHIS_CLIENT_DATA,
      MODULE_NAME.IBHIS_LOCUS,
      MODULE_NAME.IBHIS_CANS,
      MODULE_NAME.IBHIS_PSC,
      MODULE_NAME.IBHIS_REPORT,
      MODULE_NAME.IBHIS_CSI_ASSESSMENT,
      MODULE_NAME.IBHIS_SYNC_HISTORY,
    ]),
  },
  {
    key: ROUTE_NAME.ANNOUNCEMENTS,
    text: t('Announcements Center'),
    subMenu: generatePageSections([
      MODULE_NAME.ANNOUNCEMENTS_HISTORY,
      MODULE_NAME.ANNOUNCEMENTS_INPUT,
    ]),
  },
  {
    key: ROUTE_NAME.NURSE_PROGRESS_NOTE,
    text: t('Nurse Progress Note'),
    subMenu: generatePageSections([
      MODULE_NAME.NURSE_PROGRESS_NOTE,
      MODULE_NAME.PROGRESS_NOTE_HISTORY,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.FAMILY_HEALTH,
    text: t('Family Health'),
    subMenu: generatePageSections([
      MODULE_NAME.FAMILY_HEALTH_HISTORY,
      MODULE_NAME.FAMILY_HEALTH_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.INPATIENT_NOTE,
    text: t('Inpatient Note'),
    subMenu: generatePageSections([
      MODULE_NAME.PROGRESS_NOTE_HISTORY,
      MODULE_NAME.INPATIENT_NOTE_INPUT,
      MODULE_NAME.PROGRESS_NOTE_AUTO_SAVE,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.PLACEMENT,
    text: t('Out of Home Placement'),
    subMenu: generatePageSections([MODULE_NAME.PLACEMENT_HISTORY, MODULE_NAME.PLACEMENT_INPUT]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.IMPLANTABLE_DEVICES,
    text: t('Implantable Devices'),
    subMenu: generatePageSections([
      MODULE_NAME.IMPLANTABLE_DEVICE_HISTORY,
      MODULE_NAME.IMPLANTABLE_DEVICE_ENTRY,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.PATIENT_PROCEDURE,
    text: t('Patient Procedure'),
    subMenu: generatePageSections([
      MODULE_NAME.PATIENT_PROCEDURES_HISTORY,
      MODULE_NAME.PATIENT_PROCEDURE_INPUT,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.WHO_ASSIST,
    text: t('WHO - ASSIST'),
    subMenu: generatePageSections([MODULE_NAME.WHO_ASSIST_HISTORY, MODULE_NAME.WHO_ASSIST_FORM]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.DIRECT_MESSAGING,
    text: t('Direct Messaging'),
    subMenu: generatePageSections([MODULE_NAME.DIRECT_MESSAGING, MODULE_NAME.DIRECT_MESSAGING_ADD]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.SAFET,
    text: t('SAFET'),
    subMenu: generatePageSections([
      MODULE_NAME.SUICIDE_ASSESSMENT_FIVE_STEP_EVALUATION_AND_TRIAGE_HISTORY,
      MODULE_NAME.SUICIDE_ASSESSMENT_FIVE_STEP_EVALUATION_AND_TRIAGE_INPUT,
    ]),
  },
  {
    key: ROUTE_NAME.SUICIDE_RISK_SCREENING,
    text: t('Suicide Risk Screening'),
    subMenu: generatePageSections([
      MODULE_NAME.SUICIDE_RISK_SCREENING_HISTORY,
      MODULE_NAME.SUICIDE_RISK_SCREENING_FORM,
    ]),
    showCardView: true,
  },
  {
    key: ROUTE_NAME.TRIAGE_OBSERVATION,
    text: t('Triage Observation'),
    subMenu: [],
    showCardView: true,
  },
  {
    key: ROUTE_NAME.QUESTIONNAIRE,
    text: t('Questionnaire'),
    subMenu: [],
    showCardView: true,
  },
];

const legacyModules = [
  {
    rootPage: ROUTE_NAME.PRESCRIPTION,
    key: 'CurrentMedication',
    text: t('Current Rx'),
    Component: CurrentRxTable,
    icon: 'assets/images/prescriptions.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.PRESCRIPTION,
    key: 'OtherAgencyMedication',
    text: t('Other Agency Rx'),
    Component: OtherAgencyRxTable,
    icon: 'assets/images/other-agency-rx.png',
    strict: true,
  },
  {
    rootPage: ROUTE_NAME.VITALS,
    key: 'VitalsHistory',
    text: t('Vital History'),
    icon: 'assets/images/modern-history.png',
    Component: VitalsHistory,
  },
];

const MODULE = moduleMapping.reduce(
  (acc, item) => ({
    ...acc,
    [item.key]: item.Component,
  }),
  {},
);

export const LEGACY_MODULE = legacyModules.reduce(
  (acc, item) => ({
    ...acc,
    [item.key]: item.Component,
  }),
  {},
);

const PAGE = mockNavigatorItems.reduce((acc, item) => ({ ...acc, [item.key]: item }), {});

const SECURITY_FUNCTIONS = {
  GLOBAL_TABS_SET: 'GlobalTabsSet',
  HR_COMMENT: 'hr-comment',
  HR_EDIT: 'hr-edit',
  HR_PHOTO: 'hr-photo',
  HR_EDUCATION_LICENSE: 'hr-education-license',
  HR_EMPLOYMENT_HIRE_INFORMATION: 'hr-employment-hire-information',
  HR_SPECIALTIES: 'hr-specialties',
  HR_POSITIONS: 'hr-positions',
  HR_IDENTIFIER: 'hr-identifier',
  HR_SIGNATURE: 'hr-signature',
  HR_PSYCHIATRIST: 'hr-psychiatrist',
  HR_ROLES: 'hr-roles',
  HR_COPY_ROLES: 'hr-copy-roles',
  INPATIENT_ADMISSION_ORDERS: 'inpatient-admission-orders',
  INPATIENT_ANCILLARY_OREDERS: 'inpatient-ancillary-orders',
  INAPATIENT_COMPREHENSIVE_NURSING_ASSESSMENT: 'inpatient-comprehensive-nursing-assessment',
  INAPATIENT_HEATH_PHYSICAL_NOTE: 'inpatient-healthphysicalnote',
  INPATIENT_NURSING_ADMISSION_ASSESSMENT: 'inpatient-nursing-admission-assessment',
  INPATIENT_STAFF_DAILY_NOTE: 'inpatient-staff-daily-note',
  INPATIENT_BHT_ROUNDING_SHEET: 'inpatient-bht-rounding-sheet-inpatient',
  INPATIENT_COMMENTS: 'inpatient-comments',
  INPATIENT_PATIENT_VALUABLES: 'inpatient-patient-valuables',
  INPATIENT_RESTRAINT_SECLUSION: 'inpatient-restraint-seclusion',
  RESIDENTIAL_NURSE_DAILY_NOTE: 'residential-nurse-daily-note',
  RESIDENTIAL_DAILY_STAFF_NOTE: 'residential-daily-staff-note',
  RESIDENTIAL_BHT_ROUNDING_SHEET: 'residential-bht-rounding-sheet-residential',
  DYNAMIC_PROGRESS_NOTE_BUILDER: 'DynamicProgressNoteBuilder',
  FORM_EDIT: 'FormEdit',
  HR_PROGRAMS: 'hr-programs',
  DYNAMIC_ATTRIBUTES: 'DynamicAttributes',
  IBHIS_SYNC_HISTORY_ADMIN: 'ibhisSyncHistoryAdmin',
};

export { mockNavigatorItems, moduleMapping, MODULE_DATA, MODULE, PAGE, SECURITY_FUNCTIONS };
