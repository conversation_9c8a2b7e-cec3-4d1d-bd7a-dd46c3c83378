import { COL<PERSON>_KEY, COLOR_SET, PRIMARY_COLOR_SET, PRIMARY_IMAGE_SET } from './colors';
import {
  CALENDAR_VIEW_KEY,
  SETTING_KEYS_DATE_FORMAT,
  SETTING_KEYS_FIRST_DAY,
  SETTING_KEYS_TIME_FORMAT,
} from './calendar';
import { AI_SETTING_KEY, SETTING_KEYS } from './settingKeys';
import { WELCOME_SHOW_OPTIONS, SECTION_STYLE, WELCOME_KEY } from 'constants/welcome';
import { t } from 'utils/string';

const LAYOUT_SETTINGS = {
  SIDEBAR: 'SideBar',
  TABS: 'Tabs',
  BOTH: 'Both',
  LEFT: 'left',
  RIGHT: 'right',
};

const BACKGROUND_TYPE = {
  COVER: 'cover',
  FIXED: 'fixed',
  CONTAIN: 'contain',
};

const SPLIT_SCREEN_SETTINGS = {
  HORIZONTAL: 'horizontal',
  VERTICAL: 'vertical',
};

const HEADER_ITEMS = {
  PATIENT_ATTRIBUTE: {
    key: 'PATIENT_ATTRIBUTE',
    text: 'Patient Attribute',
  },
  PATIENT_PROFILE_IMAGE: {
    key: 'PATIENT_PROFILE_IMAGE',
    text: 'Patient Profile Image',
  },
  PATIENT_INFORMATION: {
    key: 'PATIENT_INFORMATION',
    text: 'Patient Information',
  },
  PATIENT_ADDITIONAL_INFORMATION: {
    key: 'PATIENT_ADDITIONAL_INFORMATION',
    text: 'Patient Additional Information',
  },
  SEARCH: {
    key: 'SEARCH',
    text: 'Search',
  },
  APPOINTMENT_REMINDER: {
    key: 'APPOINTMENT_REMINDER',
    text: 'Appointment Reminder',
  },
  WEATHER: {
    key: 'WEATHER',
    text: 'Weather',
  },
  NOTIFICATION: {
    key: 'NOTIFICATION',
    text: 'Notification',
  },
  CALENDAR: {
    key: 'CALENDAR',
    text: 'Calendar',
  },
  CHAT: {
    key: 'CHAT',
    text: 'Chat',
  },
  RPA: {
    key: 'RPA',
    text: 'Automation',
  },
  ACCESSIBILITY: {
    key: 'ACCESSIBILITY',
    text: 'Accessibility',
  },
  AXIOM_AI: {
    key: 'AXIOM_AI',
    text: 'Axiom Virtual Assistance',
  },
};

const PATIENT_INFORMATION_BAR_ITEMS = {
  BODY_METRICS: 'BODY_METRICS',
  ALLERGIES: 'ALLERGIES',
  VITALS: 'VITALS',
  PROBLEMS_LIST: 'PROBLEMS_LIST',
  DIAGNOSIS: 'DIAGNOSIS',
  MEDICATIONS: 'MEDICATIONS',
  WORKLIST: 'WORKLIST',
};

const QUICK_ACCESS_ITEMS = {
  WELCOME: {
    key: 'welcome-page',
    text: 'Welcome Page',
  },
  CALENDAR: {
    key: 'calendar',
    text: 'Calendar',
  },
  CRITICAL_INFO: {
    key: 'critical-info',
    text: 'Critical Info',
  },
  PENDING_NOTIFICATION: {
    key: 'pending-notification',
    text: 'Pending Notifications',
  },
  PRODUCTIVITY: {
    key: 'productivity',
    text: 'Productivity',
  },
  PORTAL: {
    key: 'portal',
    text: 'Portal',
  },
  FLOW_SHEET: {
    key: 'flow-sheet',
    text: 'Flow Sheet',
  },
  SPLIT_SCREEN: {
    key: 'split-screen',
    text: 'Split Screen',
  },
};

const SECTION_STYLE_OPTIONS = {
  DEFAULT: 'Default',
  CLASSIC: 'Classic',
  MODERN: 'Modern',
  CREATIVE: 'Creative',
  LEFT: 'Left',
};

const HEADER_SIZE_OPTIONS = {
  NORMAL: 'Normal',
  MEDIUM: 'Medium',
  LARGE: 'Large',
};

const ACCESSIBILITY_KEYS = {
  CONTENT_SCALING: 'CONTENT_SCALING',
  FONT_SCALING: 'FONT_SCALING',
  HIGHLIGHT_TITLES: 'HIGHLIGHT_TITLES',
  HIGHLIGHT_BUTTONS: 'HIGHLIGHT_BUTTONS',
  TEXT_MAGNIFIER: 'TEXT_MAGNIFIER',
  CHANGE_FONT: 'CHANGE_FONT',
  // Color
  COLOR_SETTING: 'COLOR_SETTING',
  DARK_CONTRAST: 'DARK_CONTRAST',
  LIGHT_CONTRAST: 'LIGHT_CONTRAST',
  HIGH_CONTRAST: 'HIGH_CONTRAST',
  HIGH_SATURATION: 'HIGH_SATURATION',
  LOW_SATURATION: 'LOW_SATURATION',
  MONOCHROME: 'MONOCHROME',

  MUTE: 'MUTE',
  HIDE_IMAGES: 'HIDE_IMAGES',
  HIGHLIGHT_HOVER: 'HIGHLIGHT_HOVER',
  FOCUS_MASK: 'FOCUS_MASK',
  HIDE_ANIMATION: 'HIDE_ANIMATION',
  BIG_CURSOR: 'BIG_CURSOR',
};

const ACCESSIBILITY_TEMPLATE_KEY = {
  DEFAULT: 'DEFAULT',
  SEIZURE_SAFE: 'SEIZURE_SAFE',
  VISION_IMPAIRED: 'VISION_IMPAIRED',
  ADHD_FRIENDLY: 'ADHD_FRIENDLY',
  CONGNITIVE_DISABILITY: 'CONGNITIVE_DISABILITY',
  KEYBOARD_NAVIGATION: 'KEYBOARD_NAVIGATION',
  BLIND_USERS: 'BLIND_USERS',
};

const ACCESSIBILITY_TEMPLATES = {
  [ACCESSIBILITY_TEMPLATE_KEY.DEFAULT]: {},
  [ACCESSIBILITY_TEMPLATE_KEY.SEIZURE_SAFE]: {
    [ACCESSIBILITY_KEYS.COLOR_SETTING]: ACCESSIBILITY_KEYS.LOW_SATURATION,
    [ACCESSIBILITY_KEYS.HIDE_ANIMATION]: true,
  },
  [ACCESSIBILITY_TEMPLATE_KEY.VISION_IMPAIRED]: {
    [ACCESSIBILITY_KEYS.CONTENT_SCALING]: 1.2,
    [ACCESSIBILITY_KEYS.COLOR_SETTING]: ACCESSIBILITY_KEYS.HIGH_SATURATION,
  },
  [ACCESSIBILITY_TEMPLATE_KEY.ADHD_FRIENDLY]: {
    [ACCESSIBILITY_KEYS.CONTENT_SCALING]: 1.1,
    [ACCESSIBILITY_KEYS.COLOR_SETTING]: ACCESSIBILITY_KEYS.HIGH_SATURATION,
    [ACCESSIBILITY_KEYS.HIDE_ANIMATION]: true,
    [ACCESSIBILITY_KEYS.FOCUS_MASK]: true,
  },
  [ACCESSIBILITY_TEMPLATE_KEY.CONGNITIVE_DISABILITY]: {
    [ACCESSIBILITY_KEYS.CONTENT_SCALING]: 1.1,
    [ACCESSIBILITY_KEYS.HIGHLIGHT_TITLES]: true,
    [ACCESSIBILITY_KEYS.HIGHLIGHT_BUTTONS]: true,
    [ACCESSIBILITY_KEYS.HIDE_ANIMATION]: true,
  },
  [ACCESSIBILITY_TEMPLATE_KEY.KEYBOARD_NAVIGATION]: {},
  [ACCESSIBILITY_TEMPLATE_KEY.BLIND_USERS]: {},
};

const SELECTION_BUTTONS = [
  { text: '7', value: 7 * 24 * 60 * 60 * 1000 },
  { text: '30', value: 30 * 24 * 60 * 60 * 1000 },
];

const DEFAULT_SETTINGS = {
  [SETTING_KEYS.THEME]: COLOR_KEY.PURPLE,
  [SETTING_KEYS.BG_IMAGE]: '',
  [SETTING_KEYS.MAIN_COLOR]: PRIMARY_COLOR_SET[COLOR_KEY.PURPLE],
  [SETTING_KEYS.CUSTOM_THEME_LIST]: COLOR_SET,
  [SETTING_KEYS.DARK_MODE]: false,
  [SETTING_KEYS.SHOW_QUICKACCESS_ICONS_ON]: LAYOUT_SETTINGS.RIGHT,
  [SETTING_KEYS.SHOW_NAVIGATION_AS]: LAYOUT_SETTINGS.BOTH,
  [SETTING_KEYS.SHOW_QUICKACCESS_PANEL_AS]: LAYOUT_SETTINGS.BOTH,
  [SETTING_KEYS.LANDING_PAGE]: '/appointment',
  [SETTING_KEYS.USERNAME]: '',
  [SETTING_KEYS.DOB]: new Date(),
  [SETTING_KEYS.PHONE]: '',
  [SETTING_KEYS.EMAIL]: '',
  [SETTING_KEYS.SHOW_SPLIT_SCREEN_AS]: SPLIT_SCREEN_SETTINGS.VERTICAL,
  [SETTING_KEYS.TIME_FORMAT]: SETTING_KEYS_TIME_FORMAT[0].key,
  [SETTING_KEYS.DATE_FORMAT]: SETTING_KEYS_DATE_FORMAT[0].key,
  [SETTING_KEYS.FIRST_DATE_OF_WEEK]: SETTING_KEYS_FIRST_DAY[0].key,
  [SETTING_KEYS.BACKGROUND_TYPE]: BACKGROUND_TYPE.COVER,
  [SETTING_KEYS.WELCOME_WAY_SHOW]: WELCOME_SHOW_OPTIONS[0]?.key,
  [SETTING_KEYS.WELCOME_ITEMS]: Object.values(WELCOME_KEY),
  [SETTING_KEYS.WELCOME_STYLE]: SECTION_STYLE.CREATIVE,
  [SETTING_KEYS.IS_TUTORIAL]: true,
  [SETTING_KEYS.CALENDAR_VIEW]: CALENDAR_VIEW_KEY.LIST_VIEW,
  [SETTING_KEYS.TEMPERATURE_IN_F]: true,
  [SETTING_KEYS.HEADER_ITEMS]: [
    HEADER_ITEMS.PATIENT_ATTRIBUTE.key,
    HEADER_ITEMS.PATIENT_PROFILE_IMAGE.key,
    HEADER_ITEMS.PATIENT_INFORMATION.key,
    HEADER_ITEMS.PATIENT_ADDITIONAL_INFORMATION.key,
    HEADER_ITEMS.SEARCH.key,
    HEADER_ITEMS.APPOINTMENT_REMINDER.key,
    HEADER_ITEMS.WEATHER.key,
    HEADER_ITEMS.CALENDAR.key,
    HEADER_ITEMS.AXIOM_AI.key,
    HEADER_ITEMS.CHAT.key,
    HEADER_ITEMS.RPA.key,
    HEADER_ITEMS.ACCESSIBILITY.key,
    HEADER_ITEMS.NOTIFICATION.key,
  ],
  [SETTING_KEYS.HIDE_OTHER_PATIENTS]: false,
  [SETTING_KEYS.SECTION_STYLE]: SECTION_STYLE_OPTIONS.DEFAULT,
  [SETTING_KEYS.SECTION_STYLE_IMAGE]: PRIMARY_IMAGE_SET,
  [SETTING_KEYS.QUICK_ACCESS_ITEMS]: [
    QUICK_ACCESS_ITEMS.WELCOME.key,
    QUICK_ACCESS_ITEMS.CALENDAR.key,
    QUICK_ACCESS_ITEMS.CRITICAL_INFO.key,
    QUICK_ACCESS_ITEMS.PENDING_NOTIFICATION.key,
    QUICK_ACCESS_ITEMS.PRODUCTIVITY.key,
    QUICK_ACCESS_ITEMS.PORTAL.key,
    QUICK_ACCESS_ITEMS.FLOW_SHEET.key,
    QUICK_ACCESS_ITEMS.SPLIT_SCREEN.key,
  ],
  [SETTING_KEYS.NAVIGATION_DETAIL]: '',
  [SETTING_KEYS.TABS_SET_ACTIVE]: '',
  [SETTING_KEYS.NAVIGATION_TABS_SET]: [
    { key: 'default', text: 'Default', description: 'All Tabs', active: true },
  ],
  [SETTING_KEYS.HEADER_SIZE]: HEADER_SIZE_OPTIONS.NORMAL,
  [SETTING_KEYS.LAYOUT_VIEW]: 'All',
  [SETTING_KEYS.FORM_SETTINGS]: [],
  [SETTING_KEYS.ACCESSIBILITY_PROFILE]: ACCESSIBILITY_TEMPLATE_KEY.DEFAULT,
  [SETTING_KEYS.ACCESSIBILITY_TEMPLATE]: ACCESSIBILITY_TEMPLATES,
  [SETTING_KEYS.CALENDAR_APPOINTMENT_LENGTH]: 30,
  [SETTING_KEYS.CALENDAR_START_DAY_AT]: 6,
  [SETTING_KEYS.CALENDAR_END_DAY_AT]: 18,
  [SETTING_KEYS.PRESCRIPTION_REFILL_ACTIONS]: ['30', '60', '90', '120'],
  [SETTING_KEYS.QUICK_DATE_SELECTION_BUTTONS]: SELECTION_BUTTONS,
  [SETTING_KEYS.PROGRESS_NOTE_DEFAULT_TYPE]: 'Current',
  [SETTING_KEYS.PROGRESS_NOTE_DEFAULT_DEFAULT_DAY_BACK]: 7,
  [SETTING_KEYS.RX_WARN_DRUG_DIAGNOSIS]: true,
  [SETTING_KEYS.RX_WARN_DRUG_EFFECTS]: true,
  [SETTING_KEYS.RX_WARN_DRUG_FOOD]: true,
  [SETTING_KEYS.RX_WARN_DRUG_PREGNANCY]: true,
  [SETTING_KEYS.RX_WARN_DRUG_WARNING]: true,
};

const DEFAULT_AI_SETTINGS = {
  [AI_SETTING_KEY.AUTO_FILL]: false,
  [AI_SETTING_KEY.REAL_TIME_TRANSCRIPTION]: false,
  [AI_SETTING_KEY.AUTO_PAUSE]: false,
  [AI_SETTING_KEY.AUTO_HIGHLIGHT]: true,
  [AI_SETTING_KEY.PATIENT_WORD_CHOICE]: 'Patient',
  [AI_SETTING_KEY.DOCTOR_WORD_CHOICE]: 'Doctor',
};

const NAVIGATION_TYPE = {
  PAGE: 'page',
  SETTINGS: 'settings',
  PAGE_NAME: 'pageName',
  FLOWSHEET: 'flowsheet',
  PATIENT: 'patient',
  DYNAMIC: 'dynamic',
};

const SECTION_DISPLAY_DESC = {
  [SECTION_STYLE_OPTIONS.CLASSIC]: 'Select the color to use for Classic sections',
  [SECTION_STYLE_OPTIONS.MODERN]: 'Select the color to use for Modern sections',
  [SECTION_STYLE_OPTIONS.CREATIVE]:
    'Upload images here to use for the Creative section style. AxiomEHR will rotate through each image.',
};

export const SETTING_COLLAPSE_SECTION = {
  THEME: 'THEME',
  HEADER_SIZE: 'HEADER_SIZE',
  HEADER_ITEMS: 'HEADER_ITEMS',
  ICONS_POSITION: 'ICONS_POSITION',
  QUICK_ACCESS_ITEMS: 'QUICK_ACCESS_ITEMS',
  QUICK_ACCESS_PANEL: 'QUICK_ACCESS_PANEL',
  RIBBON: 'RIBBON',
  TABS_BAR_DISPLAY: 'TABS_BAR_DISPLAY',
  SECTION_STYLE: 'SECTION_STYLE',
  SECTION_DISPLAY_SETTINGS: 'SECTION_DISPLAY_SETTINGS',
  FORM_SETTINGS: 'FORM_SETTINGS',
  ACCESSIBILITY_SETTINGS: 'ACCESSIBILITY_SETTINGS',
};

export const SETTING_MODAL_SECTION = {
  GENERAL: 'GENERAL',
  APPEARANCE: 'APPEARANCE',
  CALENDAR: 'CALENDAR',
  WEATHER: 'WEATHER',
  TEXT_REPLACEMENT: 'TEXT_REPLACEMENT',
  SHORTHAND: 'SHORTHAND',
  VIRTUAL_ASSISTANCE_SIDEBAR: 'VIRTUAL_ASSISTANCE_SIDEBAR',
};

export const SETTING_COLLAPSE_TEXT = {
  [SETTING_COLLAPSE_SECTION.THEME]: {
    Title: t('Theme'),
    SubTitle: t('Customize the appearance of Axiom'),
    DarkMode: t('Dark mode'),
    HighLevel: t('High level customize'),
  },
  [SETTING_COLLAPSE_SECTION.HEADER_SIZE]: {
    Title: t('Header Sizes'),
    SubTitle: t('Choose a size for the header'),
  },
  [SETTING_COLLAPSE_SECTION.HEADER_ITEMS]: {
    Title: t('Header Items'),
    SubTitle: t('Choose what appears in the header'),
    ShowPatientOnHeader: t('Hide other patients on the header'),
  },
  [SETTING_COLLAPSE_SECTION.ICONS_POSITION]: {
    Title: t('Icons Position'),
    SubTitle: t('Choose where you want the top icons to appear'),
    Left: t('Left Side'),
    Right: t('Right Side'),
  },
  [SETTING_COLLAPSE_SECTION.QUICK_ACCESS_ITEMS]: {
    Title: t('Quick Access Items'),
    SubTitle: t('Choose what appears in the quick access'),
  },
  [SETTING_COLLAPSE_SECTION.QUICK_ACCESS_PANEL]: {
    Title: t('Quick Access Panel'),
    SubTitle: t('Show Quick Access Panel as'),
    SideBarTitle: t('Sidebar'),
    SideBarSubTitle: t('Sidebar floats on the screen and can be moved around'),
    TabTitle: t('Tab'),
    TabSubtitle: t('Tab always stick on the top of the screen below navigation'),
    BothTitle: t('Both'),
    BothSubtitle: t('Show both on sidebar and tab'),
  },
  [SETTING_COLLAPSE_SECTION.RIBBON]: {
    Title: t('Tabs Bar / Ribbon'),
    SubTitle: t(
      'The tabs bar give fast access to frequently visited pages, and you can also create your own sets of tab bars.',
    ),
    Enabled: t('Enable Tabs Bar'),
  },
  [SETTING_COLLAPSE_SECTION.TABS_BAR_DISPLAY]: {
    Title: t('Tabs Bar Display'),
    SubTitle: t('Select how you want the tabs bar to display'),
    ShowFrequent: t('Show Frequently Visited Tabs'),
    ShowCustom: t('Show Custom Tabs Set'),
  },
  [SETTING_COLLAPSE_SECTION.SECTION_STYLE]: {
    Title: t('Section Style'),
    SubTitle: t('Select how you want sections to display'),
  },
  [SETTING_COLLAPSE_SECTION.SECTION_DISPLAY_SETTINGS]: {
    Title: t('Section Display Settings'),
    [SECTION_STYLE_OPTIONS.CLASSIC]: 'Select the color to use for Classic sections',
    [SECTION_STYLE_OPTIONS.MODERN]: 'Select the color to use for Modern sections',
    [SECTION_STYLE_OPTIONS.CREATIVE]:
      'Upload images here to use for the Creative section style. AxiomEHR will rotate through each image.',
  },
  [SETTING_COLLAPSE_SECTION.SECTION_STYLE]: {
    Title: t('Section Style'),
    SubTitle: t('Select how you want sections to display'),
  },
  [SETTING_COLLAPSE_SECTION.FORM_SETTINGS]: {
    Title: t('Form Settings'),
    SubTitle: t('Global settings for all form'),
    OnlyShow: t('Only Show Required Fields'),
    DataEntryTemplate: t('Data Entry Template'),
    Link: t('Settings For Specific Pages'),
  },
  [SETTING_COLLAPSE_SECTION.ACCESSIBILITY_SETTINGS]: {
    Title: t('Accessibility Settings'),
    SubTitle: t('Select the accessibility template that suit you best'),
    SeizureSafe: t('Seizure Safe'),
    SeizureSafeDesc: t('Remove animation, flashes & reduce color'),
    VisionImpairedProfile: t('Vision Impaired Profile'),
    VisionImpairedProfileDesc: t('Improves the appearance of the website'),
    ADHD: t('ADHD Friendly Profile'),
    ADHDDesc: t('Increased Concentration, Reduced Distractions'),
    Cognitive: t('Cognitive Disability Profile'),
    CognitiveDesc: t('Support with reading & focusing'),
    Keyboard: t('Keyboard Navigation'),
    KeyboardDesc: t('Navigate website with your keyboard'),
    BlindUsers: t('Blind Users (Screen Reader)'),
    BlindUsersDesc: t('Make the website compatible with screen readers'),
    Link: t('Show All Accessibility Settings'),
  },
};

export const SETTNG_MODAL_TEXT = {
  [SETTING_MODAL_SECTION.GENERAL]: {
    Title: t('General'),
    LandingPageTitle: t('Landing Page'),
    LandingPageSubTitle: t('Choose a landing page to open when you log in Axiom'),
    WelcomePopUpTitle: t('Welcome Pop Up'),
    WelcomePopUpSubTitle: t('Choose when it shows'),
    HeaderSizesTitle: t('Header Sizes'),
    HeaderSizesSubTitle: t('Choose a size for the header'),
    HeaderItemsTitle: t('Header Items'),
    HeaderItemsSubTitle: t('Choose what appears in the header'),
    ShowPatientOnHeader: t('Hide other patients on the header'),
    IconsPositionTitle: t('Icons Position'),
    IconsPositionSubTitle: t('Choose where you want the top icons to appear'),
    IconsPositionLeft: t('Left Side'),
    IconsPositionRight: t('Right Side'),
    QuickAccessPanelTitle: t('Quick Access Display'),
    QuickAccessPanelSubTitle: t("Pick how you'd like the quick access panel to show"),
    QuickAccessPanelSideBarTitle: t('Sidebar'),
    QuickAccessPanelSideBarSubTitle: t('Sidebar floats on the screen and can be moved around'),
    QuickAccessPanelTabTitle: t('Tab'),
    QuickAccessPanelTabSubtitle: t('Tab always stick on the top of the screen below navigation'),
    QuickAccessPanelBothTitle: t('Both'),
    QuickAccessPanelBothSubtitle: t('Show both on sidebar and tab'),
    QuickAccessTitle: t('Quick Access Items'),
    QuickAccessSubTitle: t('Choose what appears in the quick access'),
    RibbonTitle: t('Tabs Bar / Ribbon'),
    RibbonSubTitle: t(
      'The tabs bar give fast access to frequently visited pages, and you can also create your own sets of tab bars.',
    ),
    RibbonEnabled: t('Enable Tabs Bar'),
    TabsBarDisplayTitle: t('Tabs Bar Display'),
    TabsBarDisplaySubTitle: t('Select how you want the tabs bar to display'),
    TabsBarDisplayShowFrequent: t('Show Frequently Visited Tabs'),
    TabsBarDisplayShowCustom: t('Show Custom Tabs Set'),
    ProgressNoteTitle: t('Progress Note'),
    ProgressNoteType: t('Select default Progress Note type'),
    ProgressNoteDayBack: t('Select default Day back'),
    SplitScreenTitle: t('Split Screen'),
    SplitScreenSubTitle: t('Screen will be splited'),
    SplitScreenHorizontal: t('Horizontal'),
    SplitScreenVertical: t('Vertical'),
    RxWarningTitle: t('Prescription Warning'),
    RxWarningOptionDrugFood: t('Drug Food'),
    RxWarningOptionDrugWarn: t('Drug Warning'),
    RxWarningOptionDrugPregnancyWarn: t('Drug Pregnancy Warning'),
    RxWarningOptionDrugEffects: t('Drug Take Effects'),
    RxWarningOptionDrugDiagnosis: t('Drug Diagnosis'),
  },
  [SETTING_MODAL_SECTION.APPEARANCE]: {
    Title: t('Appearance'),
    ThemeTitle: t('Theme'),
    ThemeSubTitle: t('Customize the appearance of Axiom'),
    ThemeDarkMode: t('Dark mode'),
    ThemeHighLevel: t('High level customize'),
    SectionStyleTitle: t('Section Style'),
    SectionStyleSubTitle: t('Select how you want sections to display'),
    SectionDisplaySettings: t('Section Display Settings'),
    [SECTION_STYLE_OPTIONS.CLASSIC]: 'Select the color to use for Classic sections',
    [SECTION_STYLE_OPTIONS.MODERN]: 'Select the color to use for Modern sections',
    [SECTION_STYLE_OPTIONS.CREATIVE]:
      'Upload images here to use for the Creative section style. AxiomEHR will rotate through each image.',
  },
  [SETTING_MODAL_SECTION.CALENDAR]: {
    Title: t('Calendar'),
    DateFormat: t('Date Format'),
    TimeFormat: t('Time Format'),
    WeekBeginsOn: t('Week Begins On'),
    DefaultViewType: t('Default View Type'),
    TimeScaleIncrements: t('Time Scale Increments'),
    StartDayAt: t('Start Day At'),
    EndDayAt: t('End Day At'),
    DefaultAppointmentLength: t('Default Appointment Length'),
  },
  [SETTING_MODAL_SECTION.WEATHER]: {
    Title: t('Weather'),
    Display: t('Display temperature in'),
  },
  [SETTING_MODAL_SECTION.TEXT_REPLACEMENT]: {
    Title: t('Text Replacement'),
    Desc: t('Text replacement will be applied to all pages'),
  },
  [SETTING_MODAL_SECTION.SHORTHAND]: {
    Title: t('Shortcuts'),
    Prescription: t('Prescription Refill Actions List'),
    QuickButtons: t('Quick buttons'),
    TextShortcuts: t('Text Shortcuts'),
  },
  [SETTING_MODAL_SECTION.VIRTUAL_ASSISTANCE_SIDEBAR]: {
    Title: t('Virtual Assistance Sidebar'),
    RecordProcess: t('Record Process'),
    AutofillTitle: t('Auto fill'),
    AutofillDesc: t('Automatically fill in notes based on suggestions after the recording session'),
    TranscriptionTitle: t('Show Real-time Transcription'),
    TranscriptionDesc: t(
      'We recommend turning this feature off so you can fully focus on your session',
    ),
    AutoPauseTitle: t('Auto pause/continue record session'),
    AutopauseDesc: t(
      'The recording will automatically pause when there is no sound detected and seamlessly resume when the conversation continues.',
    ),
    TranscriptTitle: t('Transcript'),
    AutohighlightedTitle: t('Auto highlighted keywords'),
    AutohighlightedDesc: t('Automatically highlight important keywords'),
    PatientWordChoice: t('Patient Word Choice'),
    ClinicianWordChoice: t('Clinician Word Choice'),
  },
};

export {
  LAYOUT_SETTINGS,
  SPLIT_SCREEN_SETTINGS,
  DEFAULT_SETTINGS,
  BACKGROUND_TYPE,
  NAVIGATION_TYPE,
  DEFAULT_AI_SETTINGS,
  HEADER_ITEMS,
  PATIENT_INFORMATION_BAR_ITEMS,
  QUICK_ACCESS_ITEMS,
  SECTION_STYLE_OPTIONS,
  SECTION_DISPLAY_DESC,
  HEADER_SIZE_OPTIONS,
  ACCESSIBILITY_KEYS,
  ACCESSIBILITY_TEMPLATE_KEY,
  ACCESSIBILITY_TEMPLATES,
  SELECTION_BUTTONS,
};
