import React, { Fragment, useMemo } from 'react';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import { t } from 'utils/string';
import { DefaultButton, PrimaryButton } from '@fluentui/react';
import useModal from 'hooks/useModal';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import { useRequest } from 'hooks';
import { API_ENROLLMENT, API_METHOD } from 'constants/urlRequest';
import useHookForm from 'components/HookForm/useHookForm';
import { handleValidationBE } from 'utils/form';
import { toast } from 'react-toastify';
import WatchValues from 'components/HookForm/WatchValues';
import useClass from 'hooks/useClass';
import FieldDate from 'components/HookForm/FieldDate';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import { formatToFluentOptions } from 'utils';
import { TEXT_TEMPLATE } from 'constants/texts';
import moment from 'moment';
import HookFormProvider from 'components/HookForm/HookFormProvider';

function GridAssignModal({ patientId, requestGrid }) {
  const css = useClass();
  const { hideModal } = useModal();
  const apiDetail = useRequest({ url: API_ENROLLMENT.GRID, requiredParams: { patientId } });
  const apiSave = useRequest({ url: API_ENROLLMENT.GRID, method: API_METHOD.POST });
  const apiStatus = useRequest({ url: API_ENROLLMENT.GRID_STATUS, shouldCache: true });

  const enrollments = useMemo(
    () =>
      (apiDetail.data || []).map((i) => ({
        ...i,
        dlu: i.dlu ? moment(i.dlu).toDate() : null,
        projectedReviewDate:
          i.dlu && i.projectedReviewDateDisplay
            ? moment(i.projectedReviewDateDisplay).toDate()
            : null,
      })),
    [apiDetail?.data],
  );

  const defaultValues = {
    patientId,
    gridStatusId: '',
    gridStatusDate: null,
    enrollments,
  };

  const methods = useHookForm({ defaultValues, dependencies: { enrollments } });

  const onReset = () => methods.reset(defaultValues);

  const onSubmit = (values) => {
    const payload = { ...values };
    payload.enrollments = payload.enrollments.filter((i) => i.dlu);
    apiSave.request({
      payload,
      options: {
        onSuccess: () => {
          requestGrid({ queryString: { patientId } });
          toast.success(TEXT_TEMPLATE.UPDATE_SUCCESSFULLY('Grid'));
          hideModal();
        },
        onError: (err) => {
          handleValidationBE({ setError: methods.setError }, err);
        },
      },
    });
  };

  return (
    <HookFormProvider {...methods}>
      <ModalLayout
        title={t('Grid Assignment')}
        loading={apiSave.loading || apiDetail.loading}
        footerRightSide={
          <Fragment>
            <PrimaryButton text={t('Save')} onClick={methods.handleSubmit(onSubmit)} />
            <CalloutConfirmation onOk={onReset}>
              <DefaultButton text={t('Reset')} />
            </CalloutConfirmation>
            <DefaultButton text={t('Close')} onClick={hideModal} />
          </Fragment>
        }
      >
        <div className="p-16">
          <WatchValues name="enrollments">
            {(enrollments) => (
              <div className={css.cmClass.grid4Columns}>
                {enrollments?.map((enrollment, index) => (
                  <Fragment key={enrollment.cmGridTypeId}>
                    <FieldDate
                      title={`${enrollment.display} (Actual)`}
                      name={`enrollments.${index}.dlu`}
                      readOnly={!enrollment.allowManualEntry}
                      placeholder=""
                      isRenderRightSide={enrollment.allowManualEntry}
                      onChange={(value) => {
                        methods.setValue(
                          `enrollments.${index}.projectedReviewDate`,
                          moment(value)
                            .add(enrollment.interval, enrollment.intervalTypeDesc)
                            .toDate(),
                        );
                      }}
                    />
                    <FieldDate
                      title={t('(Project)')}
                      name={`enrollments.${index}.projectedReviewDate`}
                      readOnly
                      placeholder=""
                      isRenderRightSide={false}
                    />
                  </Fragment>
                ))}
                <FieldSelectorPrimary
                  title={t('Grid Status')}
                  name="gridStatusId"
                  options={formatToFluentOptions(apiStatus?.data)}
                />
                <FieldDate title={t('Grid Status Date')} name="gridStatusDate" placeholder="" />
              </div>
            )}
          </WatchValues>
        </div>
      </ModalLayout>
    </HookFormProvider>
  );
}

export default GridAssignModal;
