import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryButton, Stack, TextField } from '@fluentui/react';
import FieldSelectorPrimary from 'components/Form/FieldSelectorPrimary';
import { t } from 'utils/string';
import { formatToFluentOptions } from 'utils';
import MyField from 'components/Form/Field';
import moment from 'moment';
import { useNavigator, useRequest, useSetting } from 'hooks';
import { SETTING_KEYS } from 'constants/settingKeys';
import { API_CALOCUS, API_METHOD } from 'constants/urlRequest';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { handleValidationBE } from 'utils/form';
import { toast } from 'react-toastify';
import get from 'lodash/get';
import { scrollIntoViewId } from 'utils/globalScroll';
import { MODULE_NAME } from 'constants/routes';
import { fetchByQueryKey } from 'apis/queryClient';
import QUERY_KEY from 'constants/queryKey';

function Finish({ back, backToStart, isPageHaveHistorySection }) {
  const { searchParams, navigate } = useNavigator();
  const [dateFormat, timeFormat] = useSetting(SETTING_KEYS.DATE_FORMAT, SETTING_KEYS.TIME_FORMAT);

  const { data: locusLookup = {} } = useRequest({ url: API_CALOCUS.LOOKUP, shouldCache: true });

  const apiSave = useRequest({ url: API_CALOCUS.DEFAULT, method: API_METHOD.POST });

  const onSubmit = (form) => {
    let answers = [];
    for (const section of get(form, 'values.locus.evaluating', []).filter(Boolean)) {
      for (const ques of section.questions.filter(Boolean)) {
        for (const ans of ques.answer.filter(Boolean)) {
          answers.push(ans);
        }
      }
    }

    const payload = {
      ...form.values?.locus,
      evaluationDate: new Date(),
      patientId: searchParams.patientId,
      formDate: new Date(),
      answers,
      prevDispositionLevel: form.values?.locus.loc,
    };

    apiSave.mutateAsync(payload, {
      onSuccess: () => {
        form.setFieldValue('showCalocus', false);
        form.setFieldValue('calocusDisposition', payload.actualDispositionLevel);
        form.setFieldValue('calocusDate', payload.formDate);
        form.setFieldValue('locus', {});
        toast.success(t('Save C&A LOCUS Evaluation successfully.'));
        scrollIntoViewId('calocus-section');
        isPageHaveHistorySection && navigate({ hash: MODULE_NAME.CALOCUS_HISTORY });
        fetchByQueryKey(QUERY_KEY.CALOCUS_HISTORY);
        fetchByQueryKey(QUERY_KEY.CALOCUS_BY_PATIENT);
      },
      onError: (error) => {
        const custom = (key) => `locus.${key}`;
        handleValidationBE(form, error, {}, custom);
      },
    });
  };

  return (
    <LoadingWrapper loading={apiSave.loading}>
      <Stack tokens={{ childrenGap: 12 }}>
        <span className="weight-600">
          {t('You have successfully completed a C&A LOCUS© Evaluation.')}
        </span>
        <TextField
          value={moment().format(`${dateFormat} ${timeFormat}`)}
          readOnly
          label={t('Date/Time of Evaluation')}
        />
        <FieldSelectorPrimary
          name="locus.loc"
          isFast={false}
          title={t('Current Level of Care')}
          disabled
          required
          placeholder=""
          options={formatToFluentOptions(locusLookup.locusLevelOfCare)}
        />
        <FieldSelectorPrimary
          name="locus.recommendedDispositionLevel"
          isFast={false}
          title={t('CALOCUS Recommended Disposition')}
          disabled
          required
          placeholder=""
          options={formatToFluentOptions(locusLookup.locusLevelOfCare)}
        />
        <FieldSelectorPrimary
          name="locus.actualDispositionLevel"
          isFast={false}
          title={t('Actual Disposition')}
          options={formatToFluentOptions(locusLookup.locusLevelOfCare)?.filter(
            (i) => i.text !== 'None',
          )}
        />
        <MyField isFast={false} name="locus">
          {({ field }) => (
            <FieldSelectorPrimary
              name="locus.reasonVariance"
              isFast={false}
              required={
                +field.value.actualDispositionLevel !== +field.value.recommendedDispositionLevel
              }
              title={t('Reason For Variance')}
              options={formatToFluentOptions(locusLookup.locusReasonForVariance)}
            />
          )}
        </MyField>
        <Stack horizontal tokens={{ childrenGap: 16 }}>
          <MyField isFast={false}>
            {({ form }) => (
              <PrimaryButton
                text={t('Finish')}
                data-isbutton="button"
                onClick={() => onSubmit(form)}
              />
            )}
          </MyField>
          <DefaultButton text={t('Previous')} data-isbutton="button" onClick={back} />
          <MyField>
            {({ form: { setFieldValue } }) => (
              <DefaultButton
                text={t('Cancel Evaluation')}
                data-isbutton="button"
                onClick={() => {
                  setFieldValue('locus', {});
                  backToStart();
                }}
              />
            )}
          </MyField>
          <MyField>
            {({ form: { setFieldValue } }) => (
              <DefaultButton
                text={t('Cancel')}
                data-isbutton="button"
                onClick={() => {
                  setFieldValue('showCalocus', false);
                  setFieldValue('locus', {});
                }}
              />
            )}
          </MyField>
        </Stack>
      </Stack>
    </LoadingWrapper>
  );
}

export default Finish;
