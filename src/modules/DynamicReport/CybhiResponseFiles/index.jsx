import React, { useCallback, useMemo, useState } from 'react';
import { t } from 'utils/string';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import ELEMENT_ID from 'constants/elementId';
import { MODAL_SIZE } from 'constants/modal';
import QUERY_KEY from 'constants/queryKey';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import { API_METHOD, API_CYBHI_RESPONSE_FILES } from 'constants/urlRequest';
import { toast } from 'react-toastify';
import useModal from 'hooks/useModal';
import useRequest from 'hooks/useRequest';
import { TEXT_TEMPLATE } from 'constants/texts';
import ModalConfirm from 'components/Modal/ModalConfirm';
import Table from 'components/Table';
import Tag, { TAG_TYPE } from 'components/Tag';
import { getStatus } from 'constants/statusRender';
import { Spinner, Stack } from '@fluentui/react';

const CybhiResponseFiles = () => {
  const { showModal } = useModal();
  const [showArchived, setShowArchived] = useState(false);
  //use for key in request, cause loading spin need key change
  // const [pageParams, setPageParams] = useState({});

  const { updateParams, data, params, refetch } = useRequest({
    key: [QUERY_KEY.CYBHI_RESPONSE_FILES_HISTORY, showArchived],
    url: API_CYBHI_RESPONSE_FILES.GET_RESPONSE_FILES,
    params: { ...DEFAULT_PAGINATION_PARAMS, OrderBy: 'receivedDate', IsArchived: showArchived },
    autoRefetch: true,
    keepPreviousData: true,
  });

  const getMenu = (item) => ({
    items: [
      {
        key: 'download',
        text: 'Download',
        onClick: () => {},
        iconProps: {
          iconName: 'Download',
        },
        href: item?.blobUrl,
        target: '_blank',
      },
      {
        key: 'archive',
        text: 'Archive',
        onClick: () => {
          showModal({
            content: (
              <ModalConfirm
                message={t(TEXT_TEMPLATE.ARCHIVE_CONFIRMATION('this file'))}
                onOk={() => {}}
              />
            ),
            size: MODAL_SIZE.X_SMALL,
          });
        },
        iconProps: {
          iconName: 'Archive',
        },
        disabled: item?.status === 'Loading',
      },
    ],
    shouldFocusOnMount: false,
  });

  return (
    <CollapseVertical id={ELEMENT_ID.REPORT.RECENT} open title={t('Carelon Response Files')}>
      <Table
        columns={tableColumns}
        loading={data?.loading} //|| apiArchiving.loading}
        items={data?.items || []}
        initalItems={data?.items || []}
        totalItems={data?.totalItems}
        onMetadataChange={updateParams}
        pagination={true}
        refetch={refetch}
        metadata={params}
        getMenuProps={getMenu}
      />
    </CollapseVertical>
  );
};
export default CybhiResponseFiles;

const tableColumns = [
  {
    name: 'Document Title',
    fieldName: 'fileName',
    sortable: true,
  },
  {
    name: 'Type',
    fieldName: 'fileType',
    sortable: true,
  },
  {
    name: 'Received Date/Time',
    fieldName: 'receivedDate',
    sortable: true,
    renderItem: ({ receivedDate }, _, c, render) => render({ date: receivedDate, withTime: true }),
  },
  {
    name: 'Response Type',
    fieldName: 'responseType',
    sortable: true,
    renderItem: ({ responseType }) => getStatus(responseType?.toLowerCase()),
  },
  {
    key: 'Action',
    name: 'Action',
    fieldName: 'action',
  },
];
