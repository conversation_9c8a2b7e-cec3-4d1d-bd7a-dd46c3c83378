import React, { useMemo } from 'react';
import ParameterTable from '../ParameterTable';
import GroupTableDetail from 'components/GroupDetailTable';
import Tag, { TAG_TYPE } from 'components/Tag';
import {
  DefaultButton,
  Icon,
  ImageIcon,
  PrimaryButton,
  SearchBox,
  Spinner,
  Stack,
} from '@fluentui/react';
import { getStatus } from 'constants/statusRender';
import { t } from 'utils/string';
import StackHorizontal from 'components/StackHorizontal';
import useClass from 'hooks/useClass';
import cn from 'classnames';
import { downloadContentAPI } from 'utils/file';

const StatusFilterButton = ({ text, onClick, icon, status, selectedStatus }) => {
  const css = useClass(statusFilterButtonStyles);

  return (
    <DefaultButton
      text={text}
      className={cn(css.pageSectionItem, { [css.selected]: selectedStatus === status })}
      onClick={onClick}
      onRenderIcon={() =>
        icon?.startsWith('assets/images') ? (
          <ImageIcon imageProps={{ src: icon }} />
        ) : (
          <Icon iconName={icon} />
        )
      }
    />
  );
};

const statusFilterButtonStyles = (theme) => ({
  pageSectionItem: {
    padding: '4px 8px',
    borderRadius: '99px',
    whiteSpace: 'nowrap',

    'i, img': {
      height: 20,
    },
    ':hover': {
      backgroundColor: theme.palette.primaryColorShade2,
    },
  },
  selected: {
    backgroundColor: theme.palette.primaryColorShade2,
  },
});

export const Filter = ({ onSearch, onFilterClick, statusCount, selectedStatus }) => {
  return (
    <StackHorizontal
      horizontalAlign="start"
      items={[
        {
          grow: false,
          view: (
            <SearchBox
              styles={dropdownStyles}
              placeholder={t('Search Report...')}
              iconProps={{ iconName: 'Zoom' }}
              onChange={(_, value) => onSearch(value)}
            />
          ),
        },
        {
          grow: false,
          view: (
            <Stack horizontal tokens={{ childrenGap: 8 }} wrap>
              {mockStatusList.map((item, index) => (
                <StatusFilterButton
                  key={index}
                  {...item}
                  text={`${item?.status} (${statusCount?.[item?.status?.toLowerCase()] || '0'})`}
                  selectedStatus={selectedStatus}
                  onClick={() => {
                    onFilterClick(item);
                  }}
                />
              ))}
            </Stack>
          ),
        },
      ]}
    />
  );
};

const TableRecentReport = ({
  loading,
  data,
  onMetadataChange,
  getMenu,
  params,
  refetch,
  hightLightRecordIds,
}) => {
  const highlightAnimatedIndexs = useMemo(
    () =>
      (data?.items || []).reduce((curr, curVal, curIndex) => {
        if (hightLightRecordIds?.includes(curVal.recentReportId)) return [...curr, curIndex];
        return curr;
      }, []),
    [data?.items, hightLightRecordIds],
  );
  return (
    <GroupTableDetail
      highlightAnimatedIndexs={highlightAnimatedIndexs}
      onRenderDetail={(item, width) => (
        <ParameterTable width={width} recentReportId={item?.recentReportId} />
      )}
      loading={loading}
      columns={_columns}
      initalItems={data?.items || []}
      totalItems={data?.totalItems}
      metadata={params}
      onMetadataChange={onMetadataChange}
      pagination={true}
      getMenuProps={getMenu}
      refetch={refetch}
    />
  );
};

const _columns = [
  {
    name: 'Category',
    fieldName: 'category',
    sortable: true,
  },
  {
    name: 'Report Name',
    fieldName: 'reportName',
    sortable: true,
  },
  {
    name: 'Run Date & Time',
    fieldName: 'runDate',
    sortable: true,
    renderItem: ({ runDate }, _, c, render) => render({ date: runDate, withTime: true }),
  },
  {
    name: 'Status',
    fieldName: 'status',
    sortable: true,
    renderItem: ({ status }) =>
      status === 'Loading' ? (
        <Tag type={TAG_TYPE.VIOLET}>
          <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 8 }}>
            <Spinner
              styles={{
                circle: {
                  borderLeftColor: '#f1eafa',
                  borderRightColor: '#f1eafa',
                  borderBottomColor: '#f1eafa',
                  borderTopColor: '#722ED1',
                },
              }}
            />
            <span>Loading...</span>
          </Stack>
        </Tag>
      ) : (
        getStatus(status?.toLowerCase())
      ),
  },
  {
    name: '',
    fieldName: 'download',
    renderItem: ({ status, outputFileUrl }) => (
      <PrimaryButton href={outputFileUrl} disabled={status !== 'Completed'} text={t('Download')} />
    ),
  },
  {
    key: 'Action',
    name: 'Action',
    fieldName: 'action',
  },
];

const mockStatusList = [
  { icon: 'assets/images/complete.png', status: 'Completed' },
  { icon: 'assets/images/loading.png', status: 'Loading' },
  { icon: 'assets/images/scheduled.png', status: 'Scheduled' },
  { icon: 'assets/images/error.png', status: 'Error' },
  { icon: 'assets/images/cancelled.png', status: 'Cancelled' },
  { icon: 'assets/images/ready.png', status: 'Ready' },
  { icon: 'assets/images/sent.png', status: 'Sent' },
];

const dropdownStyles = { root: { width: 220 } };

export default TableRecentReport;
