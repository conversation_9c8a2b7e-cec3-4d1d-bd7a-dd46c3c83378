import React, { useContext, useEffect } from 'react';
import { toast } from 'react-toastify';
import useHookForm from 'components/HookForm/useHookForm';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { useNavigator, useRequest } from 'hooks';
import { API_METHOD, API_RCM_FUNDING_SOURCE, API_RCM_PAYOR_ASSIGNMENT } from 'constants/urlRequest';
import { handleValidationBE } from 'utils/form';
import { t } from 'utils/string';
import { TEXT_TEMPLATE } from 'constants/texts';
import { MODULE_NAME } from 'constants/routes';
import ELEMENT_ID from 'constants/elementId';
import { FormEditContext } from 'contexts/FormEditContext';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import FieldDate from 'components/HookForm/FieldDate';
import FieldText from 'components/HookForm/FieldText';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import ResponsiveGrid from 'components/Layout/Responsive/Container';
import ResponsiveItem from 'components/Layout/Responsive/Item';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { formatToFluentOptions } from 'utils';
import { fetchByQueryKey } from 'apis/queryClient';
import QUERY_KEY from 'constants/queryKey';
import HookFormProvider from 'components/HookForm/HookFormProvider';
import { FORM_ID } from 'constants/formId';

const FundingSourceInput = ({ payor }) => {
  const { searchParams, navigate } = useNavigator();

  const { editItem, setEditItem } = useContext(FormEditContext).getEditFunctions(
    MODULE_NAME.FUNDING_SOURCE_INPUT,
  );

  const apiOptions = useRequest({ url: API_RCM_FUNDING_SOURCE.LOOKUP, shouldCache: true });

  const defaultValues = {
    clientId: searchParams.patientId,
    fundingSource: '',
    authorization: '',
    authorizationEntry: '',
    effectiveDate: null,
    endDate: null,
    status: null,
    comment: '',
    ...(payor && {
      payorAssignmentId: payor.payorAssignmentId,
      clientId: payor.patientId,
      payorId: payor.payorId,
    }),
  };

  const methods = useHookForm({
    defaultValues: editItem
      ? {
          ...editItem,
          ...(payor && {
            payorAssignmentId: payor.payorAssignmentId,
            clientId: payor.patientId,
            payorId: payor.payorId,
          }),
        }
      : defaultValues,
    dependencies: { editItem },
  });

  const apiSave = useRequest({
    url: payor ? API_RCM_PAYOR_ASSIGNMENT.AUTH_GROUPS() : API_RCM_FUNDING_SOURCE.DEFAULT,
    method: API_METHOD.POST,
  });

  useEffect(() => {
    if (editItem) {
      methods.reset({
        ...editItem,
        ...(payor && {
          payorAssignmentId: payor.payorAssignmentId,
          clientId: payor.patientId,
          payorId: payor.payorId,
        }),
      });
    }
  }, [editItem, methods]);

  const onReset = () => methods.reset(defaultValues);

  const onSubmit = (values) => {
    apiSave.request({
      values,
      options: {
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('Funding Source')));
          setEditItem(null);
          if (payor) {
            fetchByQueryKey(QUERY_KEY.FUNDING_SOURCE_PAYOR_HISTORY);
          } else {
            navigate({ hash: MODULE_NAME.FUNDING_SOURCE_HISTORY });
            fetchByQueryKey(QUERY_KEY.FUNDING_SOURCE_HISTORY);
          }
        },
        onError: (err) => {
          handleValidationBE({ setError: methods.setError }, err);
        },
      },
    });
  };

  return (
    <CollapseVertical
      className="mb-24"
      open
      title={t('Funding Source Input')}
      id={ELEMENT_ID.FUNDING_SOURCE?.INPUT}
    >
      <HookFormProvider {...methods} formId={FORM_ID.FUNDING_SOURCE_INPUT}>
        <LoadingWrapper loading={apiSave.loading}>
          <Stack tokens={{ padding: 16 }}>
            <ResponsiveGrid>
              <ResponsiveItem md={4}>
                <FieldSelectorPrimary
                  title={t('Funding Source')}
                  name="authGroupId"
                  options={formatToFluentOptions(apiOptions?.data)}
                  isFast={false}
                  onChange={({ data }) => {
                    methods.setValue('authGroupCode', data?.code);
                    methods.setValue('authGroupId', data?.key);
                  }}
                  required
                />
              </ResponsiveItem>
              <ResponsiveItem md={4}>
                <FieldText
                  readOnly
                  title={t('Authorization')}
                  name="authGroupCode"
                  placeholder=""
                  voice={false}
                />
              </ResponsiveItem>
              <ResponsiveItem md={4}>
                <FieldText title={t('Authorization Entry')} name="authGroupCodeManual" />
              </ResponsiveItem>

              <ResponsiveItem md={4}>
                <FieldDate title={t('Start Date')} name="beginDate" />
              </ResponsiveItem>
              <ResponsiveItem md={4}>
                <FieldDate title={t('End Date')} name="endDate" />
              </ResponsiveItem>
              <ResponsiveItem md={4}>
                <FieldSelectorPrimary
                  title={t('Status')}
                  name="status"
                  required
                  options={[
                    { key: 'Pending', text: 'Pending' },
                    { key: 'Approved', text: 'Approved' },
                  ]}
                />
              </ResponsiveItem>

              <ResponsiveItem md={12}>
                <FieldText title={t('Comment')} name="comments" />
              </ResponsiveItem>

              <ResponsiveItem md={12}>
                <Stack horizontal tokens={{ childrenGap: 16 }}>
                  <PrimaryButton
                    text={t('Submit')}
                    onClick={(e) => {
                      methods.clearErrors();
                      methods.handleSubmit(onSubmit)(e);
                    }}
                  />
                  <CalloutConfirmation onOk={onReset}>
                    <DefaultButton text={t('Reset')} />
                  </CalloutConfirmation>
                </Stack>
              </ResponsiveItem>
            </ResponsiveGrid>
          </Stack>
        </LoadingWrapper>
      </HookFormProvider>
    </CollapseVertical>
  );
};

export default FundingSourceInput;
