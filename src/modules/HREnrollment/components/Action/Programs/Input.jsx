import React, { useEffect, useMemo } from 'react';
import { Stack, DefaultButton } from '@fluentui/react';
import { ELEMENT_ID } from '../../constant';
import { useClass, useRequest } from 'hooks';
import QUERY_KEY from 'constants/queryKey';
import { API_EMPLOYEE_PROGRAM, API_METHOD } from 'constants/urlRequest';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { t } from 'utils/string';
import cn from 'classnames';
import { Col, Container, Row } from 'components/Layout/Grid';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import { formatToFluentOptions, removeUndefinedFromObject } from 'utils';
import FieldDate from 'components/HookForm/FieldDate';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import { TEXT_TEMPLATE } from 'constants/texts';
import { handleValidationBE } from 'utils/form';
import { fetchByQueryKey } from 'apis/queryClient';
import { toast } from 'react-toastify';
import { PrimaryButtonAdd } from 'HOCs';
import useHookForm from 'components/HookForm/useHookForm';
import HookFormProvider from 'components/HookForm/HookFormProvider';

const Input = ({ viewItem, setViewItem, employeeId }) => {
  const css = useClass();

  const defaultInitialValues = useMemo(
    () => ({
      employeeId: employeeId,
      programId: null,
      startDate: null,
      endDate: null,
    }),
    [employeeId],
  );

  const methods = useHookForm({
    defaultValues: defaultInitialValues,
    dependencies: { employeeId },
  });

  const programLookups = useRequest({
    url: API_EMPLOYEE_PROGRAM.GET_PROGRAMS_LOOKUP,
  });

  const submitRequest = useRequest({
    url: API_EMPLOYEE_PROGRAM.DEFAULT,
    method: API_METHOD.POST,
  });

  const handleReset = () => {
    methods.reset(defaultInitialValues);
    setViewItem?.();
  };

  useEffect(() => {
    if (viewItem) {
      methods.reset(viewItem);
    }
  }, [viewItem]);

  const onSubmit = (values) => {
    const isEditing = values?.employeeProgramId;
    submitRequest.request({
      payload: removeUndefinedFromObject(values),
      options: {
        onError: (err) => handleValidationBE({ setError: methods.setError }, err),
        onSuccess: () => {
          toast.success(
            isEditing
              ? TEXT_TEMPLATE.UPDATE_SUCCESSFULLY(t('Programs'))
              : TEXT_TEMPLATE.ADD_SUCCESSFULLY(t('Programs')),
          );

          handleReset?.();
          fetchByQueryKey(QUERY_KEY.GET_EMPLOYEE_PROGRAM + employeeId);
        },
      },
    });
  };

  return (
    <CollapseVertical className="mb-16" open title={t('Programs')} id={ELEMENT_ID.PROGRAMS.INPUT}>
      <HookFormProvider {...methods}>
        <React.Fragment>
          <LoadingWrapper loading={submitRequest?.loading}>
            <Stack
              className={cn(
                css.cmClass.backgroundGrey100,
                css.cmClass.border,
                css.cmClass.borderColor,
              )}
              tokens={{ childrenGap: 8, padding: 16 }}
            >
              <Container>
                <Row>
                  <Col md={3}>
                    <FieldSelectorPrimary
                      options={formatToFluentOptions(programLookups?.data || [])}
                      required
                      title={t('Program')}
                      name="programId"
                    />
                  </Col>
                  <Col md={3}>
                    <FieldDate required title={t('Eff. Date')} name="startDate" />
                  </Col>
                  <Col md={3}>
                    <FieldDate title={t('Exp. Date')} name="endDate" />
                  </Col>
                </Row>
              </Container>
            </Stack>
          </LoadingWrapper>
          <Stack horizontal tokens={{ childrenGap: 16 }} className="mt-16">
            <LoadingWrapper loading={submitRequest?.loading}>
              <PrimaryButtonAdd
                text={viewItem?.empEducationId ? t('Edit') : t('Submit')}
                data-isbutton="button"
                onClick={methods.handleSubmit(onSubmit)}
              />
            </LoadingWrapper>
            <CalloutConfirmation onOk={handleReset}>
              <DefaultButton disabled={submitRequest?.loading} text={t('Reset')} />
            </CalloutConfirmation>
          </Stack>
        </React.Fragment>
      </HookFormProvider>
    </CollapseVertical>
  );
};

export default Input;
