import React, { Fragment, useContext, useEffect, useState } from 'react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { t } from 'utils/string';
import { PrimaryButton, SearchBox, Stack } from '@fluentui/react';
import GroupDetailTable from 'components/GroupDetailTable';
import { useClass, useModal, useNavigator, useRequest, useSetting, useUser } from 'hooks';
import ResponsiveGrid from 'components/Layout/Responsive/Container';
import ResponsiveItem from 'components/Layout/Responsive/Item';
import QUERY_KEY from 'constants/queryKey';
import { API_IBHIS, API_METHOD } from 'constants/urlRequest';
import ModalConfirm from 'components/Modal/ModalConfirm';
import { TEXT_TEMPLATE } from 'constants/texts';
import { MODAL_SIZE } from 'constants/modal';
import { toast } from 'react-toastify';
import { scrollIntoViewId } from 'utils/globalScroll';
import { parseFormatDateBE } from 'utils/time';
import { getStatus } from 'constants/statusRender';
import ModalViewItem from 'components/Modal/ModalView/ModalViewItem';
import { onPrint } from 'utils/file';
import { SETTING_KEYS } from 'constants/settingKeys';
import { IBHISContext } from 'contexts/IBHISContext';
import queryClient from 'apis/queryClient';
import cn from 'classnames';
import { MODULE_NAME } from 'constants/routes';
import { DEFAULT_PAGINATION_PARAMS } from 'constants';

const Detail = ({ item }) => {
  const { data: lookup } = useRequest({ url: API_IBHIS.CANS_DICTIONARY, shouldCache: true });
  const assessmentType = lookup?.assessmentTypes.find((i) => i.key === item.assessmentType);
  const css = useClass();

  return (
    <Stack className={css.cmClass.backgroundGrey200} tokens={{ childrenGap: 8, padding: 16 }}>
      {assessmentType?.description === 'Administrative Close' && (
        <Stack horizontal tokens={{ childrenGap: 4 }}>
          <div>{t('If Administrative Close, Select Reason: ')}</div>
          <div>
            {lookup?.adminCloseReasons.find((i) => i.key === item.adminCloseReason)?.description}
          </div>
        </Stack>
      )}
      {assessmentType?.description !== 'Administrative Close' && (
        <Fragment>
          {item.contributorName1 && (
            <Stack horizontal tokens={{ childrenGap: 4 }}>
              <div>{t('Contributor 1 Name: ')}</div>
              <div>{item.contributorName1}</div>
            </Stack>
          )}
          {item.contributorName2 && (
            <Stack horizontal tokens={{ childrenGap: 4 }}>
              <div>{t('Contributor 2 Name: ')}</div>
              <div>{item.contributorName2}</div>
            </Stack>
          )}
          {item.contributorName3 && (
            <Stack horizontal tokens={{ childrenGap: 4 }}>
              <div>{t('Contributor 3 Name: ')}</div>
              <div>{item.contributorName3}</div>
            </Stack>
          )}
          {item.cansCaregiverResourcesAndNeeds?.map((i, index) => (
            <Stack key={`${i.caregiverName}-${index}`} horizontal tokens={{ childrenGap: 4 }}>
              <div>{`Caregiver/Contact ${index + 1}: `}</div>
              <div>{i.caregiverName}</div>
            </Stack>
          ))}
        </Fragment>
      )}
    </Stack>
  );
};

export const HistoryEmpty = ({ form }) => {
  const { navigate } = useNavigator();
  const { currentPatient } = useUser();
  const css = useClass();

  return (
    <CollapseVertical open title={t(`${form} History `)}>
      <Stack horizontal tokens={{ childrenGap: 4 }}>
        <span>{t(`To attach a ${form} assessment to`)}</span>
        <strong>{`${currentPatient.fullName},`}</strong>
        <span> {t('first')}</span>
        <u
          className={cn('pointer', css.cmClass.colorPrimary)}
          onClick={() => navigate({ hash: MODULE_NAME.IBHIS_SEARCH })}
        >
          {t('create an episode.')}
        </u>
      </Stack>
    </CollapseVertical>
  );
};

const CANSHistory = ({ setEditItem }) => {
  const [DATE_FORMAT] = useSetting(SETTING_KEYS.DATE_FORMAT);
  const { showModal } = useModal();
  const { ibhisPatientId } = useContext(IBHISContext);
  const [searchValue, setSearchValue] = useState();

  const { data, loading, updateParams, refetch, params } = useRequest({
    key: QUERY_KEY.IBHIS_CANS,
    url: API_IBHIS.CANS(),
    params: { ...DEFAULT_PAGINATION_PARAMS, ibhisClientId: ibhisPatientId, OrderBy: 'updateDate' },
    enabled: !!ibhisPatientId,
    autoRefetch: true,
  });

  useEffect(() => {
    if (ibhisPatientId) {
      updateParams({ ibhisClientId: ibhisPatientId });
      setSearchValue(ibhisPatientId);
    } else {
      queryClient.setQueryData([QUERY_KEY.IBHIS_CANS], []);
      setSearchValue();
    }
  }, [ibhisPatientId]);

  const onSearch = () => {
    updateParams({ ibhisClientId: searchValue });
  };

  const apiDelete = useRequest({
    url: API_IBHIS.CANS(''),
    method: API_METHOD.DELETE,
  });

  const getMenu = (item) => ({
    items: [
      {
        key: 'edit',
        text: 'Edit',
        iconProps: { iconName: 'EditNote' },
        onClick: setEditItem,
      },
      {
        key: 'view',
        text: 'View',
        iconProps: { iconName: 'ViewList' },
        onClick: () =>
          showModal({
            content: (
              <ModalViewItem
                url={API_IBHIS.CANS_PRINT(item.id)}
                title={t('CANS')}
                itemKey={`CANS-${item.submissionId}`}
              />
            ),
          }),
      },
      {
        key: 'print',
        text: 'Print',
        iconProps: { iconName: 'Print' },
        onClick: async () => {
          onPrint({
            url: API_IBHIS.CANS_PRINT(item.id),
            queryString: {
              dateFormatString: parseFormatDateBE(DATE_FORMAT),
            },
          });
        },
      },

      {
        key: 'delete',
        text: 'Delete',
        iconProps: { iconName: 'Delete' },
        onClick: () =>
          showModal({
            content: (
              <ModalConfirm
                message={t(TEXT_TEMPLATE.DELETE_CONFIRMATION('this CANS record'))}
                onOk={() =>
                  apiDelete.request({
                    url: API_IBHIS.CANS(item.id),
                    options: {
                      onSuccess: () => {
                        toast.success(TEXT_TEMPLATE.DELETE_SUCCESSFULLY('CANS record'));
                        refetch();
                      },
                    },
                  })
                }
              />
            ),
            size: MODAL_SIZE.X_SMALL,
          }),
      },
    ],
    shouldFocusOnMount: false,
  });

  if (!ibhisPatientId) return <HistoryEmpty form="CANS" />;

  return (
    <CollapseVertical open title={t('CANS History ')}>
      <Stack tokens={{ childrenGap: 16 }}>
        <ResponsiveGrid>
          <ResponsiveItem md={3}>
            <SearchBox
              iconProps={{ iconName: 'Zoom' }}
              placeholder={t('IBHIS Client ID')}
              onChange={(_, value) => setSearchValue(value)}
              value={searchValue}
              onSearch={onSearch}
            />
          </ResponsiveItem>
          <ResponsiveItem md={4}>
            <Stack horizontal tokens={{ childrenGap: 16 }}>
              <PrimaryButton
                text={t('Search')}
                onClick={onSearch}
                iconProps={{ iconName: 'Zoom' }}
              />
              <PrimaryButton
                text={t('New CANS Assessment')}
                iconProps={{ iconName: 'Add' }}
                onClick={() => {
                  setEditItem();
                  scrollIntoViewId('CANS-INPUT', 'start');
                }}
                disabled={!ibhisPatientId}
              />
            </Stack>
          </ResponsiveItem>
        </ResponsiveGrid>
        <GroupDetailTable
          key={`CANS-HISTORY-${data?.length}`}
          loading={loading || apiDelete.loading}
          columns={_columns}
          initalItems={data?.items || []}
          totalItems={data?.totalItems}
          refetch={() => searchValue && updateParams({ isFromIbhis: true })}
          metadata={params}
          pagination
          onMetadataChange={updateParams}
          getMenuProps={getMenu}
          onRenderDetail={(item) => <Detail item={item} />}
        />
      </Stack>
    </CollapseVertical>
  );
};

export default CANSHistory;

const _columns = [
  { name: 'IBHIS Location Code', fieldName: 'providerNumber', sortable: true },
  { name: 'IBHIS Client ID', fieldName: 'ibhisClientID', sortable: true },
  {
    name: 'Effective Date',
    fieldName: 'assessmentDate',
    renderItem: ({ assessmentDate }, _, c, render) => render({ date: assessmentDate }),
    sortable: true,
  },
  {
    name: 'CANS Reason/Status',
    fieldName: 'assessmentType',
    renderItem: ({ assessmentType }) => <AssessmentType assessmentType={assessmentType} />,
    sortable: true,
  },
  {
    name: 'Status',
    fieldName: 'status',
    renderItem: ({ status }) => getStatus(status),
    sortable: true,
  },
  {
    name: 'Last Update',
    fieldName: 'updateDate',
    renderItem: ({ updateDate, updateUser }, _, c, render) =>
      render({ date: updateDate, user: updateUser, withTime: true }),
    sortable: true,
  },
  { name: 'Comments', fieldName: 'comments', sortable: true },
  { key: 'Action', name: 'Action', fieldName: 'Action' },
];

const AssessmentType = ({ assessmentType }) => {
  const { data: lookups } = useRequest({ url: API_IBHIS.CANS_DICTIONARY, shouldCache: true });

  return lookups?.assessmentTypes?.find((item) => item.key === assessmentType)?.description;
};
