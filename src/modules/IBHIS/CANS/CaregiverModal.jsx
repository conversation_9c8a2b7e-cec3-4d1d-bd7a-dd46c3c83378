import React from 'react';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import FieldText from 'components/HookForm/FieldText';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import Table from 'components/Table';
import { useModal, useRequest } from 'hooks';
import { formatToFluentOptions } from 'utils';
import { t } from 'utils/string';
import { API_IBHIS, API_METHOD } from 'constants/urlRequest';
import { toast } from 'react-toastify';
import { handleValidationBE } from 'utils/form';
import ModalConfirm from 'components/Modal/ModalConfirm';
import { TEXT_TEMPLATE } from 'constants/texts';
import { MODAL_SIZE } from 'constants/modal';
import useHookForm from 'components/HookForm/useHookForm';
import HookFormProvider from 'components/HookForm/HookFormProvider';
import { FORM_ID } from 'constants/formId';

const defaultInitialValues = {
  caregiverName: null,
  caregiverRelationship: null,
  supervision: null,
  involvementWithCare: null,
  knowledge: null,
  socialResources: null,
  residentialStability: null,
  medicalPhysical: null,
  mentalHealth: null,
  substanceUse: null,
  development: null,
  safety: null,
  updateDate: null,
  updateUser: null,
};

function CaregiverModal({ relationship }) {
  const { hideModal, showModal } = useModal();

  const methods = useHookForm({ defaultValues: defaultInitialValues });

  const apiSave = useRequest({
    url: API_IBHIS.CANS_CAREGIVERS(),
    method: API_METHOD.POST,
  });

  const apiDelete = useRequest({
    method: API_METHOD.DELETE,
  });

  const { data, loading, refetch } = useRequest({
    url: API_IBHIS.CANS_CAREGIVERS(),
    shouldCache: true,
  });

  const onSubmit = (values) => {
    apiSave.request({
      payload: values,
      options: {
        onSuccess: () => {
          toast.success(
            values.id ? t('Caregiver Updated Successfully') : t('Caregiver Added Successfully'),
          );
          refetch();
          onReset();
        },
        onError: (e) => {
          handleValidationBE({ setError: methods.setError }, e);
        },
      },
    });
  };

  const onReset = () => {
    methods.clearErrors();
    methods.reset(defaultInitialValues);
  };

  const handleSubmit = (e) => {
    methods.clearErrors();
    methods.handleSubmit(onSubmit)(e);
  };

  return (
    <ModalLayout
      title={t('Add/Edit Contact')}
      loading={apiSave.loading || apiDelete.loading || loading}
      footerRightSide={
        <Stack horizontal tokens={{ childrenGap: 16 }} horizontalAlign="end" verticalAlign="center">
          <DefaultButton text={t('Cancel')} data-isbutton="button" onClick={hideModal} />
        </Stack>
      }
    >
      <Stack tokens={{ padding: 16, childrenGap: 16 }}>
        <CollapseVertical open title={t('History')}>
          <Table
            columns={[
              { name: 'Name', fieldName: 'caregiverName' },
              {
                name: 'Relationship',
                fieldName: 'caregiverRelationship',
                renderItem: ({ caregiverRelationship }) =>
                  relationship.find((i) => i.key === caregiverRelationship)?.description,
              },
              { key: 'Action', name: 'Action', fieldName: 'action' },
            ]}
            items={data || []}
            pagination={false}
            refetch={refetch}
            onEdit={(item) => methods.reset(item)}
            onDelete={(item) => {
              showModal({
                content: (
                  <ModalConfirm
                    message={TEXT_TEMPLATE.DELETE_CONFIRMATION(t('this Caregiver'))}
                    onOk={async () => {
                      await apiDelete.request({
                        url: API_IBHIS.CANS_CAREGIVERS(item.id),
                        options: {
                          onSuccess: () => {
                            toast.success(TEXT_TEMPLATE.DELETE_SUCCESSFULLY(t('The Caregiver')));
                            refetch();
                          },
                        },
                      });
                    }}
                  />
                ),
                isReplace: false,
                size: MODAL_SIZE.X_SMALL,
              });
            }}
          />
        </CollapseVertical>
        <CollapseVertical open title={t('Caregiver')}>
          <HookFormProvider formId={FORM_ID.IBHIS_CAREGIVER_INPUT} {...methods}>
            <Stack tokens={{ childrenGap: 16 }}>
              <Stack horizontal tokens={{ childrenGap: 16 }}>
                <FieldText
                  name="caregiverName"
                  title={t('Name')}
                  required
                  fieldClassName="flex-1"
                  maxLength={80}
                />
                <FieldSelectorPrimary
                  name="caregiverRelationship"
                  title={t('Relationship')}
                  options={formatToFluentOptions(relationship)}
                  required
                  fieldClassName="flex-1"
                />
              </Stack>
              <Stack horizontal tokens={{ childrenGap: 16 }}>
                <PrimaryButton text={t('Save')} data-isbutton="button" onClick={handleSubmit} />
                <DefaultButton text={t('Reset')} data-isbutton="button" onClick={onReset} />
              </Stack>
            </Stack>
          </HookFormProvider>
        </CollapseVertical>
      </Stack>
    </ModalLayout>
  );
}

export default CaregiverModal;
