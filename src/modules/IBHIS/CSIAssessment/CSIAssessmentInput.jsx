import React, { useContext, useEffect } from 'react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import useHookForm from 'components/HookForm/useHookForm';
import { t } from 'utils/string';
import ResponsiveGrid from 'components/Layout/Responsive/Container';
import ResponsiveItem from 'components/Layout/Responsive/Item';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import FieldDate from 'components/HookForm/FieldDate';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import FieldInfo from 'components/HookForm/FieldInfo';
import HorizontalRule from 'components/HorizontalRule';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import { useRequest } from 'hooks';
import { API_IBHIS, API_METHOD } from 'constants/urlRequest';
import { TEXT_TEMPLATE } from 'constants/texts';
import QUERY_KEY from 'constants/queryKey';
import { handleValidationBE } from 'utils/form';
import { formatToFluentOptions } from 'utils';
import { IBHISContext } from 'contexts/IBHISContext';
import { fetchByQueryKey } from 'apis/queryClient';
import { toast } from 'react-toastify';
import HookFormProvider from 'components/HookForm/HookFormProvider';
import { FORM_ID } from 'constants/formId';

const CSIAssessmentInput = ({ editItem, setEditItem }) => {
  const { ibhisPatientId } = useContext(IBHISContext);

  const defaultValues = {
    serviceRequestID: '',
    treatmentAppointmentFirstOfferDate: null,
    treatmentAppointmentSecondOfferDate: null,
    treatmentAppointmentThirdOfferDate: null,
    treatmentAppointmentAcceptedDate: null,
    closureReason: '',
    referredTo: '',
  };

  const methods = useHookForm({
    defaultValues: editItem || defaultValues,
    dependencies: editItem,
  });

  useEffect(() => {
    if (editItem) methods.reset(editItem);
  }, [editItem, methods]);

  const onReset = () => {
    methods.reset(defaultValues);
    setEditItem();
  };

  const apiLookup = useRequest({ url: API_IBHIS.CSI_ASSESSMENT_LOOKUP, shouldCache: true });
  const apiService = useRequest({
    url: API_IBHIS.SEARCH_SERVICE_REQUEST,
    params: {
      isFromIbhis: true,
      PageSize: 10000,
      SearchType: 'SearchByClientId',
    },
    requiredParams: {
      ibhisClientID: ibhisPatientId,
    },
  });

  const apiCreate = useRequest({
    method: API_METHOD.POST,
  });

  const apiUpdate = useRequest({
    method: API_METHOD.PUT,
  });

  const onSubmit = (values, isToLAC) => {
    let payload = {
      ...values,
      ibhisClientID: ibhisPatientId,
      status: isToLAC ? 'Complete' : 'Incomplete',
    };

    const apiSave = editItem ? apiUpdate : apiCreate;

    apiSave.request({
      url: API_IBHIS.CSI_ASSESSMENT(editItem ? `${editItem.csiSubmissionID}` : ''),
      payload,
      options: {
        onSuccess: () => {
          fetchByQueryKey(QUERY_KEY.IBHIS_CSI_ASSESSMENT);
          toast.success(
            editItem
              ? TEXT_TEMPLATE.UPDATE_SUCCESSFULLY('CSI Assessment')
              : TEXT_TEMPLATE.SAVE_SUCCESSFULLY('CSI Assessment'),
          );
          onReset();
          setEditItem();
        },
        onError: (err) => {
          handleValidationBE({ setError: methods.setError }, err);
        },
      },
    });
  };

  const handleSubmit = (isToLAC) => {
    methods.clearErrors();
    methods.handleSubmit((values) => onSubmit(values, isToLAC))();
  };

  return (
    <CollapseVertical className="mb-24" open title={t('CSI Assessment Input')}>
      <HookFormProvider {...methods} formId={FORM_ID.IBHIS_CSI_ASSESSMENT_INPUT}>
        <LoadingWrapper loading={apiCreate.loading || apiUpdate.loading}>
          <Stack tokens={{ padding: 16 }}>
            <ResponsiveGrid>
              <ResponsiveItem md={3}>
                <LoadingWrapper loading={apiService.loading}>
                  <FieldSelectorPrimary
                    title={t('Choose A Service Request')}
                    name="serviceRequestID"
                    options={(apiService?.data?.items || []).map((i) => ({
                      key: i.request.serviceRequestId,
                      text: i.request.requestDate,
                    }))}
                    isFast={false}
                    required
                  />
                </LoadingWrapper>
              </ResponsiveItem>

              <ResponsiveItem md={3}>
                <FieldInfo title={t('Service Request ID')} name="serviceRequestID" />
              </ResponsiveItem>

              <ResponsiveItem md={3} newLine>
                <FieldDate
                  title={t('Treatment Appointment First Offer Date')}
                  name="treatmentAppointmentFirstOfferDate"
                  required
                  isRenderRightSide={false}
                />
              </ResponsiveItem>
              <ResponsiveItem md={3}>
                <FieldDate
                  title={t('Treatment Appointment Second Offer Date')}
                  name="treatmentAppointmentSecondOfferDate"
                  isRenderRightSide={false}
                />
              </ResponsiveItem>
              <ResponsiveItem md={3}>
                <FieldDate
                  title={t('Treatment Appointment Third Offer Date')}
                  name="treatmentAppointmentThirdOfferDate"
                  isRenderRightSide={false}
                />
              </ResponsiveItem>
              <ResponsiveItem md={3}>
                <FieldDate
                  title={t('Treatment Appointment Accepted Date')}
                  name="treatmentAppointmentAcceptedDate"
                  isRenderRightSide={false}
                  required
                />
              </ResponsiveItem>

              <ResponsiveItem md={3}>
                <FieldSelectorPrimary
                  title={t('Closure Reason')}
                  name="closureReason"
                  isFast={false}
                  required
                  options={formatToFluentOptions(apiLookup?.data?.closureReason)}
                />
              </ResponsiveItem>
              <ResponsiveItem md={3}>
                <FieldSelectorPrimary
                  title={t('Referred To')}
                  name="referredTo"
                  isFast={false}
                  required
                  options={formatToFluentOptions(apiLookup?.data?.referredTo)}
                />
              </ResponsiveItem>

              <ResponsiveItem md={12}>
                <HorizontalRule mb={16} />
                <Stack horizontal tokens={{ childrenGap: 16 }}>
                  <PrimaryButton
                    text={t('Send CSI Assessment To LAC DMH')}
                    onClick={() => handleSubmit(true)}
                    data-isbutton="button"
                  />
                  <DefaultButton
                    text={t('Save As Incomplete')}
                    onClick={() => handleSubmit(false)}
                    data-isbutton="button"
                  />
                  <CalloutConfirmation onOk={onReset}>
                    <DefaultButton text={t('Reset')} />
                  </CalloutConfirmation>
                </Stack>
              </ResponsiveItem>
            </ResponsiveGrid>
          </Stack>
        </LoadingWrapper>
      </HookFormProvider>
    </CollapseVertical>
  );
};

export default CSIAssessmentInput;
