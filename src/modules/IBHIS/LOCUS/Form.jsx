import React, { useContext, useState } from 'react';
import Evaluation from './Evaluation';
import Finish from './Finish';
import { IBHISContext } from 'contexts/IBHISContext';
import { useRequest } from 'hooks';
import { API_HIDEX_LOCUS } from 'constants/urlRequest';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { t } from 'utils/string';
import { TooltipHost, IconButton, Stack } from '@fluentui/react';
import useHookForm from 'components/HookForm/useHookForm';
import HookFormProvider from 'components/HookForm/HookFormProvider';
import { FORM_ID } from 'constants/formId';

const Form = () => {
  const { ibhisPatientId } = useContext(IBHISContext);

  const [step, setStep] = useState(0);

  const apiGetQuestion = useRequest({
    url: API_HIDEX_LOCUS.GET_LOCUS_QUESTION,
    shouldCache: true,
    enabled: false,
  });

  const methods = useHookForm({
    defaultValues: async () => {
      const { data: question } = await apiGetQuestion.request();
      return { ...question, ibhisPatientId };
    },
    dependencies: { ibhisPatientId },
  });

  const onFetchLatestForm = async (e) => {
    e.stopPropagation();
    const { data: question } = await apiGetQuestion.request({ queryString: { isFromHidex: true } });
    methods.reset({ ...question, ibhisPatientId });
  };

  const Component = STEPS[step];
  return (
    <CollapseVertical
      open
      className="mb-24"
      title={
        step === 0 ? (
          <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 4 }}>
            <span>{t('LOCUS Input')}</span>
            <TooltipHost content={t('Fetch latest form')}>
              <IconButton iconProps={{ iconName: 'Download' }} onClick={onFetchLatestForm} />
            </TooltipHost>
          </Stack>
        ) : (
          t('LOCUS Input')
        )
      }
    >
      <HookFormProvider formId={FORM_ID.IBHIS_LOCUS_INPUT} {...methods}>
        <Stack tokens={{ padding: 16 }}>
          <Component setStep={setStep} apiGetQuestion={apiGetQuestion} />
        </Stack>
      </HookFormProvider>
    </CollapseVertical>
  );
};

const STEPS = [Evaluation, Finish];
export default Form;
