import React, { Fragment, useEffect, useMemo } from 'react';
import { t } from 'utils/string';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import useClass from 'hooks/useClass';
import useRequest from 'hooks/useRequest';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import cn from 'classnames';
import HorizontalRule from 'components/HorizontalRule';
import FieldText from 'components/HookForm/FieldText';
import FieldDate from 'components/HookForm/FieldDate';
import ResponsiveGrid from 'components/Layout/Responsive/Container';
import ResponsiveItem from 'components/Layout/Responsive/Item';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import FieldCheckBox from 'components/HookForm/FieldCheckbox';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { API_IBHIS, API_METHOD } from 'constants/urlRequest';
import { formatToFluentOptions } from 'utils';
import { toast } from 'react-toastify';
import { TEXT_TEMPLATE } from 'constants/texts';
import QUERY_KEY from 'constants/queryKey';
import queryClient, { fetchByQueryKey } from 'apis/queryClient';
import ELEMENT_ID from 'constants/elementId';
import FieldLayout from 'components/HookForm/FieldLayout';
import sortBy from 'lodash/sortBy';
import FieldInfo from 'components/HookForm/FieldInfo';
import StatusInfo from '../UpdateAxiomClient/StatusInfoHookForm';
import useUser from 'hooks/useUser';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import useHookForm from 'components/HookForm/useHookForm';
import WatchValues from 'components/HookForm/WatchValues';
import { handleValidationBE } from 'utils/form';
import HookFormProvider from 'components/HookForm/HookFormProvider';
import { FORM_ID } from 'constants/formId';

const PSCInput = ({ ibhisPatientId, pscSubmission, setPSCSubmission }) => {
  const { info } = useUser();

  const defaultInitialValues = useMemo(
    () => ({
      pscToolQ: {
        complainsOfAchesAndPains: null,
        spendsMoreTimeAlone: null,
        tiresEasilyHasLittleEnergy: null,
        fidgetyUnableToSitStill: null,
        hasTroubleWithTeacher: null,
        lessInterestedInSchool: null,
        actsAsIfDrivenByAMotor: null,
        daydreamsTooMuch: null,
        distractedEasily: null,
        isAfraidOfNewSituations: null,
        feelsSadUnhappy: null,
        isIrritableAngry: null,
        feelsHopeless: null,
        hasTroubleConcentrating: null,
        lessInterestInFriends: null,
        fightsWithOtherChildren: null,
        absentFromSchool: null,
        schoolGradesDropping: null,
        isDownOnHimOrHerself: null,
        visitsTheDoctorWithDoctorFindingNothingWrong: null,
        hasTroubleSleeping: null,
        worriesALot: null,
        wantsToBeWithYouMoreThanBefore: null,
        feelsHeOrSheIsBad: null,
        takesUnnecessaryRisks: null,
        getsHurtFrequently: null,
        seemsToBeHavingLessFun: null,
        actsYoungerThanChildrenHisOrHerAge: null,
        doesNotListenToRules: null,
        doesNotShowFeelings: null,
        doesNotUnderstandOtherPeoplesFeelings: null,
        teasesOthers: null,
        blamesOthersForHisOrHerTroubles: null,
        takesThingsThatDoNotBelongToHimOrHer: null,
        refusesToShare: null,
      },
      submissionId: null,
      pscAdministrativeData: {
        client: {
          respondentRelationship: null,
          respondentName: null,
          caregiverDeclinedToRespond: 'N',
          caregiverDidNotRespondToAllQuestions: 'N',
          clientId: ibhisPatientId,
          practitionerReviewingNPI: null,
          providerNumber: null,
        },
        assessment: {
          adminCloseReason: null,
          date: null,
          type: null,
        },
        totalScore: null,
      },
      ibhisEpsdtPscId: null,
      status: 'New',
      comments: null,
      isToIbhis: false,
      updateUser: info?.username,
      updateDateTime: null,
    }),
    [ibhisPatientId, info?.username],
  );

  const css = useClass(styles);

  const methods = useHookForm({
    defaultValues: defaultInitialValues,
    shouldUnregister: true,
    dependencies: {
      clientId: ibhisPatientId,
      updateUser: info?.username,
    },
  });

  const { data: lookups, loading: loadingLookups } = useRequest({
    url: API_IBHIS.PSC_LOOKUP,
    shouldCache: true,
  });

  const options = useMemo(
    () =>
      sortBy(
        lookups?.pscqAs?.map((i) => ({ ...i, text: `${i?.description} (${i?.key})` })) || [],
        'key',
      ),
    [lookups],
  );

  const { request, loading: saving } = useRequest({
    url: API_IBHIS.PSC(),
    method: API_METHOD.POST,
    showAdditionalData: true,
    shouldRefreshAfterSave: false,
  });

  const handleSubmit = (isToIbhis) => {
    methods.clearErrors();
    methods.handleSubmit((values) => onSubmit(values, isToIbhis))();
  };

  const onSubmit = (values, isToIbhis) => {
    const totalScore = Object.values(values?.pscToolQ || {}).reduce(
      (acc, cur) => acc + Number(cur),
      0,
    );
    const payload = {
      ...(values || {}),
      pscAdministrativeData: {
        ...(values?.pscAdministrativeData || {}),
        totalScore,
      },
      isToIbhis,
      status: undefined,
    };

    request({
      payload,
      options: {
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('PSC')));
          fetchByQueryKey(API_IBHIS.PSC_HISTORY);
          setPSCSubmission();
          queryClient.setQueryData([QUERY_KEY.IBHIS_PSC_GET_DETAIL], undefined);
          resetForm();
        },
        onError: (err) => handleValidationBE({ setError: methods.setError }, err),
      },
    });
  };

  const resetForm = () => {
    setPSCSubmission();
    queryClient.setQueryData([QUERY_KEY.IBHIS_PSC_GET_DETAIL], undefined);
    methods.reset(defaultInitialValues);
  };

  useEffect(() => {
    if (pscSubmission) {
      methods.reset(pscSubmission);
    }
  }, [pscSubmission]);

  useEffect(() => {
    methods?.register('pscAdministrativeData.client.clientId');
    methods?.register('status');
    methods?.register('updateUser');
    methods?.register('updateDateTime');
    methods?.register('ibhisEpsdtPscId');
    methods?.register('submissionId');
  }, [methods?.register]);

  const filteredAdminCloseReasons = useMemo(() => {
    return (lookups?.adminCloseReasons || [])?.filter(
      (i) => i?.key === '1' || i?.key === '3' || i?.key === '4',
    );
  }, [lookups?.adminCloseReasons]);

  return (
    <CollapseVertical open title={t('PSC Input')} className="mb-24" id={ELEMENT_ID.IBHIS.PSC_INPUT}>
      <LoadingWrapper loading={loadingLookups || saving}>
        <HookFormProvider formId={FORM_ID.IBHIS_PSC_INPUT} {...methods}>
          <Stack tokens={{ childrenGap: 16 }}>
            <Stack tokens={{ childrenGap: 8 }}>
              <ResponsiveGrid>
                <ResponsiveItem md={3}>
                  <FieldInfo
                    name="pscAdministrativeData.client.clientId"
                    title={t('IBHIS Client ID')}
                  />
                </ResponsiveItem>
                <ResponsiveItem md={3}>
                  <FieldSelectorPrimary
                    isFast={false}
                    required
                    name="pscAdministrativeData.client.providerNumber"
                    title={t('IBHIS Location Code')}
                    options={formatToFluentOptions(lookups?.providerNumbers)}
                  />
                </ResponsiveItem>
                <ResponsiveItem md={3}>
                  <FieldSelectorPrimary
                    isFast={false}
                    required
                    name="pscAdministrativeData.assessment.type"
                    title={t('Assessment Type')}
                    options={formatToFluentOptions(lookups?.assessmentTypes)}
                  />
                </ResponsiveItem>
                <ResponsiveItem md={3}>
                  <WatchValues name="pscAdministrativeData.assessment.type">
                    {(value) =>
                      value === '5' && (
                        <FieldSelectorPrimary
                          isFast={false}
                          name="pscAdministrativeData.assessment.adminCloseReason"
                          title={t('If Administrative Close, Select Reason')}
                          required
                          options={formatToFluentOptions(filteredAdminCloseReasons)}
                        />
                      )
                    }
                  </WatchValues>
                </ResponsiveItem>
                <ResponsiveItem md={3}>
                  <FieldDate
                    required
                    name="pscAdministrativeData.assessment.date"
                    title={t('Fill Out On')}
                  />
                </ResponsiveItem>
                <ResponsiveItem md={3}>
                  <FieldText
                    name="pscAdministrativeData.client.practitionerReviewingNPI"
                    title={t('Assessing Practitioner NPI')}
                    required
                    maxLength={10}
                    type="text"
                    onChange={({ data }) => {
                      // Only allow numeric characters
                      const numericValue = data.replace(/[^0-9]/g, '');
                      methods.setValue('assessingPractitionerNPI', numericValue);
                    }}
                    helpText="Must contain exactly 10 numeric characters. Example: **********"
                  />
                </ResponsiveItem>
                <WatchValues name="pscAdministrativeData.assessment.type">
                  {(value) =>
                    value !== '5' && (
                      <React.Fragment>
                        <ResponsiveItem md={3}>
                          <FieldSelectorPrimary
                            isFast={false}
                            required
                            name="pscAdministrativeData.client.respondentRelationship"
                            title={t('Relationship to Child')}
                            options={formatToFluentOptions(lookups?.respondentRelationships)}
                          />
                        </ResponsiveItem>
                        <ResponsiveItem md={3}>
                          <FieldText
                            required
                            name="pscAdministrativeData.client.respondentName"
                            title={t('Respondent Name')}
                          />
                        </ResponsiveItem>
                      </React.Fragment>
                    )
                  }
                </WatchValues>
              </ResponsiveGrid>
              <WatchValues name="pscAdministrativeData.assessment.type" isFast={false}>
                {(value) =>
                  value !== '5' && (
                    <Fragment>
                      <ResponsiveGrid>
                        <ResponsiveItem md={12}>
                          <Stack
                            className={cn(
                              css.cmClass.backgroundGrey200,
                              css.cmClass.border,
                              'mt-8',
                              css.cmClass.borderRadius4,
                            )}
                            tokens={{ padding: 8 }}
                          >
                            {t(
                              'Emotional and physical health go together in children. Because parents are often the first to notice a problem with their child’s behavior, emotions, or learning, you may help your child get the best care possible by answering these questions. Please indicate which statement best describes your child.',
                            )}
                          </Stack>
                        </ResponsiveItem>
                        <ResponsiveItem md={8}>
                          <Stack className={css.cmClass.border}>
                            <strong
                              className={cn(
                                css.cmClass.backgroundGrey200,
                                css.cmClass.title,
                                'p-8',
                              )}
                            >
                              {t('Please mark under the heading that best describes your child')}
                            </strong>
                            <HorizontalRule />
                            <Stack
                              tokens={{ padding: 8 }}
                              className={cn('text-12-20', css.cmClass.colorSecondary)}
                            >
                              {t(
                                '* The Not Applicable option is available for children who are not of school age or are too young for school',
                              )}
                            </Stack>
                            <HorizontalRule />
                            <div className={cn(css.radioChoice, css.cmClass.backgroundGrey200)}>
                              <strong></strong>
                              <strong>Never (0)</strong>
                              <strong>Sometimes (1)</strong>
                              <strong>Often (2)</strong>
                            </div>
                            <HorizontalRule />
                            <div className={css.cmClass.backgroundColor}>
                              {QUESTIONS.map((f, index) => (
                                <div key={f.key} className={css.radioChoice}>
                                  <span>
                                    {index + 1}. {f.title}
                                  </span>
                                  {options?.map((i) => (
                                    <WatchValues name={f.key} key={`${i.key}-${f.title}`}>
                                      {(value) => (
                                        <FieldLayout name={f.key}>
                                          <input
                                            ref={methods.register(f.key).ref}
                                            type="radio"
                                            className={css.radio}
                                            value={value}
                                            checked={i.key === value}
                                            onChange={() => {
                                              methods.setValue(f.key, i.key);
                                            }}
                                          />
                                        </FieldLayout>
                                      )}
                                    </WatchValues>
                                  ))}
                                  {index !== QUESTIONS.length - 1 && (
                                    <HorizontalRule className="full" />
                                  )}
                                </div>
                              ))}
                            </div>
                            <HorizontalRule className="full" mt={4} />
                            <Stack
                              horizontal
                              tokens={{ childrenGap: 8, padding: '8px 8px 8px 16px' }}
                              verticalAlign="center"
                            >
                              <strong>{t('Total Score:')}</strong>
                              <WatchValues name="pscToolQ">
                                {(value) => (
                                  <strong>
                                    {Object.values(value || {}).reduce(
                                      (acc, cur) => acc + Number(cur),
                                      0,
                                    )}
                                  </strong>
                                )}
                              </WatchValues>
                            </Stack>
                          </Stack>
                        </ResponsiveItem>
                      </ResponsiveGrid>
                      <FieldCheckBox
                        fieldClassName="mt-16"
                        name="pscAdministrativeData.client.caregiverDeclinedToRespond"
                        label={t('Caregiver declined to respond')}
                        formatValue={(value) => value === 'Y'}
                        formatCheckedOption={(v) => (v ? 'Y' : 'N')}
                      />
                      <FieldCheckBox
                        fieldClassName="mt-8"
                        name="pscAdministrativeData.client.caregiverDidNotRespondToAllQuestions"
                        label={t('Caregiver did not respond to all required questions')}
                        formatValue={(value) => value === 'Y'}
                        formatCheckedOption={(v) => (v ? 'Y' : 'N')}
                      />
                    </Fragment>
                  )
                }
              </WatchValues>
              <StatusInfo withComments />
            </Stack>

            <Stack horizontal tokens={{ childrenGap: 16 }}>
              <PrimaryButton
                text={t('Submit To IBHIS')}
                data-istoibhis="true"
                onClick={() => handleSubmit(true)}
              />
              <PrimaryButton
                className={css.cmBtn.secondaryButton}
                text={t('Save as Incomplete')}
                data-istoibhis="false"
                onClick={() => handleSubmit(false)}
              />
              <CalloutConfirmation onOk={resetForm}>
                <DefaultButton text={t('Reset')} />
              </CalloutConfirmation>
            </Stack>
          </Stack>
        </HookFormProvider>
      </LoadingWrapper>
    </CollapseVertical>
  );
};

export default PSCInput;

const styles = (theme) => {
  return {
    radioChoice: {
      display: 'grid',
      alignItems: 'center',
      gridTemplateColumns: '3fr repeat(3, 1fr)',
      gap: 4,
      minHeight: 32,
      paddingTop: 4,
      span: {
        marginLeft: 16,
      },
      '.full': {
        gridColumn: '1/5',
      },
      input: {
        marginLeft: 16,
      },
      '&:nth-child(2n)': {
        backgroundColor: theme.darkMode
          ? theme.palette.backgroundColorShade2
          : theme.custom.grey100,
      },
      '&:hover': {
        backgroundColor: theme.darkMode
          ? theme.palette.backgroundColorShade3
          : theme.custom.grey200,
      },
    },
    radio: {
      height: 24,
      accentColor: theme.palette.primaryColor,
      transform: 'scale(1.3)',
    },
    description: { width: '33%' },
  };
};

const QUESTIONS = [
  { key: 'pscToolQ.complainsOfAchesAndPains', title: 'Complains of aches/pains' },
  { key: 'pscToolQ.spendsMoreTimeAlone', title: 'Spends more time alone' },
  { key: 'pscToolQ.tiresEasilyHasLittleEnergy', title: 'Tires easily, has little energy' },
  { key: 'pscToolQ.fidgetyUnableToSitStill', title: 'Fidgety, unable to sit still' },
  { key: 'pscToolQ.hasTroubleWithTeacher', title: 'Has trouble with a teacher' },
  { key: 'pscToolQ.lessInterestedInSchool', title: 'Less interested in school' },
  { key: 'pscToolQ.actsAsIfDrivenByAMotor', title: 'Acts as if driven by a motor' },
  { key: 'pscToolQ.daydreamsTooMuch', title: 'Daydreams too much' },
  { key: 'pscToolQ.distractedEasily', title: 'Distracted easily' },
  { key: 'pscToolQ.isAfraidOfNewSituations', title: 'Is afraid of new situations' },
  { key: 'pscToolQ.feelsSadUnhappy', title: 'Feels sad, unhappy' },
  { key: 'pscToolQ.isIrritableAngry', title: 'Is irritable, angry' },
  { key: 'pscToolQ.feelsHopeless', title: 'Feels hopeless' },
  { key: 'pscToolQ.hasTroubleConcentrating', title: 'Has trouble concentrating' },
  { key: 'pscToolQ.lessInterestInFriends', title: 'Less interest in friends' },
  { key: 'pscToolQ.fightsWithOtherChildren', title: 'Fights with others' },
  { key: 'pscToolQ.absentFromSchool', title: 'Absent from school' },
  { key: 'pscToolQ.schoolGradesDropping', title: 'School grades dropping' },
  { key: 'pscToolQ.isDownOnHimOrHerself', title: 'Is down on him or herself' },
  {
    key: 'pscToolQ.visitsTheDoctorWithDoctorFindingNothingWrong',
    title: 'Visits doctor with doctor finding nothing wrong',
  },
  { key: 'pscToolQ.hasTroubleSleeping', title: 'Has trouble sleeping' },
  { key: 'pscToolQ.worriesALot', title: 'Worries a lot' },
  {
    key: 'pscToolQ.wantsToBeWithYouMoreThanBefore',
    title: 'Wants to be with you more than before',
  },
  { key: 'pscToolQ.feelsHeOrSheIsBad', title: 'Feels he or she is bad' },
  { key: 'pscToolQ.takesUnnecessaryRisks', title: 'Takes unnecessary risks' },
  { key: 'pscToolQ.getsHurtFrequently', title: 'Gets hurt frequently' },
  { key: 'pscToolQ.seemsToBeHavingLessFun', title: 'Seems to be having less fun' },
  {
    key: 'pscToolQ.actsYoungerThanChildrenHisOrHerAge',
    title: 'Acts younger than children his or her age',
  },
  { key: 'pscToolQ.doesNotListenToRules', title: 'Does not listen to rules' },
  { key: 'pscToolQ.doesNotShowFeelings', title: 'Does not show feelings' },
  {
    key: 'pscToolQ.doesNotUnderstandOtherPeoplesFeelings',
    title: 'Does not understand other people’s feelings',
  },
  { key: 'pscToolQ.teasesOthers', title: 'Teases others' },
  {
    key: 'pscToolQ.blamesOthersForHisOrHerTroubles',
    title: 'Blames others for his or her troubles',
  },
  {
    key: 'pscToolQ.takesThingsThatDoNotBelongToHimOrHer',
    title: 'Takes things that do not belong to him or her',
  },
  { key: 'pscToolQ.refusesToShare', title: 'Refuses to share' },
];
