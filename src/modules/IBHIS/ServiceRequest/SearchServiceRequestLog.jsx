import { PrimaryButton, Stack } from '@fluentui/react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import React, { useContext, useState } from 'react';
import { t } from 'utils/string';
import { SERVICE_REQUEST_ACTION } from './constant';
import ResponsiveGrid from 'components/Layout/Responsive/Container';
import ResponsiveItem from 'components/Layout/Responsive/Item';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import FieldText from 'components/HookForm/FieldText';
import FieldDate from 'components/HookForm/FieldDate';
import { API_IBHIS, API_METHOD } from 'constants/urlRequest';
import { useModal, useRequest } from 'hooks';
import Table from 'components/Table';
import ModalConfirm from 'components/Modal/ModalConfirm';
import { TEXT_TEMPLATE } from 'constants/texts';
import { MODAL_SIZE } from 'constants/modal';
import { IBHISContext } from 'contexts/IBHISContext';
import { toast } from 'react-toastify';
import { getStatus } from 'constants/statusRender';
import useHookForm from 'components/HookForm/useHookForm';
import WatchValues from 'components/HookForm/WatchValues';
import { scrollIntoViewId } from 'utils/globalScroll';
import ELEMENT_ID from 'constants/elementId';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import QUERY_KEY from 'constants/queryKey';
import HookFormProvider from 'components/HookForm/HookFormProvider';

const SEARCH_TYPE = {
  SearchByClientId: 'SearchByClientId',
  SearchByPotentialClient: 'SearchByPotentialClient',
  SearchByProgramIdRequestDate: 'SearchByProgramIdRequestDate',
};

const SEARCH_OPTIONS = [
  {
    key: SEARCH_TYPE.SearchByPotentialClient,
    text: t('By Name And DOB'),
  },
  {
    key: SEARCH_TYPE.SearchByClientId,
    text: t('By ClientId'),
  },
  {
    key: SEARCH_TYPE.SearchByProgramIdRequestDate,
    text: t('By Program'),
  },
];
function SearchServiceRequestLog() {
  const [params, setParams] = useState();
  const { showModal } = useModal();
  const methods = useHookForm({
    defaultValues: { SearchType: SEARCH_TYPE.SearchByPotentialClient },
  });
  const { setEditServiceRequest } = useContext(IBHISContext);

  const apiSearch = useRequest({
    key: QUERY_KEY.IBHIS_SERVICE_REQUEST,
    url: API_IBHIS.SEARCH_SERVICE_REQUEST,
    params: { ...DEFAULT_PAGINATION_PARAMS, OrderBy: 'requestDate' },
    requiredParams: {
      ...(params?.SearchType ? { SearchType: params?.SearchType } : {}),
      ...(params?.PotentialClientLastName
        ? { PotentialClientLastName: params?.PotentialClientLastName }
        : {}),
      ...(params?.PotentialClientFirstName
        ? { PotentialClientFirstName: params?.PotentialClientFirstName }
        : {}),
      ...(params?.PotentialClientDOB ? { PotentialClientDOB: params?.PotentialClientDOB } : {}),
      ...(params?.IbhisClientId ? { IbhisClientId: params?.IbhisClientId } : {}),
      ...(params?.ProgramId ? { ProgramId: params?.ProgramId } : {}),
      ...(params?.RequestDateFrom ? { RequestDateFrom: params?.RequestDateFrom } : {}),
      ...(params?.RequestDateTo ? { RequestDateTo: params?.RequestDateTo } : {}),
      ...(params?.isFromIbhis ? { isFromIbhis: params?.isFromIbhis?.toString() } : {}),
    },
    autoRefetch: true,
    enabled: !!params,
  });

  const { request: requestDelete } = useRequest({ method: API_METHOD.DELETE, enabled: false });

  const onSearch = (isFromIbhis = false) => {
    const values = methods.getValues();
    const searchValues = { ...(values || {}), isFromIbhis, Page: 0 };
    setParams(searchValues);
    setTimeout(() => apiSearch?.refetch(), 100);
  };

  const onDelete = (item) => {
    showModal({
      content: (
        <ModalConfirm
          message={t(TEXT_TEMPLATE.DELETE_CONFIRMATION('this Service Request'))}
          onOk={() =>
            requestDelete({
              url: API_IBHIS.DELETE_SERVICE_REQUEST(item?.ibhisServiceRequestId),
              options: {
                onSuccess: () => {
                  toast.success(t(TEXT_TEMPLATE.DELETE_SUCCESSFULLY('Service Request')));
                  apiSearch?.refetch();
                },
              },
            })
          }
        />
      ),
      size: MODAL_SIZE.X_SMALL,
    });
  };

  const onEdit = (item) => {
    setEditServiceRequest({
      isFromIbhis: params?.isFromIbhis,
      ibhisServiceRequestId: item?.ibhisServiceRequestId,
    });
    scrollIntoViewId(ELEMENT_ID.IBHIS.SERVICE_REQUEST_LOG_INPUT_ID, 'start');
  };

  return (
    <CollapseVertical
      open
      title={t('Search DMH Service Request Logs')}
      className="mb-24"
      icon="assets/images/ibhis-search-dot.png"
      id={SERVICE_REQUEST_ACTION.SEARCH_SERVICE_REQUEST_LOG}
    >
      <Stack tokens={{ childrenGap: 16 }}>
        <HookFormProvider {...methods}>
          <ResponsiveGrid>
            <ResponsiveItem md={2}>
              <FieldSelectorPrimary
                options={SEARCH_OPTIONS}
                title={t('Search Using')}
                name="SearchType"
                onChange={({ data }) => {
                  methods.setValue('SearchType', data?.key);
                  // Reset all search fields when search type changes
                  methods.setValue('PotentialClientFirstName', null);
                  methods.setValue('PotentialClientLastName', null);
                  methods.setValue('PotentialClientDOB', null);
                  methods.setValue('IbhisClientId', null);
                  methods.setValue('ProgramId', null);
                  methods.setValue('RequestDateFrom', null);
                  methods.setValue('RequestDateTo', null);
                  // Reset params to trigger new search
                  setParams(null);
                }}
              />
            </ResponsiveItem>
            <WatchValues name="SearchType">
              {(value) => {
                if (value === SEARCH_TYPE.SearchByPotentialClient)
                  return (
                    <React.Fragment>
                      <ResponsiveItem md={3}>
                        <FieldText
                          required
                          title={t('Client First Name')}
                          name="PotentialClientFirstName"
                        />
                      </ResponsiveItem>
                      <ResponsiveItem md={3}>
                        <FieldText
                          required
                          title={t('Client Last Name')}
                          name="PotentialClientLastName"
                        />
                      </ResponsiveItem>
                      <ResponsiveItem md={2}>
                        <FieldDate required title={t('Client DOB')} name="PotentialClientDOB" />
                      </ResponsiveItem>
                    </React.Fragment>
                  );

                if (value === SEARCH_TYPE.SearchByClientId)
                  return (
                    <ResponsiveItem md={3}>
                      <FieldText required title={t('Client ID')} name="IbhisClientId" />
                    </ResponsiveItem>
                  );
                if (value === SEARCH_TYPE.SearchByProgramIdRequestDate)
                  return (
                    <React.Fragment>
                      <ResponsiveItem md={3}>
                        <FieldText required title={t('Program ID')} name="ProgramId" />
                      </ResponsiveItem>
                      <ResponsiveItem md={3}>
                        <FieldDate required title={t('Request Date From')} name="RequestDateFrom" />
                      </ResponsiveItem>
                      <ResponsiveItem md={3}>
                        <FieldDate required title={t('Request Date To')} name="RequestDateTo" />
                      </ResponsiveItem>
                    </React.Fragment>
                  );
              }}
            </WatchValues>

            <ResponsiveItem md={2} className="self-end">
              <Stack horizontal tokens={{ childrenGap: 8 }}>
                <PrimaryButton
                  text={t('Search')}
                  iconProps={{ iconName: 'Search' }}
                  type="submit"
                  onClick={() => onSearch()}
                />
                <PrimaryButton
                  disabled={
                    !(apiSearch?.data?.items?.length === 0 && params?.isFromIbhis === false)
                  }
                  text={t('Search From IBHIS')}
                  iconProps={{ iconName: 'Search' }}
                  type="submit"
                  onClick={() => {
                    onSearch(true);
                  }}
                />
              </Stack>
            </ResponsiveItem>
          </ResponsiveGrid>
        </HookFormProvider>

        <Table
          columns={columns}
          items={apiSearch?.data?.items || []}
          loading={apiSearch?.loading}
          metadata={apiSearch?.params}
          onMetadataChange={apiSearch?.updateParams}
          onDelete={onDelete}
          onEdit={onEdit}
          refetch={apiSearch?.refetch}
          totalItems={apiSearch?.data?.totalItems}
        />
      </Stack>
    </CollapseVertical>
  );
}

const columns = [
  { name: 'Service Request ID', fieldName: 'ibhisServiceRequestId', sortable: true },
  {
    name: 'Request Date',
    fieldName: 'requestDate',
    renderItem: ({ request: { requestDate } }, _, c, render) => render({ date: requestDate }),
    sortable: true,
  },
  {
    name: 'Request Respond Staff',
    fieldName: 'requestRespondStaff',
    renderItem: ({ request: { requestRespondStaff } }) => requestRespondStaff,
    sortable: true,
  },
  {
    name: 'Program Of Service',
    fieldName: 'programOfService',
    renderItem: ({ request: { programOfService } }) => programOfService,
    sortable: true,
  },
  {
    name: 'Status',
    fieldName: 'status',
    renderItem: ({ status }) => getStatus(status),
    sortable: true,
  },
  { key: 'Action', name: 'Action', fieldName: 'action' },
];

export default SearchServiceRequestLog;
