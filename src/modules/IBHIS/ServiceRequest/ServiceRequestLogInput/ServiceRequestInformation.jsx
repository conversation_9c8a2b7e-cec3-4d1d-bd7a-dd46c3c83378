import React, { useMemo } from 'react';
import { Stack } from '@fluentui/react';
import { t } from 'utils/string';
import { useClass } from 'hooks';
import cn from 'classnames';
import ResponsiveGrid from 'components/Layout/Responsive/Container';
import ResponsiveItem from 'components/Layout/Responsive/Item';
import FieldDate from 'components/HookForm/FieldDate';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import FieldChoice from 'components/HookForm/FieldChoice';
import FieldText from 'components/HookForm/FieldText';
import FieldTime from 'components/HookForm/FieldTime';
import { formatToFluentOptions } from 'utils';
import { REFERRING_PARTY_ROLE_OPTIONS } from '../constant';
import WatchValues from 'components/HookForm/WatchValues';
import { useFormContext, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';

const ServiceRequestInformation = ({ lookupData }) => {
  const css = useClass();
  const { setValue } = useFormContext();
  const ibhisClientId = useWatch({ name: 'ibhisClientId' });
  const optionsProgramOfService = useMemo(() => {
    return (lookupData?.assignedSites || [])?.map((item) => ({
      key: item?.ibhisProgramId,
      text: `${item?.assignmentSiteName} ${
        item?.ibhisProgramName ? `- ${item?.ibhisProgramName}` : ''
      }`,
      assignmentSiteName: item?.assignmentSiteName,
    }));
  }, [lookupData?.assignedSites]);

  return (
    <Stack tokens={{ childrenGap: 16 }}>
      <Stack
        className={cn(css.cmClass.backgroundGrey200, css.cmClass.borderRadius8)}
        tokens={{ padding: '12px 16px', childrenGap: 16 }}
      >
        <div className={'weight-600'}>{t('Service Request Information')}</div>

        <ResponsiveGrid>
          <ResponsiveItem md={3}>
            <FieldDate
              required
              title={t('Date of Request')}
              name="request.requestDate"
              isRenderRightSide={false}
              maxDate={new Date()}
            />
          </ResponsiveItem>
          <ResponsiveItem md={3}>
            <FieldTime required title={t('Time of Request')} name="request.requestTime" />
          </ResponsiveItem>
          <ResponsiveItem md={3}>
            <FieldSelectorPrimary
              title={t('Request Type')}
              name="request.requestType"
              required
              options={formatToFluentOptions(lookupData?.requestTypes)}
            />
          </ResponsiveItem>
          <ResponsiveItem md={3}>
            <WatchValues name="request.programOfService">
              {(programOfServiceValue) => (
                <FieldSelectorPrimary
                  title={t('Request Program Of Service')}
                  name="request.programOfService"
                  required
                  options={
                    !!ibhisClientId && lookupData?.assignedSites?.length
                      ? optionsProgramOfService
                      : formatToFluentOptions(lookupData?.programOfServices)
                  }
                  onChange={({ data }) => {
                    if (!data?.key) {
                      toast.warning(
                        t(
                          `No IBHIS Program is assigned to this ${data?.assignmentSiteName} site. Please visit the Support - IBHIS page to assign one.`,
                        ),
                      );

                      programOfServiceValue === null
                        ? setValue('request.programOfService', undefined)
                        : setValue('request.programOfService', null);
                    } else {
                      setValue('request.programOfService', data?.key);
                    }
                  }}
                />
              )}
            </WatchValues>
          </ResponsiveItem>
        </ResponsiveGrid>
        <ResponsiveGrid>
          <ResponsiveItem md={3}>
            <FieldText title={t('Referring Party First Name')} required name="referringFirstName" />
          </ResponsiveItem>
          <ResponsiveItem md={3}>
            <FieldText title={t('Referring Party Last name')} required name="referringLastName" />
          </ResponsiveItem>
          <ResponsiveItem md={3}>
            <FieldText
              mask="(999) 999 - 9999"
              placeholder={t('(###) ### - ####')}
              title={t('Referring Contact number')}
              name="referringPhone"
            />{' '}
          </ResponsiveItem>
          <ResponsiveItem md={3}>
            <FieldChoice
              horizontal
              options={formatToFluentOptions(lookupData?.urgentRequestTypes)}
              formatCheckedOption={(item) => item.key}
              title={t('Is this an urgent request?')}
              name="request.urgentRequest"
              required
            />
          </ResponsiveItem>
        </ResponsiveGrid>
        <ResponsiveGrid>
          <ResponsiveItem md={3}>
            <WatchValues name="referringPartyRoleType">
              {(value) => (
                <FieldSelectorPrimary
                  title={t('Referring Party Role Type')}
                  name="referringPartyRoleType"
                  required
                  options={REFERRING_PARTY_ROLE_OPTIONS}
                  onChange={({ _, data }) => {
                    setValue('referringPartyRoleType', data?.key);
                    if (data?.key !== value) {
                      setValue('referringPartyRole', null);
                      setValue('school', null);
                      setValue('referringFacility', null);
                      setValue('roleType', null);
                      return;
                    }
                  }}
                />
              )}
            </WatchValues>
          </ResponsiveItem>
          <WatchValues name="referringPartyRoleType">
            {(value) => {
              let options = [];
              if (value === 'HealthProviderSchool')
                options = formatToFluentOptions(lookupData?.healthProviderSchoolTypes);
              if (value === 'AccessSelf')
                options = formatToFluentOptions(lookupData?.accessSelfTypes);
              if (value === 'Roles') options = formatToFluentOptions(lookupData?.otherRolesTypes);
              return (
                (value === 'HealthProviderSchool' ||
                  value === 'AccessSelf' ||
                  value === 'Roles') && (
                  <ResponsiveItem md={3}>
                    <FieldSelectorPrimary
                      required
                      title={t('Referring Party Role')}
                      name="referringPartyRole"
                      options={options}
                      onChange={({ _, data }) => {
                        setValue('referringPartyRole', data?.key);
                        if (data?.key !== 'School') {
                          setValue('school', null);
                          return;
                        }
                      }}
                    />
                  </ResponsiveItem>
                )
              );
            }}
          </WatchValues>
          <WatchValues name="referringPartyRole">
            {(value) =>
              value === 'School' && (
                <ResponsiveItem md={3}>
                  <FieldSelectorPrimary
                    title={t('School')}
                    name="school"
                    required
                    options={formatToFluentOptions(lookupData?.countySchools)}
                  />
                </ResponsiveItem>
              )
            }
          </WatchValues>
          <WatchValues name="referringPartyRoleType">
            {(value) =>
              (value === 'Other' || value === 'CollateralFamilyMember') && (
                <ResponsiveItem md={3}>
                  <FieldText title={t('Role Type')} required name="roleType" />
                </ResponsiveItem>
              )
            }
          </WatchValues>
        </ResponsiveGrid>
        <ResponsiveGrid>
          <ResponsiveItem md={3}>
            <FieldText
              title={t('Initiating User (Staff Responding to Request)')}
              name="request.requestRespondStaff"
              required
            />
          </ResponsiveItem>
          <WatchValues name="referringPartyRoleType">
            {(value) =>
              (value === 'HealthProviderSchool' || value === 'Roles') && (
                <ResponsiveItem md={3}>
                  <FieldText
                    required
                    title={t('Referring Facility / Site / School')}
                    name="referringFacility"
                  />
                </ResponsiveItem>
              )
            }
          </WatchValues>
          <ResponsiveItem md={3}>
            <FieldText title={t('Reason for Request')} name="request.requestReason" />
          </ResponsiveItem>
        </ResponsiveGrid>
        <ResponsiveGrid>
          <ResponsiveItem md={12}>
            <FieldChoice
              horizontal
              options={formatToFluentOptions(lookupData?.clientAwareOfReferralTypes)}
              formatCheckedOption={(item) => item.key}
              title={t('Is the client/potential client aware of the referral?')}
              name="clientAwareOfReferral"
            />
          </ResponsiveItem>
        </ResponsiveGrid>
      </Stack>
    </Stack>
  );
};

export default ServiceRequestInformation;
