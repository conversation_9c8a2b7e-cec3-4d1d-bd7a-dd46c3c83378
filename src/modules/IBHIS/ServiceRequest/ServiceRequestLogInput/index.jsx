import React, { useContext, useEffect } from 'react';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { t } from 'utils/string';
import { useRequest } from 'hooks';
import PatientInformation from './PatientInformation';
import ServiceRequestInformation from './ServiceRequestInformation';
import AppointmentInformation from './AppointmentInformation';
import MedicationAppointmentInformation from './MedicationAppointmentInformation';
import { API_IBHIS, API_METHOD } from 'constants/urlRequest';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import ScreenerQuestions from './ScreenerQuestions';
import { TEXT_TEMPLATE } from 'constants/texts';
import { IBHISContext } from 'contexts/IBHISContext';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import useHookForm from 'components/HookForm/useHookForm';
import { useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import { formatPhoneNumber, handleValidationBE } from 'utils/form';
import ELEMENT_ID from 'constants/elementId';
import { isNil } from 'utils';
import { fetchByQueryKey } from 'apis/queryClient';
import QUERY_KEY from 'constants/queryKey';
import { scrollIntoViewId } from 'utils/globalScroll';
import { SERVICE_REQUEST_ACTION } from '../constant';
import HookFormProvider from 'components/HookForm/HookFormProvider';
import { FORM_ID } from 'constants/formId';

const defaultInitialValues = {
  request: {
    serviceRequestId: null,
    programOfService: null,
    requestDate: null,
    requestTime: null,
    requestReason: null,
    requestRespondStaff: null,
    comments: null,
    urgentRequest: null,
    RequestType: null,
  },
  clientType: 'New',
  ibhisClientId: null,
  potentialClientLastName: null,
  potentialClientFirstName: null,
  potentialClientDOB: null,
  potentialClientPhone: null,
  insuranceStatus: null,
  preferredLanguage: null,
  legalGuardianName: null,
  legalGuardianPhone: null,
  legalGuardianLanguage: null,
  serviceAgreementDate: null,
  releaseFrom: {
    releaseFromType: null,
    yesReleaseFromType: null,
    releaseDischargeDate: null,
    inpatientFacilityCode: null,
    unlistedInpatientFacilityCode: null,
  },
  receivingMentalHealthServices: null,
  agencyName: null,
  onPsychMedInLast30Days: null,
  ifOnPsychMedInLast30DaysYes: null,
  medApptPracitionerNPI: null,
  medApptProgramOfService: null,
  medApptDate: null,
  medApptTime: null,
  earlierMedApptOffered: null,
  firstOfferedMedApptDate: null,
  secondOfferedMedApptDate: null,
  thirdOfferedMedApptDate: null,
  acceptedFirstOfferedMedAppt: null,
  referringLastName: null,
  referringFirstName: null,
  referringPhone: null,
  clientAwareOfReferral: null,
  referringPartyRole: null,
  referringFacility: null,
  school: null,
  roleType: null,
  referringPartyRoleType: null,
  disposition: {
    dispositionType: null,
    dispositionDetails: null,
    dispositionChoice: null,
  },
  apptPractitionerNPI: null,
  apptProgramOfService: null,
  apptDate: null,
  apptTime: null,
  closureReason: null,
  earlierApptOfferedType: null,
  firstOfferedApptDate: null,
  secondOfferedApptDate: null,
  thirdOfferedApptDate: null,
  clientAgeType: null,
  clientAgeYouth020Type: null,
  screeningTool: {
    q1: null,
    q2: null,
    q3: null,
    q4: null,
    q5: null,
    q6: null,
    q7: null,
    q7a: null,
    q7b: null,
    q8: null,
    q9: null,
    q10: null,
    q11: null,
    q12: null,
    q13: null,
    q14: null,
    q15: null,
    q16: null,
    q17: null,
    currentlyOrEverBeenInJuvenileHallOnProbationUnderCourtSupervision: null,
    dateYouSawYourPediatricianOrPrimaryCareDoctor: null,
  },
  ibhisServiceRequestId: 0,
  status: null,
  completedBy: null,
  serviceRequestComments: null,
  isToIbhis: false,
  programId: null,
  subjectId: null,
};
const ServiceRequestLogInput = () => {
  const { editServiceRequest, setEditServiceRequest } = useContext(IBHISContext);
  const methods = useHookForm({ defaultValues: defaultInitialValues });
  const ibhisClientId = useWatch({ control: methods.control, name: 'ibhisClientId' });
  const apiLookup = useRequest({
    key: [API_IBHIS.GET_SERVICE_REQUEST_LOOKUPS, 'lookup-service-request'],
    url: API_IBHIS.GET_SERVICE_REQUEST_LOOKUPS,
    requiredParams: { ...(!!ibhisClientId ? { ibhisClientId } : {}) },
    autoRefetch: true,
  });

  const addServiceRequest = useRequest({
    url: API_IBHIS.ADD_SERVICE_REQUEST,
    method: API_METHOD.POST,
    showAdditionalData: true,
  });

  const getServiceRequest = useRequest({
    url: API_IBHIS.GET_SERVICE_REQUEST,
    requiredParams: {
      ibhisServiceRequestId: editServiceRequest?.ibhisServiceRequestId,
      ...(!isNil(editServiceRequest?.isFromIbhis)
        ? { isFromIbhis: editServiceRequest?.isFromIbhis?.toString() }
        : {}),
    },
    autoRefetch: true,
    enabled: !!editServiceRequest?.ibhisServiceRequestId,
  });

  useEffect(() => {
    if (getServiceRequest?.data) {
      methods.reset({
        ...getServiceRequest.data,
        ...(!getServiceRequest.data?.screeningTool ? { screeningTool: {} } : {}),
      });
    }
  }, [getServiceRequest?.data]);

  const onSubmit = (values, event) => {
    const submitter = event.nativeEvent.submitter;
    const isToIbhis = submitter?.dataset.istoibhis === 'true';
    const potentialClientPhone = formatPhoneNumber(values.potentialClientPhone);
    const legalGuardianPhone = formatPhoneNumber(values.legalGuardianPhone);
    const referringPhone = formatPhoneNumber(values.referringPhone);
    addServiceRequest.request({
      payload: {
        ...values,
        isToIbhis,
        status: undefined,
        potentialClientPhone,
        legalGuardianPhone,
        referringPhone,
      },
      options: {
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('Service Request')));
          fetchByQueryKey(QUERY_KEY.IBHIS_SERVICE_REQUEST);
          scrollIntoViewId(SERVICE_REQUEST_ACTION.SEARCH_SERVICE_REQUEST_LOG);
          onReset();
        },
        onError: (e) => handleValidationBE({ setError: methods.setError }, e),
      },
    });
  };

  const handleSubmit = (e) => {
    methods.clearErrors();
    methods.handleSubmit(onSubmit)(e);
  };

  const onReset = () => {
    setEditServiceRequest({});
    methods.reset(defaultInitialValues);
  };

  return (
    <CollapseVertical
      open
      title={t('Create Service Request')}
      className="mb-24"
      id={ELEMENT_ID.IBHIS.SERVICE_REQUEST_LOG_INPUT_ID}
      icon="assets/images/modern-edit.png"
    >
      <LoadingWrapper
        loading={apiLookup?.loading || addServiceRequest?.loading || getServiceRequest?.loading}
      >
        <HookFormProvider {...methods} formId={FORM_ID.IBHIS_SERVICE_REQUEST_LOG_INPUT}>
          <form onSubmit={handleSubmit}>
            <Stack tokens={{ childrenGap: 16 }}>
              <PatientInformation lookupData={apiLookup?.data} />
              <ServiceRequestInformation lookupData={apiLookup?.data} />
              <AppointmentInformation lookupData={apiLookup?.data} />
              <MedicationAppointmentInformation lookupData={apiLookup?.data} />
              <ScreenerQuestions lookupData={apiLookup?.data} />

              <Stack horizontal tokens={{ childrenGap: 16 }}>
                <PrimaryButton
                  text={t('Send Service Request')}
                  data-isbutton="button"
                  data-isToIbhis="true"
                  type="submit"
                />
                <PrimaryButton
                  text={t('Save As Incomplete')}
                  data-isbutton="button"
                  data-isToIbhis="false"
                  type="submit"
                />
                <CalloutConfirmation onOk={onReset}>
                  <DefaultButton text={t('Reset')} data-isbutton="button" />
                </CalloutConfirmation>
              </Stack>
            </Stack>
          </form>
        </HookFormProvider>
      </LoadingWrapper>
    </CollapseVertical>
  );
};

export default ServiceRequestLogInput;
