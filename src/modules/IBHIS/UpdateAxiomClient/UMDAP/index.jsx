import React, { useContext } from 'react';
import { t } from 'utils/string';
import <PERSON><PERSON>ield from 'components/Form/Field';
import { PrimaryButton, Stack } from '@fluentui/react';
import { useClass, useRequest } from 'hooks';
import { Formik } from 'formik';
import { API_IBHIS, API_METHOD } from 'constants/urlRequest';
import FieldText from 'components/Form/FieldText';
import FieldDate from 'components/Form/FieldDate';
import QUERY_KEY from 'constants/queryKey';
import { handleValidationBE } from 'utils/form';
import { toast } from 'react-toastify';
import { TEXT_TEMPLATE } from 'constants/texts';
import { IBHISContext } from 'contexts/IBHISContext';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import ClientInfo from '../ClientInfo';
import cn from 'classnames';
import StatusInfo from '../StatusInfo';

function UMDAP() {
  const css = useClass();
  const { ibhisPatientId } = useContext(IBHISContext);

  const apiSave = useRequest({
    url: API_IBHIS.UMDAP,
    method: API_METHOD.POST,
  });

  const { data, loading, updateParams } = useRequest({
    key: `${QUERY_KEY.IBHIS_UMDAP}-${ibhisPatientId}`,
    url: API_IBHIS.UMDAP,
    requiredParams: { ibhisPatientId },
    autoRefetch: true,
    shouldCache: true,
  });

  const onSubmit = (form, isToIbhis) => {
    apiSave.request({
      payload: { ...(form?.values || {}), isToIbhis, status: undefined },
      options: {
        onSuccess: () => {
          updateParams({ isFromIbhis: false });
          toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY('UMDAP Information'));
        },
        onError: (err) => handleValidationBE(form, err),
      },
    });
  };

  return (
    <Formik initialValues={{ ...data, ibhisPatientId }} onSubmit={() => {}} enableReinitialize>
      <LoadingWrapper loading={loading || apiSave.loading}>
        <ClientInfo
          showEpisode={false}
          lastSync={data?.updateDateTime}
          refetch={() => updateParams({ isFromIbhis: true })}
        />
        <div className={cn('mt-16 mb-16', css.cmClass.grid3Columns)}>
          <FieldText
            title={t('Number Of Dependents Upon Income')}
            name="numberOfDependentsUponIncome"
            type="number"
          />
          <FieldText
            title={t('Adjusted Monthly Income')}
            name="adjustedMonthlyIncome"
            type="number"
          />
          <FieldText
            title={t('Annual Liability')}
            name="annualLiability"
            type="text"
            formatValue={(v) => {
              const num = Number(v);
              return isNaN(num) ? v : num.toFixed(2).replace('.', ',');
            }}
            onBlur={(e, form) => {
              let val = e?.target?.value?.trim();
              if (val && !val.includes(',') && !val.includes('.')) {
                val = val + ',00';
                e.target.value = val;
              }
              const normalizedVal = val?.replace(',', '.').replace(/[^0-9.-]/g, '');
              const num = parseFloat(normalizedVal);
              form?.setFieldValue('annualLiability', isNaN(num) ? null : num);
            }}
          />
          <FieldText title={t('Responsible Person')} name="responsiblePerson" />
          <FieldText title={t('Client Note')} name="clientNote" />
          <FieldDate title={t('Annual Charge Period')} name="annualChargePeriod" required />
          {/* <FieldText
            title={t('Client Additional UMDAP Unique ID')}
            name="clientAdditionalUMDAPUniqueID"
          /> */}
          {/* <FieldText
            title={t('Client Responsible Legal Entity')}
            name="clientResponsibleLegalEntity"
          /> */}
          {/* <FieldDate title={t('Record Creation Date')} name="recordCreationDate" /> */}
        </div>
        <StatusInfo />
        <MyField>
          {({ form }) => (
            <Stack horizontal tokens={{ childrenGap: 16 }} className="mt-16">
              <PrimaryButton text={t('Submit to IBHIS')} onClick={() => onSubmit(form, true)} />
              <PrimaryButton
                className={css.cmBtn.secondaryButton}
                text={t('Save as Incomplete')}
                onClick={() => onSubmit(form, false)}
              />
            </Stack>
          )}
        </MyField>
      </LoadingWrapper>
    </Formik>
  );
}

export default UMDAP;
