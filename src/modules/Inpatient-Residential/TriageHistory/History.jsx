import React, { useCallback, useMemo } from 'react';
import { ContextualMenuItemType, Stack } from '@fluentui/react';
import cn from 'classnames';
import GroupTableDetail from 'components/GroupDetailTable';
import Table from 'components/Table';
import QUERY_KEY from 'constants/queryKey';
import { API_PAGE_ACTION, API_TRIAGE_OBSERVATION } from 'constants/urlRequest';
import { useClass, useRequest } from 'hooks';
import { useSelector } from 'react-redux';
import useTriage from './useTriage';
import { t } from 'utils/string';
import { ACTION_KEY, ACTION_NAME, TRIAGE_ACTION_KEYS } from 'components/TabsBottom/constants';
import { SECURITY_FUNCTIONS } from 'constants/modules';

const TriageHistory = () => {
  const css = useClass(styles);
  const openBottomTabs = useSelector((state) => state.tabsBottom.openBottomTabs);
  const Content = useMemo(() => <HistoryContent />, []);
  return (
    <Stack className={cn(css.wrapper, { [css.hasBottomBar]: openBottomTabs })}>{Content}</Stack>
  );
};

const HistoryContent = () => {
  const filterValue = useSelector((state) => state.inpatient.filterValue);
  const columns = useSelector((state) => state.inpatient.columns);
  const { onMenuClick } = useTriage();
  const items = useSelector((state) => state.settings.navigationItems);
  const security = useSelector((state) => state.settings.pageSecurity);

  const apiMenu = useRequest({
    key: `${API_PAGE_ACTION.DEFAULT}-TriageObservationModule`,
    url: API_PAGE_ACTION.DEFAULT,
    params: { type: 'TriageObservationModule' },
  });

  const { data, loading } = useRequest({
    key: QUERY_KEY.OBSERVATION_MODULE_LIST,
    url: API_TRIAGE_OBSERVATION.SEARCH_PATIENT,

    requiredParams: {
      startDate: filterValue?.startDate,
      endDate: filterValue?.endDate,
      providerId: filterValue?.providerId,
      status: filterValue?.status?.toString?.(),
      ...(filterValue?.patientId ? { patientId: filterValue?.patientId } : {}),
      ...(filterValue?.patientName ? { patientName: filterValue?.patientName } : {}),
    },
    autoRefetch: true,
  });

  const actionItems = useCallback(
    (data, callback) =>
      TRIAGE_ACTION_KEYS.map((i) => {
        if (typeof i !== 'string') return i;

        const action = ACTION_NAME[i];
        if (
          (!Object.values(SECURITY_FUNCTIONS)?.includes(i) && !action?.route) ||
          security?.find((item) => item?.pageKey === i)?.view ||
          items?.find((item) => item?.key === action?.route)
        ) {
          return {
            key: i,
            text: action?.getTitle?.(data) || action?.title,
            onClick: () => onMenuClick(i, data, callback),
          };
        } else return;
      }).filter(Boolean),
    [],
  );

  const getMenu = (data, callback) => ({
    items: [
      ...actionItems(
        { ...data, id: `${data.chairId}-${data.patientId}`, name: data.patientName },
        callback,
      ),
      ...(apiMenu.data?.length
        ? [
            {
              key: 'dynamicform',
              text: t('Dynamic Form'),
              itemType: ContextualMenuItemType.Header,
            },
            ...apiMenu.data.map((i) => ({
              key: i.pageActionOptionId,
              text: i.optionName,
              onClick: () =>
                onMenuClick(
                  ACTION_KEY.DYNAMIC_FORM,
                  { ...data, dynamicFormId: i.linkPathV2, dynamicFormName: i.optionName },
                  callback,
                ),
            })),
          ]
        : []),
    ],
    shouldFocusOnMount: false,
  });

  const DetailRow = ({ item }) => (
    <Table columns={columns.filter((i) => !i.active)} items={[item]} pagination={false} />
  );

  return (
    <Stack tokens={{ padding: 8 }}>
      <GroupTableDetail
        onRenderDetail={(item) => <DetailRow item={item} />}
        columns={columns.filter((i) => i.active)}
        initalItems={data || []}
        loading={loading}
        getMenuProps={(i) => getMenu(i)}
        showDetail={columns.some((i) => !i.active)}
      />
    </Stack>
  );
};

export default TriageHistory;

const styles = (theme) => ({
  wrapper: {
    '.ms-layer': {
      display: 'none',
    },
  },
  hasBottomBar: {
    marginBottom: theme.footerHeight,
  },
});
