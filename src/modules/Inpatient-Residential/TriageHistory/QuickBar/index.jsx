import React, { useEffect } from 'react';
import { Default<PERSON>utton, SearchBox, Stack } from '@fluentui/react';
import { MODAL_SIZE } from 'constants/modal';
import { useClass, useModal } from 'hooks';
import CustomFieldModal from 'modules/Batch/components/CustomFieldModal';
import { useDispatch, useSelector } from 'react-redux';
import { setInpatientColumns, updateFilterValue } from 'store/actions/inpatient';
import { t } from 'utils/string';
import { debounceFunc } from 'utils/time';
import { ScanCode } from '../../components/index';
import { API_INPATIENT } from 'constants/urlRequest';
import ProfileImage from 'components/Image/ProfileImage';
import CreateChair from 'modules/Inpatient-Residential/components/CreateChair';
import AdmissionTriage from 'modules/Inpatient-Residential/components/Admission/AdmissionTriage';

function TriageQuickBar() {
  const css = useClass(styles);
  const { showModal } = useModal();
  const dispatch = useDispatch();
  const { columns, filterValue } = useSelector((state) => state.inpatient);

  const onSearch = debounceFunc((searchValue) => {
    dispatch(updateFilterValue({ patientName: searchValue, patientId: '' }));
  }, 500);

  const setColumns = (cols) => dispatch(setInpatientColumns(cols));

  useEffect(() => {
    setColumns(getColumn(css));
  }, []);

  const showTableDisplayModal = () =>
    showModal({
      content: <CustomFieldModal fields={columns} setFields={setColumns} />,
      size: MODAL_SIZE.X_SMALL,
      allowOutsideClick: true,
    });

  const showAdmit = () =>
    showModal({
      content: <AdmissionTriage providerId={filterValue?.providerId} />,
      size: MODAL_SIZE.MEDIUM,
      allowOutsideClick: true,
    });

  const showAddRoom = () =>
    showModal({
      content: <CreateChair url={API_INPATIENT.GET_ROOM} siteUrl={API_INPATIENT.GET_SITE_LOOKUP} />,
      size: MODAL_SIZE.SMALL,
      allowOutsideClick: true,
    });

  const showPatientScan = () =>
    showModal({
      content: <ScanCode />,
      size: MODAL_SIZE.X_SMALL,
      allowOutsideClick: true,
    });

  return (
    <Stack
      horizontal
      tokens={{ childrenGap: 12, padding: 8 }}
      wrap
      horizontalAlign="end"
      verticalAlign="center"
      className={css.root}
    >
      <SearchBox
        key={filterValue?.patientId}
        defaultValue={
          (filterValue?.patientName || '') +
          (filterValue?.patientId ? ` - ${filterValue?.patientId}` : '')
        }
        placeholder={t('Search Patient Name')}
        className="flex-1 min-w-input"
        iconProps={{ iconName: 'Zoom' }}
        onChange={(_, newValue) => onSearch(newValue)}
      />
      <DefaultButton
        data-isbutton="button"
        onClick={showAddRoom}
        text={t('Create Chair')}
        iconProps={{ iconName: 'Add' }}
      />
      <DefaultButton
        data-isbutton="button"
        onClick={showAdmit}
        text={t('New Admit')}
        iconProps={{ iconName: 'AddFriend' }}
      />
      <DefaultButton
        data-isbutton="button"
        onClick={showPatientScan}
        text={t('Pending Prescription')}
        iconProps={{ iconName: 'Pill' }}
        disabled
      />
      <DefaultButton
        data-isbutton="button"
        onClick={showPatientScan}
        text={t('Pending Pharmacy')}
        iconProps={{ iconName: 'Medical' }}
        disabled
      />
      <DefaultButton
        text={t('Custom Field')}
        data-isbutton="button"
        onClick={showTableDisplayModal}
        iconProps={{ iconName: 'GridViewSmall' }}
      />
    </Stack>
  );
}

const styles = (theme) => ({
  root: {
    maxWidth: '100%',
    overflow: 'hidden',
  },
  patientGrid: {
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    gap: 16,
  },
});

const getColumn = (css) => [
  {
    name: 'Avatar',
    fieldName: 'avatar',
    key: 'avatar',
    minWidth: 50,
    active: true,
    renderItem: ({ patientAvatarUrl, patientName, patientId }) => {
      if (!patientName) return '-';
      return (
        <ProfileImage
          canPreview
          size={40}
          className="round img-full m-0"
          src={patientAvatarUrl}
          letterAvatar={patientName}
          patientId={patientId}
        />
      );
    },
  },
  {
    name: 'Patient Name',
    fieldName: 'patientName',
    key: 'patientName',
    minWidth: 150,
    active: true,
    renderItem: ({ patientName }) => <span className="cell_style">{patientName || '-'}</span>,
  },
  {
    name: 'LOS',
    fieldName: 'los',
    key: 'los',
    minWidth: 150,
    active: true,
    renderItem: ({ los }) => <strong className="cell_style">{los}</strong>,
  },
  {
    name: 'Chair #',
    fieldName: 'chairNo',
    key: 'chairNo',
    minWidth: 130,
    maxWidth: 130,
    active: true,
  },
  {
    name: 'Patient Info',
    fieldName: 'functionalStrengths',
    key: 'functionalStrengths',
    minWidth: 220,
    active: true,
    renderItem: ({ dob, age, gender }, _, c, renderDate) => {
      return (
        <div className={css.patientGrid}>
          <Stack>
            <span className={css.cmClass.colorSecondary}>{t('DOB')}</span>
            <span>
              {renderDate({ date: dob })} ({age})
            </span>
          </Stack>
          <Stack>
            <span className={css.cmClass.colorSecondary}>{t('Gender')}</span>
            <span>{gender}</span>
          </Stack>
        </div>
      );
    },
  },
  {
    name: 'Adm, Reason',
    fieldName: 'admitReasonDesc',
    key: 'admitReasonDesc',
    minWidth: 150,
    active: true,
  },
  {
    name: 'Admit Date',
    fieldName: 'admitDateTime',
    key: 'admitDateTime',
    minWidth: 200,
    active: true,
    renderItem: ({ admitDateTime }, _, c, render) =>
      render({ date: admitDateTime, withTime: true }),
  },
  {
    name: 'Discharged Date',
    fieldName: 'dischargeDateTime',
    key: 'dischargeDateTime',
    minWidth: 200,
    active: true,
    renderItem: ({ dischargeDateTime }, _, c, render) =>
      render({ date: dischargeDateTime, withTime: true }),
  },
  {
    name: 'Comments',
    fieldName: 'admitComments',
    key: 'admitComments',
    minWidth: 150,
    active: true,
  },
  {
    key: 'Action',
    name: 'Action',
    fieldName: 'action',
    active: true,
  },
];

export default TriageQuickBar;
