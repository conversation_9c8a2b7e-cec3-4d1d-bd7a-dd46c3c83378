import React from 'react';
import { Stack, PrimaryButton } from '@fluentui/react';
import PerfectScroll from 'utils/PerfectScroll';
import { ACTION_KEY } from 'components/TabsBottom/constants';
import { useClass, useRequest } from 'hooks';
import { t } from 'utils/string';
import My<PERSON>ield from 'components/Form/Field';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { API_METHOD, API_RESIDENTAL, API_TRIAGE_OBSERVATION } from 'constants/urlRequest';
import { momentCloneDateTime } from 'utils/time';
import { handleValidationBE } from 'utils/form';
import { TEXT_TEMPLATE } from 'constants/texts';
import QUERY_KEY from 'constants/queryKey';
import { toast } from 'react-toastify';
import { fetchByQueryKey } from 'apis/queryClient';
import FormUndo from 'components/FormUndo';
import { FORM_ID } from 'constants/formId';
import ButtonWrapper from 'components/FormUndo/ButtonWrapper';
import { AdmissionTriageInfo } from '../../Admission/AdmissionTriage';

function AdmitInfoTriage({ data }) {
  const css = useClass(styles);
  const apiGetAdmitInfo = useRequest({
    url: API_RESIDENTAL.ADMISSION_GET_ADMISSION_DETAIL(data?.admitId),
    enabled: !!data?.admitId,
  });

  const admitCreate = useRequest({
    key: API_TRIAGE_OBSERVATION.ADMISSION + 'Update_Admit_Info',
    url: API_TRIAGE_OBSERVATION.ADMISSION,
    method: API_METHOD.POST,
  });

  const handleSubmit = (values, form) => {
    const submitData = { ...values };
    if (values?.admitDate)
      submitData.admitDate = momentCloneDateTime(values?.admitDate, values?.admitTime);
    admitCreate.request({
      payload: submitData,
      options: {
        onError: (err) => handleValidationBE(form, err),
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('Admission')));
          fetchByQueryKey(QUERY_KEY.RESIDENTIAL_HISTORY_LIST);
        },
      },
    });
  };

  return (
    <PerfectScroll id={ACTION_KEY.ADMIT_INFO_TRIAGE} className={css.root}>
      <FormUndo
        onSubmit={handleSubmit}
        initialValues={data || {}}
        enableReinitialize
        formId={FORM_ID.RESIDENTIAL_ADMIT_INFO}
      >
        <LoadingWrapper loading={apiGetAdmitInfo?.loading}>
          <Stack tokens={{ childrenGap: 16, padding: 16 }}>
            <AdmissionTriageInfo
              providerId={apiGetAdmitInfo?.data?.providerId}
              admitId={data?.admitId}
            />
            <ButtonWrapper alignEnd={false}>
              <LoadingWrapper loading={admitCreate?.loading}>
                <MyField isFast={false}>
                  {({ form }) => (
                    <PrimaryButton
                      text={t('Save')}
                      data-isbutton="button"
                      onClick={form.handleSubmit}
                    />
                  )}
                </MyField>
              </LoadingWrapper>
            </ButtonWrapper>
          </Stack>
        </LoadingWrapper>
      </FormUndo>
    </PerfectScroll>
  );
}

const styles = () => ({
  root: {
    height: 'calc(100vh - 14rem)',
  },
});

export default AdmitInfoTriage;
