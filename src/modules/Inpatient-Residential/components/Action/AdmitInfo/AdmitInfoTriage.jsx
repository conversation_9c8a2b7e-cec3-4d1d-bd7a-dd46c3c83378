import React from 'react';
import { Stack, PrimaryButton } from '@fluentui/react';
import PerfectScroll from 'utils/PerfectScroll';
import { ACTION_KEY } from 'components/TabsBottom/constants';
import { useClass, useRequest } from 'hooks';
import { t } from 'utils/string';
import MyField from 'components/Form/Field';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { API_METHOD, API_TRIAGE_OBSERVATION } from 'constants/urlRequest';
import { handleValidationBE } from 'utils/form';
import { TEXT_TEMPLATE } from 'constants/texts';
import QUERY_KEY from 'constants/queryKey';
import { toast } from 'react-toastify';
import { fetchByQueryKey } from 'apis/queryClient';
import FormUndo from 'components/FormUndo';
import { FORM_ID } from 'constants/formId';
import ButtonWrapper from 'components/FormUndo/ButtonWrapper';
import { AdmissionTriageInfo } from '../../Admission/AdmissionTriage';
import moment from 'moment';

function AdmitInfoTriage({ data }) {
  const css = useClass(styles);

  const admitCreate = useRequest({
    key: API_TRIAGE_OBSERVATION.ADMISSION + 'Update_Admit_Info',
    url: API_TRIAGE_OBSERVATION.ADMISSION,
    method: API_METHOD.POST,
  });

  const handleSubmit = (values, form) => {
    const submitData = { ...values };
    submitData.admitDate = moment(values?.admitDate).utc().format('YYYY-MM-DD');
    submitData.admitTime = moment(values?.admitTime).utc().format('HH:mm');
    admitCreate.request({
      payload: submitData,
      options: {
        onError: (err) => handleValidationBE(form, err),
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('Admission')));
          fetchByQueryKey(QUERY_KEY.RESIDENTIAL_HISTORY_LIST);
        },
      },
    });
  };

  return (
    <PerfectScroll id={ACTION_KEY.ADMIT_INFO_TRIAGE} className={css.root}>
      <FormUndo
        onSubmit={handleSubmit}
        initialValues={
          data ? { ...data, admitDate: data.admitDateTime, admitTime: data.admitDateTime } : {}
        }
        enableReinitialize
        formId={FORM_ID.RESIDENTIAL_ADMIT_INFO}
      >
        <LoadingWrapper loading={admitCreate?.loading}>
          <Stack tokens={{ childrenGap: 16, padding: 16 }}>
            <AdmissionTriageInfo providerId={data?.providerId} admitId={data?.admitId} />
            <ButtonWrapper alignEnd={false}>
              <MyField isFast={false}>
                {({ form }) => (
                  <PrimaryButton
                    text={t('Save')}
                    data-isbutton="button"
                    onClick={form.handleSubmit}
                  />
                )}
              </MyField>
            </ButtonWrapper>
          </Stack>
        </LoadingWrapper>
      </FormUndo>
    </PerfectScroll>
  );
}

const styles = () => ({
  root: {
    height: 'calc(100vh - 14rem)',
  },
});

export default AdmitInfoTriage;
