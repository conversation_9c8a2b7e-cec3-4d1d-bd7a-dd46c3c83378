import { De<PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryButton, Stack } from '@fluentui/react';
import classNames from 'classnames';
import Field<PERSON><PERSON>ckBox from 'components/Form/FieldCheckbox';
import FieldDate from 'components/Form/FieldDate';
import FieldText from 'components/Form/FieldText';
import FieldTime from 'components/Form/FieldTime';
import { useClass, useModal, useRequest } from 'hooks';
import React, { useState } from 'react';
import PerfectScroll from 'utils/PerfectScroll';
import { t } from 'utils/string';
import { API_INPATIENT, API_METHOD, API_TRIAGE_OBSERVATION } from 'constants/urlRequest';
import FieldSelectorPrimary from 'components/Form/FieldSelectorPrimary';
import { formatToFluentOptions } from 'utils';
import TableSearchDiagnosis from './TableSearchDiagnosis';
import MyField from 'components/Form/Field';
import { handleValidationBE } from 'utils/form';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { toast } from 'react-toastify';
import { TEXT_TEMPLATE } from 'constants/texts';
import { fetchByQueryKey } from 'apis/queryClient';
import QUERY_KEY from 'constants/queryKey';
import TableSearchPatient from './TableSearchPatient';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import FormUndo from 'components/FormUndo';
import { FORM_ID } from 'constants/formId';
import FormTemplate from 'components/FormUndo/FormTemplate';
import ButtonWrapper from 'components/FormUndo/ButtonWrapper';
import { scrollIntoViewId } from 'utils/globalScroll';
import moment from 'moment';

export const AdmissionTriageInfo = ({ providerId, label }) => {
  const css = useClass(styles);

  const apiTriageLookup = useRequest({
    url: API_TRIAGE_OBSERVATION.ADMISSION_LOOKUP,
    shouldCache: true,
  });

  const apiSiteLookup = useRequest({
    key: QUERY_KEY.INPATIENT_GET_SITE_LOOKUP,
    url: API_INPATIENT.GET_SITE_LOOKUP,
    shouldCache: true,
  });

  const apiChair = useRequest({
    url: API_TRIAGE_OBSERVATION.CHAIR,
    autoRefetch: true,
    params: {
      pageSize: 1000,
      orderBy: 'createDateTime',
      providerId,
      activeOnly: true,
    },
  });

  const sectionStyle = classNames(
    css.cmClass.backgroundGrey100,
    css.cmClass.border,
    css.cmClass.borderColor,
    css.radius,
  );

  return (
    <Stack tokens={{ childrenGap: 16, padding: 16 }} className={sectionStyle} id="admit-info">
      <span className="text-16-20 weight-600">{label}</span>
      <div className={css.grid}>
        <FieldDate required name="admitDate" title={t('Admit Date')} />
        <FieldTime required name="admitTime" title={t('Admit Time')} />
        <FieldSelectorPrimary
          name="providerId"
          disabled
          title={t('Site')}
          isFast={false}
          options={formatToFluentOptions(apiSiteLookup?.data)}
        />
      </div>

      <div className={css.grid}>
        <LoadingWrapper loading={apiChair?.loading}>
          <FieldSelectorPrimary
            name="chairId"
            title={t('Chair')}
            isFast={false}
            required
            options={formatToFluentOptions(apiChair?.data?.items || [], 'chairNo', 'id')}
          />
        </LoadingWrapper>

        <FieldSelectorPrimary
          name="admitReason"
          title={t('Admit Reason 1')}
          isFast={false}
          required
          options={formatToFluentOptions(apiTriageLookup?.data?.admitReasons || [])}
        />

        <FieldSelectorPrimary
          name="admitReason2"
          title={t('Admit Reason 2')}
          isFast={false}
          options={formatToFluentOptions(apiTriageLookup?.data?.admitReasons || [])}
        />
      </div>

      <Stack className={css.grid} horizontal>
        <FieldSelectorPrimary
          name="empId"
          title={t('Attending')}
          isFast={false}
          required
          options={formatToFluentOptions(apiTriageLookup?.data?.attending || [])}
        />

        <FieldSelectorPrimary
          name="payorId"
          title={t('Payor')}
          isFast={false}
          required
          options={formatToFluentOptions(apiTriageLookup?.data?.payorList || [])}
        />
        <FieldCheckBox label="Crisis" fieldClassName="self-end" name="crisis" />
      </Stack>

      <div className={css.grid}>
        <TableSearchDiagnosis
          fieldSearchProps={{ required: true }}
          name="admitDxCode"
          title={t('Admitting Diagnosis')}
        />
        <TableSearchDiagnosis name="secondaryCode" title={t('Secondary Diagnosis')} />
        <TableSearchDiagnosis name="tertiaryCode" title={t('Tertiary Diagnosis')} />
      </div>

      <div className={css.grid}>
        <FieldSelectorPrimary
          name="refSource"
          title={t('Source of Referral')}
          isFast={false}
          required
          options={formatToFluentOptions(apiTriageLookup?.data?.sourceOfReferral || [])}
        />
      </div>
      <FieldText title={t('Comment')} name="admitComments" fieldClassName="flex-1" />
    </Stack>
  );
};

function AdmissionTriage({ providerId }) {
  const css = useClass(styles);
  const { hideModal } = useModal();
  const [patientChoose, setPatientChoose] = useState({});

  const admitCreate = useRequest({
    key: API_TRIAGE_OBSERVATION.ADMISSION + 'Create',
    url: API_TRIAGE_OBSERVATION.ADMISSION,
    method: API_METHOD.POST,
  });

  const handleSubmit = (values, form) => {
    const submitData = { ...values };
    if (patientChoose?.patientId) submitData.patientId = patientChoose?.patientId;
    submitData.admitDate = moment(values?.admitDate).utc().format('YYYY-MM-DD');
    submitData.admitTime = moment(values?.admitTime).utc().format('HH:mm');
    admitCreate.request({
      payload: submitData,
      options: {
        onError: (err) => handleValidationBE(form, err),
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.ADD_SUCCESSFULLY(t('Admission')));
          fetchByQueryKey(QUERY_KEY.OBSERVATION_MODULE_LIST);
          hideModal();
        },
      },
    });
  };

  return (
    <FormUndo
      initialValues={{ admitDate: new Date(), providerId }}
      onSubmit={handleSubmit}
      formId={FORM_ID.INPATIENT_ADMISSION}
      autoShowFormTemplate={false}
    >
      <ModalLayout
        title={t('Patient Admission')}
        loading={admitCreate?.loading}
        footerRightSide={
          <ButtonWrapper>
            <MyField isFast={false}>
              {({ form }) => (
                <PrimaryButton
                  text={t('Admit Patient')}
                  data-isbutton="button"
                  onClick={form.submitForm}
                />
              )}
            </MyField>
            <DefaultButton text={t('Cancel')} data-isbutton="button" onClick={hideModal} />
          </ButtonWrapper>
        }
      >
        <FormTemplate />
        <React.Fragment>
          <PerfectScroll className={classNames('h-100', css.content)}>
            <Stack tokens={{ padding: 16, childrenGap: 16 }}>
              <TableSearchPatient
                patientChoose={patientChoose}
                onChoosePatient={(patient) => {
                  setPatientChoose(patient);
                  scrollIntoViewId('admit-info', 'start');
                }}
              />
              <AdmissionTriageInfo
                label={
                  t('Admit Summary ') +
                  `${patientChoose?.fullName ? ' - ' + patientChoose?.fullName : ''}`
                }
                providerId={providerId}
              />
            </Stack>
          </PerfectScroll>
        </React.Fragment>
      </ModalLayout>
    </FormUndo>
  );
}

const styles = () => ({
  content: {
    flex: 1,
    overflow: 'auto',
    '.ms-DetailsRow-fields': {
      cursor: 'pointer',
    },
  },
  radius: {
    borderRadius: 4,
  },
  searchHeight: {
    minHeight: 32,
  },
  grid: {
    display: 'grid',
    gridTemplateColumns: '1fr 1fr 1fr',
    gap: 16,
  },
  noBackground: {
    backgroundColor: '#FFFFFF',
  },
});

export default AdmissionTriage;
