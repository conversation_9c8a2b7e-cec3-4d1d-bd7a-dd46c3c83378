import Table from 'components/Table';
import { DetailsRow, Stack } from '@fluentui/react';
import <PERSON><PERSON><PERSON> from 'components/Form/Field';
import { API_PROBLEM } from 'constants/urlRequest';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import { useText, useRequest, useNavigator } from 'hooks';
import React, { useCallback, useState } from 'react';
import { debounceFunc } from 'utils/time';
import FieldSearch from 'components/Form/FieldSearch';

const TableSearchDiagnosis = ({ name, title, fieldSearchProps = {} }) => {
  const [itemChoose, setItemChoose] = useState({});
  const [showTable, setShowTable] = useState(false);
  const { searchParams } = useNavigator();
  const t = useText();
  const apiSearch = useRequest({
    key: API_PROBLEM.DIAGNOSTIC_CODE + title,
    url: API_PROBLEM.DIAGNOSTIC_CODE,
    params: DEFAULT_PAGINATION_PARAMS,
    requiredParams: {
      patientId: searchParams.patientId,
    },
    autoRefetch: true,
    enabled: false,
    cacheTime: 0,
  });
  const onSearch = useCallback(
    debounceFunc((SearchTerm) => {
      apiSearch?.updateParams?.({ SearchTerm, Page: 0 });
      setShowTable(true);
    }, 500),
    [],
  );
  const onRenderRow = (props, form) => (
    <DetailsRow
      {...props}
      data-isbutton="button"
      onClick={() => {
        setItemChoose(props?.item || {});
        setShowTable(false);
        form.setFieldValue(name, props?.item?.diagnosticCodeId);
      }}
    />
  );

  return (
    <Stack tokens={{ childrenGap: 4 }}>
      <FieldSearch
        name={name}
        placeholder={t('Search')}
        iconProps={{ iconName: 'Zoom' }}
        isFast={false}
        onChange={(_, newValue) => {
          console.log(newValue);
          onSearch(newValue);
        }}
        title={title}
        {...fieldSearchProps}
      />
      <MyField name={name} isFast={false}>
        {({ form }) => (
          <React.Fragment>
            <strong>{itemChoose?.diagnosticDesc}</strong>
            {showTable && (
              <Table
                loading={apiSearch?.loading}
                items={apiSearch?.data?.items || []}
                columns={_column}
                onRenderRow={(props) => onRenderRow(props, form)}
                totalItems={apiSearch?.data?.totalItems}
                metadata={apiSearch.params}
                onMetadataChange={apiSearch?.updateParams}
              />
            )}
          </React.Fragment>
        )}
      </MyField>
    </Stack>
  );
};

const _column = [
  {
    name: 'Problem Description',
    fieldName: 'diagnosticDesc',
    minWidth: 150,
    maxWidth: 150,
  },
  {
    name: 'Problem Code',
    fieldName: 'diagnosisCode',
  },
];

export default TableSearchDiagnosis;
