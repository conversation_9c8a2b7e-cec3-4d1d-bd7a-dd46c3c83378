import { <PERSON><PERSON><PERSON>on, Stack, DetailsRow } from '@fluentui/react';
import classNames from 'classnames';
import Table from 'components/Table';
import { SETTING_KEYS } from 'constants/settingKeys';
import { useClass, useSetting, useRequest, useNavigator } from 'hooks';
import moment from 'moment';
import React, { useCallback, useState } from 'react';
import { t } from 'utils/string';
import { API_PATIENT } from 'constants/urlRequest';
import FieldSearch from 'components/Form/FieldSearch';

function TableSearchPatient({ onChoosePatient, patientChoose }) {
  const css = useClass();
  const [DATE_FORMAT] = useSetting(SETTING_KEYS.DATE_FORMAT);

  const [searchValue, setSearchValue] = useState('');

  const { searchParams } = useNavigator();

  const findPatient = useRequest({
    url: API_PATIENT.DEFAULT,
    params: { Page: 0, PageSize: 10 },
    requiredParams: { patientId: searchParams.patientId },
    autoRefetch: true,
    enabled: false,
    cacheTime: 0,
  });

  const handleSearch = useCallback(
    () => findPatient.updateParams({ SearchTerm: searchValue, Page: 0 }),
    [searchValue],
  );

  const sectionStyle = classNames(
    css.cmClass.backgroundGrey100,
    css.cmClass.border,
    css.cmClass.borderColor,
    css.cmClass.borderRadius4,
  );

  const onRenderRow = (props) => {
    return (
      <DetailsRow
        {...props}
        className={classNames(
          props?.className,
          props?.item?.patientId === patientChoose?.patientId && css.table.rowBackgroundHighlight,
        )}
        data-isbutton="button"
        onClick={() => {
          onChoosePatient(props?.item);
        }}
      />
    );
  };

  return (
    <Stack tokens={{ childrenGap: 16, padding: 16 }} className={sectionStyle}>
      <Stack horizontal tokens={{ childrenGap: 16 }}>
        <FieldSearch
          name="patientId"
          placeholder={t('Search')}
          className="flex-1"
          iconProps={{ iconName: 'Zoom' }}
          onChange={(_, newValue) => setSearchValue(newValue)}
        />
        <PrimaryButton data-isbutton="button" onClick={handleSearch} text={t('Search')} />
      </Stack>
      <Table
        loading={findPatient.loading}
        items={findPatient?.data?.items?.map((item) => ({ ...item, className: 'oktest' })) || []}
        columns={getColumn(DATE_FORMAT)}
        pagination={false}
        onRenderRow={onRenderRow}
      />
    </Stack>
  );
}

const getColumn = (dateFormat) => [
  {
    name: 'Name',
    fieldName: 'fullName',
    minWidth: 150,
    sortable: true,
  },
  {
    name: 'DOB',
    fieldName: 'dob',
    sortable: true,
    renderItem: ({ dob }) => (dob ? moment(dob).format(dateFormat) : ''),
  },
  {
    name: 'Age',
    fieldName: 'age',
    minWidth: 100,
    maxWidth: 100,
    sortable: true,
  },
  {
    name: 'Population',
    fieldName: 'population',
    minWidth: 100,
    maxWidth: 100,
    sortable: true,
  },
  {
    name: 'Provider',
    fieldName: 'provider',
    minWidth: 100,
    maxWidth: 100,
    sortable: true,
  },
  {
    name: 'Social Security Number',
    fieldName: 'ssn',
    minWidth: 100,
    maxWidth: 100,
    sortable: true,
  },
  {
    name: 'Enrollment Date  ',
    fieldName: 'enrollmentDate',
    minWidth: 100,
    maxWidth: 100,
    renderItem: ({ enrollmentDate }) =>
      enrollmentDate ? moment(enrollmentDate).format(dateFormat) : '',
    sortable: true,
  },
];

export default TableSearchPatient;
