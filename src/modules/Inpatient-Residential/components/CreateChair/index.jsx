import { Defa<PERSON><PERSON><PERSON><PERSON>, PrimaryButton, SearchBox, Stack } from '@fluentui/react';
import classNames from 'classnames';
import <PERSON><PERSON>ield from 'components/Form/Field';
import FieldText from 'components/Form/FieldText';
import FormUndo from 'components/FormUndo';
import ButtonWrapper from 'components/FormUndo/ButtonWrapper';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import Table from 'components/Table';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import { TEXT_TEMPLATE } from 'constants/texts';
import { API_METHOD, API_TRIAGE_OBSERVATION } from 'constants/urlRequest';
import { useClass, useModal, useRequest } from 'hooks';
import React, { useCallback } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { handleValidationBE } from 'utils/form';
import { t } from 'utils/string';
import { debounceFunc } from 'utils/time';

function Create<PERSON>hair() {
  const css = useClass(styles);
  const { hideModal } = useModal();
  const { filterValue } = useSelector((state) => state.inpatient);

  const { data, updateParams, loading, params, refetch } = useRequest({
    url: API_TRIAGE_OBSERVATION.CHAIR,
    autoRefetch: true,
    params: {
      ...DEFAULT_PAGINATION_PARAMS,
      orderBy: 'createDateTime',
      providerId: filterValue.providerId,
    },
    saveHistory: false,
    cacheTime: 0,
  });

  const apiCreate = useRequest({ url: API_TRIAGE_OBSERVATION.CHAIR, method: API_METHOD.POST });

  const onSearch = useCallback(
    debounceFunc((searchTerm) => {
      updateParams({ searchTerm });
    }, 500),
    [],
  );

  const onsubmit = (form) => {
    apiCreate.request({
      payload: form.values,
      options: {
        onError: (error) => {
          handleValidationBE(form, error);
        },
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.ADD_SUCCESSFULLY(t('Room')));
          refetch();
          form.setValues({ providerId: filterValue.providerId });
        },
      },
    });
  };

  return (
    <FormUndo enableReinitialize initialValues={{ providerId: filterValue.providerId }}>
      <ModalLayout
        title={t('Create Chair')}
        loading={apiCreate?.loading}
        footerRightSide={
          <ButtonWrapper>
            <MyField>
              {({ form }) => (
                <PrimaryButton
                  text={t('Create Chair')}
                  data-isbutton="button"
                  onClick={() => onsubmit(form)}
                />
              )}
            </MyField>
            <DefaultButton text={t('Cancel')} data-isbutton="button" onClick={hideModal} />
          </ButtonWrapper>
        }
      >
        <Stack tokens={{ padding: 16, childrenGap: 16 }}>
          <Stack
            tokens={{ childrenGap: 16, padding: 16 }}
            className={classNames(
              css.cmClass.backgroundGrey100,
              css.cmClass.border,
              css.cmClass.borderColor,
              css.radius,
            )}
          >
            <SearchBox
              placeholder={t('Search')}
              className={classNames('flex-1', css.searchHeight)}
              iconProps={{ iconName: 'Zoom' }}
              onChange={(_, newValue) => onSearch(newValue)}
            />
            <Table
              columns={_columns}
              items={data?.items || []}
              loading={loading}
              totalItems={data?.totalItems}
              metadata={params}
              onMetadataChange={updateParams}
              refetch={refetch}
            />
          </Stack>
          <FieldText title={t('Chair Name')} name="chairNo" fieldClassName="flex-1" required />
        </Stack>
      </ModalLayout>
    </FormUndo>
  );
}

const _columns = [
  {
    name: 'Existing Chairs',
    fieldName: 'chairNo',
    sortable: true,
  },
  {
    name: 'Created Date',
    fieldName: 'createDateTime',
    sortable: true,
    renderItem: ({ createDateTime, createUser }, _, c, render) =>
      render({ date: createDateTime, user: createUser }),
  },
];

const styles = () => ({
  wrapper: {
    minWidth: 600,
  },
  radius: {
    borderRadius: 4,
  },
  searchHeight: {
    minHeight: 32,
  },
});

export default CreateChair;
