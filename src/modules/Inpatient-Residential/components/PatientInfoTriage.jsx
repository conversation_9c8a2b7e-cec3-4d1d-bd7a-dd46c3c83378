import React from 'react';
import { HoverCard, HoverCardType, Stack } from '@fluentui/react';
import cn from 'classnames';
import { SETTING_KEYS } from 'constants/settingKeys';
import { useClass, useSetting } from 'hooks';
import moment from 'moment';
import { t } from 'utils/string';
import ProfileImage from 'components/Image/ProfileImage';

function PatientInfo({ tabData }) {
  const css = useClass(styles);
  const [DATE_FORMAT, TIME_FORMAT] = useSetting(SETTING_KEYS.DATE_FORMAT, SETTING_KEYS.TIME_FORMAT);

  return (
    <Stack className={cn(css.wrapper, css.cmClass.backgroundGrey100)} horizontal>
      <ProfileImage
        canPreview
        size={40}
        className="round img-full m-0"
        src={tabData.patientAvatarUrl}
        letterAvatar={tabData.patientName}
        patientId={tabData?.patientId}
      />
      <Stack>
        <span>{t('Chair #')}</span>
        <span>{tabData.chairNo}</span>
      </Stack>
      <Stack>
        <span>{t('Patient Name')}</span>
        <span>{tabData.name}</span>
      </Stack>
      <Stack>
        <span>{t('DOB')}</span>
        <span>{moment(tabData?.dob).format(DATE_FORMAT)}</span>
      </Stack>
      <Stack>
        <span>{t('Age')}</span>
        <span>{tabData?.age}</span>
      </Stack>
      <Stack>
        <span>{t('Gender')}</span>
        <span>{tabData.gender === 'M' ? 'Male' : 'Female'}</span>
      </Stack>
      <Stack>
        <span>{t('Adm, Reason')}</span>
        <span>{tabData.admitValue || '-'}</span>
      </Stack>
      <Stack>
        <span>{t('Admit Date')}</span>
        <span>
          {moment(tabData.admitDate).format(DATE_FORMAT)} -{' '}
          {moment(tabData.admitTime).format(TIME_FORMAT)}
        </span>
      </Stack>
      <Stack>
        <span>{t('Discharged Date')}</span>
        <span>
          {tabData.dischargeDate ? moment(tabData.dischargeDate).format(DATE_FORMAT) : 'N/A'} -{' '}
          {tabData.dischargeDate ? moment(tabData.dischargeDate).format(TIME_FORMAT) : 'N/A'}
        </span>
      </Stack>
      <Stack className="flex-1">
        <span>{t('Comments')}</span>
        <HoverCard
          cardDismissDelay={500}
          type={HoverCardType.plain}
          plainCardProps={{
            onRenderPlainCard: () => <Stack tokens={{ padding: 8 }}>{tabData.comments}</Stack>,
          }}
        >
          <div className="text-truncate weight-600">{tabData.comments || '-'}</div>
        </HoverCard>
      </Stack>
    </Stack>
  );
}

const styles = () => ({
  wrapper: {
    height: 64,
    padding: 12,
    gap: 32,
    alignItems: 'center',
    '.ms-Persona-details': {
      display: 'none',
    },
    '.ms-Stack': {
      flexShrink: 0,
      overflow: 'hidden',
      span: {
        fontSize: 14,
      },
      'span:last-child': {
        fontWeight: 600,
      },
    },
  },
});

export default React.memo(PatientInfo);
