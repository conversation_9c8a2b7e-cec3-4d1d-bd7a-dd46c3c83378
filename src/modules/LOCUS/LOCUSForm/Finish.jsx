import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryButton, Stack, TextField } from '@fluentui/react';
import FieldSelectorPrimary from 'components/Form/FieldSelectorPrimary';
import { t } from 'utils/string';
import { formatToFluentOptions } from 'utils';
import My<PERSON>ield from 'components/Form/Field';
import moment from 'moment';
import { useNavigator, useRequest, useSetting } from 'hooks';
import { SETTING_KEYS } from 'constants/settingKeys';
import { API_LOCUS, API_METHOD } from 'constants/urlRequest';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { handleValidationBE } from 'utils/form';
import { toast } from 'react-toastify';
import get from 'lodash/get';
import { MODULE_NAME } from 'constants/routes';
import { fetchByQueryKey } from 'apis/queryClient';
import QUERY_KEY from 'constants/queryKey';
import { TEXT_TEMPLATE } from '../../../constants/texts';

function Finish({ back, backToStart }) {
  const { searchParams, navigate } = useNavigator();
  const [dateFormat, timeFormat] = useSetting(SETTING_KEYS.DATE_FORMAT, SETTING_KEYS.TIME_FORMAT);

  const { data: locusLookup = {} } = useRequest({
    url: API_LOCUS.LOOKUP,
    shouldCache: true,
  });

  const apiSave = useRequest({ url: API_LOCUS.DEFAULT, method: API_METHOD.POST });

  const onSubmit = (form) => {
    let answers = [];
    for (const section of get(form, 'values.evaluating', []).filter(Boolean)) {
      for (const ques of section.questions.filter(Boolean)) {
        for (const ans of ques.answer.filter(Boolean)) {
          answers.push(ans);
        }
      }
    }

    const payload = {
      ...form.values,
      evaluationDate: new Date(),
      patientId: searchParams.patientId,
      formDate: new Date(),
      answers,
      prevDispositionLevel: form.values.loc,
    };

    apiSave.mutateAsync(payload, {
      onSuccess: () => {
        toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY('LOCUS'));
        fetchByQueryKey(QUERY_KEY.LOCUS_HISTORY);
        navigate({ hash: MODULE_NAME.LOCUS_HISTORY });
        form.setValues({});
        backToStart();
      },
      onError: (error) => {
        const custom = (key) => key;
        handleValidationBE(form, error, {}, custom);
      },
    });
  };

  return (
    <LoadingWrapper loading={apiSave.loading}>
      <Stack tokens={{ childrenGap: 12 }}>
        <span className="weight-600">{t('You have successfully completed a LOCUS©.')}</span>
        <TextField
          value={moment().format(`${dateFormat} ${timeFormat}`)}
          readOnly
          label={t('Date/Time of Evaluation')}
        />
        <FieldSelectorPrimary
          name="loc"
          isFast={false}
          title={t('Current Level of Care')}
          disabled
          placeholder=""
          options={formatToFluentOptions(locusLookup.locusLevelOfCare)}
        />
        <FieldSelectorPrimary
          name="recommendedDispositionLevel"
          isFast={false}
          title={t('LOCUS Recommended Disposition')}
          disabled
          placeholder=""
          options={formatToFluentOptions(locusLookup.locusLevelOfCare)}
        />
        <FieldSelectorPrimary
          name="actualDispositionLevel"
          isFast={false}
          title={t('Actual Disposition')}
          options={formatToFluentOptions(locusLookup.locusLevelOfCare)?.filter(
            (i) => i.text !== 'None',
          )}
        />
        <MyField isFast={false}>
          {({ form: { values } }) => (
            <FieldSelectorPrimary
              name="reasonVariance"
              isFast={false}
              required={+values.actualDispositionLevel !== +values.recommendedDispositionLevel}
              title={t('Reason For Variance')}
              options={formatToFluentOptions(locusLookup.locusReasonForVariance)}
            />
          )}
        </MyField>
        <Stack horizontal tokens={{ childrenGap: 16 }}>
          <MyField isFast={false}>
            {({ form }) => (
              <PrimaryButton
                text={t('Finish')}
                data-isbutton="button"
                onClick={() => onSubmit(form)}
              />
            )}
          </MyField>
          <DefaultButton text={t('Previous')} data-isbutton="button" onClick={back} />
          <MyField>
            {({ form: { setValues } }) => (
              <DefaultButton
                text={t('Cancel Evaluation')}
                data-isbutton="button"
                onClick={() => {
                  setValues({});
                  backToStart();
                }}
              />
            )}
          </MyField>
        </Stack>
      </Stack>
    </LoadingWrapper>
  );
}

export default Finish;
