import React, { useContext, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import Table from 'components/Table';
import Tag from 'components/Tag';
import { COMPLETION_STATUS, COMPLETION_STATUS_KEY } from 'constants/statusRender';
import { PROGRESS_NOTE_FORM_NAME, PROGRESS_NOTE_FORM_TYPE } from 'constants/progressNote';
import {
  API_DYNAMIC_PROGRESS_NOTE,
  API_METHOD,
  API_PROGRESS_NOTE,
  API_SECURITY_FORMS,
} from 'constants/urlRequest';
import { useClass, useModal, useNavigator, useRequest } from 'hooks';
import { useSelector } from 'react-redux';
import ModalAmend from './ModalAmend';
import ModalViewNote from './ModalViewNote';
import { t } from 'utils/string';
import { CheckboxVisibility, DetailsRow, Selection } from '@fluentui/react';
import ModalConfirm from 'components/Modal/ModalConfirm';
import { TEXT_TEMPLATE } from 'constants/texts';
import { toast } from 'react-toastify';
import { MODAL_SIZE } from 'constants/modal';
import { downloadContentAPI, onPrint } from 'utils/file';
import { GroupNoteContext } from 'contexts/GroupNoteContext';
import moment from 'moment';
import ModalSummary from './ModalSummary';
import { MODULE_NAME, ROUTE_NAME } from 'constants/routes';
import { PsychotherapyNoteContext } from 'contexts/PsychotherapyNoteContext';
import { PsyEvalContext } from 'contexts/PsyEvalContext';
import { PsychosocialAssessmentNoteContext } from 'contexts/PsychosocialAssessmentNoteContext';
import { PsychProgressNoteContext } from 'contexts/PsychProgressNoteContext';
import ModalViewAnswer from 'modules/ProgressNote/DynamicProgressNote/Viewer/Form/Preview/ModalViewAnswer';
import { SEARCH_PARAMS } from 'constants/index';
import { EngagementSessionNoteContext } from 'contexts/EngagementSessionNoteContext';
import { CMNoteContext } from 'contexts/CMNoteContext';
import { NurseProgressNoteContext } from 'contexts/NurseProgressNoteContext';
import { InpatientNoteContext } from 'contexts/InpatientNoteContext';
import { scrollIntoViewId } from 'utils/globalScroll';
import ELEMENT_ID from 'constants/elementId';
import { PCPProgressNoteContext } from 'contexts/PCPProgressNoteContext';

const HistoryTable = forwardRef(
  ({ apiHistory, sectionList, onAfterEditItem, setSelectedItems }, ref) => {
    const css = useClass();
    const pageSecurity = useSelector((state) => state.settings.pageSecurity);

    const { data, updateParams, isFetching, params, refetch } = apiHistory || {};
    const { searchParams, navigate, pathname } = useNavigator();
    const { showModal, hideModal } = useModal();
    const { current: selection } = useRef(
      new Selection({
        onSelectionChanged: () => {
          setSelectedItems(selection.getSelection());
        },
      }),
    );
    const { ehrUserId, roles, username } = useSelector((state) => state.authentication.info);
    const {
      setEditId: setEditIdCMNote,
      setAmendId: setAmendIdCMNote,
      setAutoSaveId: setAutoSaveIdCMNote,
    } = useContext(CMNoteContext);
    const {
      setAutoSaveId: setAutoSaveIdGN,
      setAutoSaveGroupId: setAutoSaveGroupIdGN,
      setEncounterBatchId: setEncounterBatchIdGN,
      setEncounterId: setEncounterIdGN,
      setCurrentSection: setCurrentSectionGN,
    } = useContext(GroupNoteContext);

    const { setEditId: setEditPsychotherapy, setAmendId: setAmendIdPsychotherapy } =
      useContext(PsychotherapyNoteContext);

    const {
      setEditId: setEditPsychosocialAssessment,
      setAmendId: setAmendIdPsychosocialAssessment,
    } = useContext(PsychosocialAssessmentNoteContext);

    const {
      setEditId: setEditIdPsyEval,
      setAmendId: setAmendIdPsyEval,
      setAutoSaveId: setAutoSaveIdPsyEval,
    } = useContext(PsyEvalContext);

    const {
      setEditId: setEditIdPsychNote,
      setAmendId: setAmendIdPsychNote,
      setAutoSaveId: setAutoSaveIdPsychNote,
    } = useContext(PsychProgressNoteContext);

    const {
      setEditId: setEditIdPCPNote,
      setAmendId: setAmendIdPCPNote,
      setAutoSaveId: setAutoSaveIdPCPNote,
    } = useContext(PCPProgressNoteContext);

    const {
      setEditId: setEditIdEngagementSessionNote,
      setAmendId: setAmendIdEngagementSessionNote,
      setAutoSaveId: setAutoSaveIdEngagementSessionNote,
    } = useContext(EngagementSessionNoteContext);

    const {
      setEditId: setEditIdInpatientNote,
      setAmendId: setAmendIdInpatientNote,
      setAutoSaveId: setAutoSaveIdInpatientNote,
    } = useContext(InpatientNoteContext);

    const { setEditId: setEditIdNurseProgressNote, setAmendId: setAmendIdNurseProgressNote } =
      useContext(NurseProgressNoteContext);

    const { data: dynamicNotes } = useRequest({
      key: `${API_SECURITY_FORMS.BY_USER}-${username}`,
      url: API_SECURITY_FORMS.BY_USER,
      requiredParams: { userName: username },
    });

    const onView = (item, isPrintAmendDetails) => {
      item?.dynamicProgressNotePath
        ? showModal({
            content: (
              <ModalViewAnswer
                encounterId={item.encounterId}
                formId={item.encounterFormId}
                formName={item.formName}
              />
            ),
          })
        : showModal({
            content: <ModalViewNote item={item} isPrintAmendDetails={isPrintAmendDetails} />,
            isReplace: false,
          });
    };

    const onEdit = (item, isNewTab = false) => {
      const { route, hash = '' } = PROGRESS_NOTE_FORM_TYPE[item?.formName] || {};
      if (!route) return;
      const isOnOriginalPage = pathname.includes(route);
      const isPageSupportEdit = isPageSupport(hash);
      const patientId = item?.patientId || searchParams.patientId;

      //For Psychotherapy progress note
      if (route === ROUTE_NAME.PSYCHOTHERAPY_PROGRESS_NOTE) {
        setAmendIdPsychotherapy();
        setEditPsychotherapy(item?.encounterId);
        if (!isPageSupportEdit)
          navigate({
            url: route,
            hash,
            clearSearchParams: true,
            searchParams: { patientId },
            newTab: isNewTab,
          });

        return;
      }
      //For Psychosocial Assessment
      if (route === ROUTE_NAME.PSYCHOSOCIAL_ASSESSMENT_NOTE) {
        setAmendIdPsychosocialAssessment();
        setEditPsychosocialAssessment(item?.encounterId);
        if (!isPageSupportEdit)
          navigate({
            url: route,
            hash,
            clearSearchParams: true,
            searchParams: { patientId },
            newTab: isNewTab,
          });
        return;
      }
      //For Psy evaluation
      if (route === ROUTE_NAME.PSYCH_EVALUATION_NOTE) {
        setAmendIdPsyEval();
        setAutoSaveIdPsyEval();
        if (isOnOriginalPage || !isPageSupportEdit) {
          navigate({
            url: `${route}/${item?.encounterId}`,
            hash,
            clearSearchParams: true,
            searchParams: { patientId },
            newTab: isNewTab,
          });
        } else {
          setEditIdPsyEval(item?.encounterId);
        }
        return;
      }

      //For Psych Note
      if (route === ROUTE_NAME.PSYCH_NOTE) {
        setAmendIdPsychNote();
        setAutoSaveIdPsychNote();
        if (isOnOriginalPage || !isPageSupportEdit) {
          navigate({
            url: `${route}/${item?.encounterId}`,
            hash,
            clearSearchParams: true,
            searchParams: { patientId },
            newTab: isNewTab,
          });
        } else {
          setEditIdPsychNote(item?.encounterId);
        }
        return;
      }

      //For PCP Note
      if (route === ROUTE_NAME.PCP_NOTE) {
        setAmendIdPCPNote();
        setAutoSaveIdPCPNote();
        if (isOnOriginalPage || !isPageSupportEdit) {
          navigate({
            url: `${route}/${item?.encounterId}`,
            hash,
            clearSearchParams: true,
            searchParams: { patientId },
            newTab: isNewTab,
          });
        } else {
          setEditIdPCPNote(item?.encounterId);
        }
        hideModal();
        return;
      }

      //For CM Note
      if (route === ROUTE_NAME.CM_NOTE) {
        setAmendIdCMNote();
        setAutoSaveIdCMNote();
        if (isOnOriginalPage || !isPageSupportEdit) {
          navigate({
            url: `${route}/${item?.encounterId}`,
            hash,
            clearSearchParams: true,
            searchParams: { patientId },
            newTab: isNewTab,
          });
        } else {
          setEditIdCMNote(item?.encounterId);
        }
        return;
      }

      //For Engagement Session Note
      if (route === ROUTE_NAME.ENGAGEMENT_SESSION_NOTE) {
        setAmendIdEngagementSessionNote();
        setAutoSaveIdEngagementSessionNote();
        if (isOnOriginalPage || !isPageSupportEdit) {
          navigate({
            url: `${route}/${item?.encounterId}`,
            hash,
            clearSearchParams: true,
            searchParams: { patientId },
            newTab: isNewTab,
          });
        } else {
          setEditIdEngagementSessionNote(item?.encounterId);
        }
        return;
      }

      //For Nurse Progress Note
      if (route === ROUTE_NAME.NURSE_PROGRESS_NOTE) {
        setAmendIdNurseProgressNote();

        if (isOnOriginalPage || !isPageSupportEdit) {
          navigate({
            url: `${route}/${item?.encounterId}`,
            hash,
            clearSearchParams: true,
            searchParams: { patientId },
            newTab: isNewTab,
          });
        } else {
          setEditIdNurseProgressNote(item?.encounterId);
        }
        return;
      }

      onAfterEditItem?.();

      navigate({
        url: `${route}/${item?.encounterId}`,
        hash,
        clearSearchParams: true,
        searchParams: { patientId },
        newTab: isNewTab,
      });
    };

    const isPageSupport = (sectionName) => !!sectionList.find((item) => item?.key === sectionName);

    const onAmend = (item, { encounterId, amendmentId }) => {
      const { route, hash } = PROGRESS_NOTE_FORM_TYPE[item?.formName] || {};
      const isOnOriginalPage = pathname.includes(route);
      const isPageSupportEdit = isPageSupport(hash);
      const patientId = item?.patientId || searchParams.patientId;

      //For Psychotherapy progress note
      if (route === ROUTE_NAME.PSYCHOTHERAPY_PROGRESS_NOTE) {
        setEditPsychotherapy(encounterId);
        setAmendIdPsychotherapy(amendmentId);
        if (!isPageSupportEdit)
          navigate({ url: route, hash, clearSearchParams: true, searchParams: { patientId } });
      }

      //For Psychosocial Assessment
      if (route === ROUTE_NAME.PSYCHOSOCIAL_ASSESSMENT_NOTE) {
        setEditPsychosocialAssessment(encounterId);
        setAmendIdPsychosocialAssessment(amendmentId);
        if (!isPageSupportEdit)
          navigate({ url: route, hash, clearSearchParams: true, searchParams: { patientId } });
      }
      //For Psy evaluation
      if (route === ROUTE_NAME.PSYCH_EVALUATION_NOTE) {
        setAutoSaveIdPsyEval();
        if (isOnOriginalPage || !isPageSupportEdit) {
          navigate({
            url: `${route}/${encounterId}`,
            hash,
            clearSearchParams: true,
            searchParams: { amendId: amendmentId, patientId },
          });
        } else {
          setEditIdPsyEval(encounterId);
          setAmendIdPsyEval(amendmentId);
        }
      }

      //For Psy note
      if (route === ROUTE_NAME.PSYCH_NOTE) {
        setAutoSaveIdPsychNote();
        if (isOnOriginalPage || !isPageSupportEdit) {
          navigate({
            url: `${route}/${encounterId}`,
            hash,
            clearSearchParams: true,
            searchParams: { amendId: amendmentId, patientId },
          });
        } else {
          setEditIdPsychNote(encounterId);
          setAmendIdPsychNote(amendmentId);
        }
      }

      //For CM note
      if (route === ROUTE_NAME.CM_NOTE) {
        setAutoSaveIdCMNote();
        if (isOnOriginalPage || !isPageSupportEdit) {
          navigate({
            url: `${route}/${encounterId}`,
            hash,
            clearSearchParams: true,
            searchParams: { amendId: amendmentId, patientId },
          });
        } else {
          setEditIdCMNote(encounterId);
          setAmendIdCMNote(amendmentId);
        }
      }
      //For CM note
      if (route === ROUTE_NAME.PCP_NOTE) {
        setAutoSaveIdPCPNote();
        if (isOnOriginalPage || !isPageSupportEdit) {
          navigate({
            url: `${route}/${encounterId}`,
            hash,
            clearSearchParams: true,
            searchParams: { amendId: amendmentId, patientId },
          });
        } else {
          setEditIdPCPNote(encounterId);
          setAmendIdPCPNote(amendmentId);
        }
      }
      //For Engagement Session Note
      if (route === ROUTE_NAME.ENGAGEMENT_SESSION_NOTE) {
        setAutoSaveIdEngagementSessionNote();
        if (isOnOriginalPage || !isPageSupportEdit) {
          navigate({
            url: `${route}/${encounterId}`,
            hash,
            clearSearchParams: true,
            searchParams: { amendId: amendmentId, patientId },
          });
        } else {
          setAmendIdEngagementSessionNote(amendmentId);
          setEditIdEngagementSessionNote(encounterId);
        }
      }

      //For Nurse Progress Note
      if (route === ROUTE_NAME.NURSE_PROGRESS_NOTE) {
        if (isOnOriginalPage || !isPageSupportEdit) {
          navigate({
            url: `${route}/${encounterId}`,
            hash,
            clearSearchParams: true,
            searchParams: { amendId: amendmentId, patientId },
          });
        } else {
          setAmendIdNurseProgressNote(amendmentId);
          setEditIdNurseProgressNote(encounterId);
        }
      }

      //For Inpatient Note
      if (route === ROUTE_NAME.INPATIENT_NOTE) {
        setAutoSaveIdInpatientNote();
        if (isOnOriginalPage || !isPageSupportEdit) {
          navigate({
            url: `${route}/${encounterId}`,
            hash,
            clearSearchParams: true,
            searchParams: { amendId: amendmentId, patientId },
          });
        } else {
          setAmendIdInpatientNote(amendmentId);
          setEditIdInpatientNote(encounterId);
        }
        scrollIntoViewId(ELEMENT_ID.INPATIENT.INPATIENT_NOTE.INPUT);
      }
    };

    const onClickPrintDynamicPN = (item) => {
      const isAllowPrint = dynamicNotes?.find(
        (i) => i?.formTypeId == item?.encounterFormId,
      )?.userPrint;

      if (!isAllowPrint) {
        toast.error(TEXT_TEMPLATE.NO_PERMISSION);
        return;
      }

      onPrint({
        url: API_DYNAMIC_PROGRESS_NOTE.HTML,
        queryString: {
          patientId: searchParams.patientId,
          id: item.testId,
          formId: item.encounterFormId,
          encounterId: item.encounterId,
          formName: item.formName,
          dynamicNote: true,
        },
      });
    };

    const onClickPrint = async (item, url = API_PROGRESS_NOTE.PRINT, isPrintAmendDetails) => {
      const isAllowPrint = pageSecurity?.find(
        (i) =>
          i?.pageKey === PROGRESS_NOTE_FORM_TYPE[item.formName]?.route ||
          i?.pageKey === PROGRESS_NOTE_FORM_TYPE[item.formName]?.hash,
      )?.print;

      if (!isAllowPrint) {
        toast.error(TEXT_TEMPLATE.NO_PERMISSION);
        return;
      }

      onPrint({
        url,
        queryString: {
          EncounterId: item.encounterId,
          patientId: searchParams.patientId,
          formType: PROGRESS_NOTE_FORM_TYPE[item.formName]?.formType || 'DynamicNote',
          formName: item.formName,
          isPrintAmendDetails,
        },
      });
    };

    const apiDelete = useRequest({
      url: API_PROGRESS_NOTE.DEFAULT,
      method: API_METHOD.DELETE,
      enabled: false,
    });

    const onDelete = (item) => {
      const isAllowDelete = pageSecurity?.find(
        (i) => i?.pageKey === PROGRESS_NOTE_FORM_TYPE[item.formName]?.route,
      )?.delete;
      if (!isAllowDelete) {
        toast.error(TEXT_TEMPLATE.NO_PERMISSION);
        return;
      }

      showModal({
        content: (
          <ModalConfirm
            message={TEXT_TEMPLATE.DELETE_CONFIRMATION(t('this note'))}
            onOk={async () => {
              await apiDelete.request({ url: `${API_PROGRESS_NOTE.DEFAULT}/${item?.encounterId}` });
              toast.success(TEXT_TEMPLATE.DELETE_SUCCESSFULLY(t('Progress Note')));
              refetch();
            }}
          />
        ),
        size: MODAL_SIZE.X_SMALL,
        isReplace: false,
      });
    };

    const getMenu = (item) => {
      const isOwner = item.employeeId === ehrUserId;
      const hadAmendRole = Object.values(roles || {})?.includes('Amend Edit');

      const formData = PROGRESS_NOTE_FORM_TYPE[item?.formName];

      let extendActions = [
        {
          key: 'view',
          text: 'View',
          onClick: onView,
        },
        {
          key: 'print',
          text: 'Print',
          onClick: () =>
            item?.dynamicProgressNotePath
              ? onClickPrintDynamicPN(item)
              : onClickPrint(item, PROGRESS_NOTE_FORM_TYPE[item.formName]?.url),
        },

        {
          key: 'print-summary',
          text: 'Print Summary',
          onClick: () => onClickPrint(item, API_PROGRESS_NOTE.PRINT_SUMMARY),
        },
        {
          key: 'visit-summary',
          text: 'Visit Summary',
          onClick: () =>
            downloadContentAPI({
              url: API_PROGRESS_NOTE.VISIT_SUMMARY,
              params: { encounterId: item.encounterId, patientId: item?.patientId },
            }),
        },
        {
          key: 'summarize',
          text: 'Summarize',
          onClick: () => {
            showModal({
              content: <ModalSummary item={item} />,
              size: MODAL_SIZE.MEDIUM,
              isReplace: false,
            });
          },
        },
      ];

      if ((!item.editable || !isOwner) && !item.isIncomplete && hadAmendRole) {
        extendActions.push({
          key: 'amend',
          text: 'Amend',
          onClick: () =>
            showModal({
              content: (
                <ModalAmend
                  formName={item?.formName}
                  encounterId={item.encounterId}
                  originalEncounterId={item.originalEncounterId}
                  patientId={searchParams.patientId}
                  onView={(itemAmend, isPrintAmendDetails) => {
                    onView(
                      {
                        encounterId: itemAmend?.encounterId,
                        formName: itemAmend?.formName,
                        amendmentId: itemAmend?.amendmentId,
                      },
                      isPrintAmendDetails,
                    );
                  }}
                  onViewOriginal={(itemAmend) => {
                    onView({
                      encounterId: itemAmend?.origEncounterId,
                      formName: itemAmend?.formName,
                      amendmentId: itemAmend?.amendmentId,
                    });
                  }}
                  onAfterSubmit={(params) => onAmend(item, params)}
                  isCreator={isOwner}
                />
              ),
              isReplace: false,
            }),
        });
      }

      if (item?.amendmentId !== null && hadAmendRole) {
        extendActions.concat([
          {
            key: 'view',
            text: 'View with Amend Details',
            onClick: () => onView(item, true),
          },
          {
            key: 'printAmendDetail',
            text: 'Print with Amend Details',
            onClick: () => onClickPrint(item, PROGRESS_NOTE_FORM_TYPE[item.formName]?.url, true),
          },
        ]);
      }

      if (!formData && !item?.dynamicProgressNotePath) {
        return { items: extendActions, shouldFocusOnMount: false };
      }

      if (isOwner && !item?.dynamicProgressNotePath) {
        if (item.editable && item.formName !== PROGRESS_NOTE_FORM_NAME.GROUP_NOTE) {
          extendActions.push({
            key: 'edit',
            text: 'Edit',
            onClick: () => onEdit(item),
          });
        }
        if (item.isIncomplete) {
          extendActions.push({
            key: 'delete',
            text: 'Delete',
            onClick: onDelete,
          });
        }
      }

      if (
        isOwner &&
        item.formName === PROGRESS_NOTE_FORM_NAME.GROUP_NOTE &&
        !item?.encounter837BatchId &&
        !item?.encounterHcfaBatchId
      ) {
        const isOnGroupNotePage = pathname.includes(ROUTE_NAME.GROUP_NOTE);
        extendActions = extendActions.concat([
          {
            key: 'edit-single-note',
            text: 'Edit Single Note',
            onClick: () => {
              const isPageSupportEdit = isPageSupport(MODULE_NAME.PROGRESS_NOTE_GROUP);
              setCurrentSectionGN('');
              if (isOnGroupNotePage || !isPageSupportEdit) {
                navigate({
                  url: ROUTE_NAME.GROUP_NOTE,
                  clearSearchParams: true,
                  searchParams: { [SEARCH_PARAMS.ENCOUNTER_ID]: item?.encounterId },
                  hash: MODULE_NAME.PROGRESS_NOTE_GROUP,
                });
              } else {
                setAutoSaveGroupIdGN();
                setAutoSaveIdGN();
                setEncounterBatchIdGN();
                setEncounterIdGN(item?.encounterId);
                navigate({ hash: MODULE_NAME.PROGRESS_NOTE_GROUP });
              }
            },
          },
          {
            key: 'edit-group-note',
            text: 'Edit Group Note',
            onClick: () => {
              const isPageSupportEdit = isPageSupport(MODULE_NAME.PROGRESS_NOTE_GROUP);
              setCurrentSectionGN('');
              if (isOnGroupNotePage || !isPageSupportEdit) {
                navigate({
                  url: ROUTE_NAME.GROUP_NOTE,
                  clearSearchParams: true,
                  searchParams: { [SEARCH_PARAMS.ENCOUNTER_BATCH_ID]: item?.encounterBatchId },
                  hash: MODULE_NAME.PROGRESS_NOTE_GROUP,
                });
              } else {
                setAutoSaveGroupIdGN();
                setAutoSaveIdGN();
                setEncounterIdGN();
                setEncounterBatchIdGN(item?.encounterBatchId);
                navigate({ hash: MODULE_NAME.PROGRESS_NOTE_GROUP });
              }
            },
          },
        ]);
      }

      if (isOwner && item?.dynamicProgressNotePath) {
        if (item.editable) {
          extendActions.push({
            key: 'edit',
            text: 'Edit',
            onClick: () => {
              navigate({
                url: `${ROUTE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER}/${item?.encounterFormId}/answer/${item?.encounterId}`,
                hash: '',
                searchParams: { patientId: item?.item || searchParams.patientId },
              });
            },
          });
        }
      }

      return { items: extendActions, shouldFocusOnMount: false };
    };

    const onRenderRow = (_props) => {
      const { isIncomplete } = _props.item;

      return <DetailsRow className={isIncomplete && css.table.rowColorRed} {..._props} />;
    };

    useImperativeHandle(ref, () => ({
      onEdit,
    }));

    useEffect(() => {
      if (searchParams?.amendBatchId && searchParams?.encounterId && searchParams.formName) {
        //show modal amend when these ids are available
        showModal({
          content: (
            <ModalAmend
              formName={searchParams?.formName}
              encounterId={searchParams?.encounterId}
              originalEncounterId={searchParams?.encounterId}
              onView={(itemAmend, isPrintAmendDetails) => {
                onView(
                  {
                    encounterId: itemAmend?.encounterId,
                    formName: itemAmend?.formName,
                    amendmentId: itemAmend?.amendmentId,
                  },
                  isPrintAmendDetails,
                );
              }}
              onViewOriginal={(itemAmend) => {
                onView({
                  encounterId: itemAmend?.origEncounterId,
                  formName: itemAmend?.formName,
                  amendmentId: itemAmend?.amendmentId,
                });
              }}
              onAfterSubmit={(params) => onAmend(item, params)}
            />
          ),
          isReplace: false,
        });
      }
    }, [searchParams?.amendBatchId, searchParams?.encounterId, searchParams.formName]);

    return (
      <Table
        tableSelection={{
          selection: selection,
          checkboxVisibility: CheckboxVisibility.always,
        }}
        columns={getColumn}
        items={data?.items || []}
        loading={isFetching}
        getMenuProps={getMenu}
        totalItems={data?.totalItems}
        metadata={params}
        onMetadataChange={updateParams}
        onRenderRow={onRenderRow}
        refetch={refetch}
      />
    );
  },
);

export default HistoryTable;

const getColumn = [
  {
    name: 'ID',
    fieldName: 'encounterId',
  },
  {
    name: 'Form Type',
    fieldName: 'formName',
    sortable: true,
  },
  {
    name: 'Provider',
    fieldName: 'providerName',
    sortable: true,
  },
  {
    name: 'Employee',
    fieldName: 'firstName',
    sortable: true,
    renderItem: ({ lastName, firstName }) => `${firstName} ${lastName}`,
  },
  {
    name: 'Patient Name',
    fieldName: 'patientName',
    sortable: true,
  },
  {
    name: 'Start Date',
    fieldName: 'startTime',
    sortable: true,
    renderItem: ({ startDate, startTime, durationFlag, durationInMinutes }, _, c, render) =>
      moment(startDate).isBefore(moment('1/1/2021'))
        ? render({ date: startTime, withTime: true })
        : [1, 2].includes(durationFlag)
        ? render({ date: startTime }) +
          (!!durationInMinutes ? ` - Duration: ${durationInMinutes} minutes` : '')
        : render({ date: startTime, withTime: true }),
  },
  {
    name: 'End Date',
    fieldName: 'endTime',
    isResizable: true,
    sortable: true,
    renderItem: ({ startDate, endTime, durationFlag }, _, c, render) =>
      moment(startDate).isBefore(moment('1/1/2021'))
        ? render({ date: endTime, withTime: true })
        : durationFlag === 1
        ? ''
        : render({ date: endTime, withTime: true }),
  },
  {
    name: 'Service',
    fieldName: 'serviceCode',
    sortable: true,
  },
  {
    name: 'Description',
    fieldName: 'serviceName',
    sortable: true,
  },
  {
    name: 'Units',
    fieldName: 'units',
    sortable: true,
  },
  {
    name: 'Status',
    fieldName: 'isIncomplete',
    sortable: true,
    renderItem: ({ isIncomplete }) => {
      const statusInfo =
        COMPLETION_STATUS[
          isIncomplete ? COMPLETION_STATUS_KEY.incomplete : COMPLETION_STATUS_KEY.complete
        ];

      return <Tag type={statusInfo?.type}>{statusInfo?.text}</Tag>;
    },
  },
  {
    name: 'Create Info',
    fieldName: 'createDate',
    sortable: true,
    renderItem: (i, _, c, render) =>
      render({ prefix: t('Created on'), date: i.createDate, user: i.createUser, withTime: true }),
  },
  {
    name: 'Update Info',
    fieldName: 'updateDate',
    sortable: true,
    renderItem: (i, _, c, render) =>
      render({ prefix: t('Updated on'), date: i.updateDate, user: i.updateUser, withTime: true }),
  },
  {
    key: 'Action',
    name: 'Action',
    fieldName: 'action',
  },
];
