import useClass from 'hooks/useClass';
import FlagImage from 'components/Image/flagImage';
import cn from 'classnames';
import { DefaultButton, PrimaryButton } from '@fluentui/react';
import { t } from 'utils/string';
import React, { useCallback } from 'react';
import useRequest from 'hooks/useRequest';
import { API_CALL_OUTSIDE, API_METHOD, API_PATIENT } from 'constants/urlRequest';
import LinkPrimary from 'components/Link/LinkPrimary';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import { Col, Container } from 'components/Layout/Grid';
import useModal from 'hooks/useModal';
import AddImageModal from 'components/Image/AddImageModal';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import FieldPhoneNumber from 'components/Form/FieldPhoneNumber';
import FieldSelectorPrimary from 'components/Form/FieldSelectorPrimary';
import { formatToFluentOptions } from 'utils';
import { useForm } from 'react-hook-form';
import FieldText from 'components/Form/FieldText';
import HookFormProvider from 'components/HookForm/HookFormProvider';

function EditPatientModal({ data, className = '', onUpdateSuccess, apiContractStatus, ...props }) {
  const css = useClass(styles);
  const { showModal, hideModal } = useModal();
  const apiUpdateAvatar = useRequest({
    url: API_PATIENT.UPDATE_AVATAR,
    enabled: false,
    method: API_METHOD.POST,
  });

  const apiUpdate = useRequest({
    url: API_CALL_OUTSIDE.UPDATE_PATIENT,
    enabled: false,
    method: API_METHOD.PUT,
  });

  const patient = data;
  const fullName = patient?.fullName || patient?.patientName || '';

  const methods = useForm({
    defaultValues: { ...patient, photo: patient?.photo || patient?.avatarUrl },
  });

  const { handleSubmit, setValue, getValues } = methods;

  const onSave = useCallback(
    async (formData) => {
      const { photo, patientId, cellPhone, workPhone, homePhone, email, contactStatusId } =
        formData;

      const infoPayload = {
        patientId,
        cellPhone,
        workPhone,
        homePhone,
        email,
        contactStatusId,
      };

      const handleUpdateInfo = () => {
        return apiUpdate.request({
          url: API_CALL_OUTSIDE.UPDATE_PATIENT,
          payload: infoPayload,
        });
      };

      if (photo?.base64) {
        const [info, avatar] = await Promise.allSettled([
          handleUpdateInfo(),
          apiUpdateAvatar.request({
            payload: {
              patientId: patient.patientId,
              avatarBase64: photo.base64,
            },
          }),
        ]);

        onUpdateSuccess({
          ...(info.status === 'fulfilled' ? infoPayload : {}),
          ...(avatar.status === 'fulfilled' ? { photo: photo?.base64 } : {}),
        });
      } else {
        await handleUpdateInfo();
        onUpdateSuccess(infoPayload);
      }
      hideModal();
    },
    [apiUpdate, apiUpdateAvatar, hideModal, onUpdateSuccess, patient.patientId],
  );

  const handleEditImage = useCallback(() => {
    showModal({
      content: (
        <AddImageModal
          onUpdate={(file) => {
            setValue('photo', file);
            hideModal();
          }}
        />
      ),
      isReplace: false,
    });
  }, [hideModal, setValue, showModal]);

  return (
    <HookFormProvider {...methods}>
      <ModalLayout
        title={
          <div className={css.header}>
            <img src={'/assets/images/calling/contract.webp'} alt={'contract'} />
            <span>{fullName}</span>
          </div>
        }
        className={cn(css.patient, className)}
        footerRightSide={
          <>
            <LoadingWrapper loading={apiUpdateAvatar.loading || apiUpdate.loading}>
              <PrimaryButton onClick={handleSubmit(onSave)}>{t('Save')}</PrimaryButton>
            </LoadingWrapper>
            <DefaultButton onClick={hideModal}>{t('Cancel')}</DefaultButton>
          </>
        }
        {...props}
      >
        <Container>
          <Col className={css.imageWrapper}>
            <FlagImage
              size={129}
              className="round image-profile img-full"
              src={getValues('photo')?.base64 || getValues('photo')}
              letterAvatar={fullName}
            />
            <LinkPrimary onClick={handleEditImage}>{t('Edit Profile Image')}</LinkPrimary>
          </Col>
          <Col md={6}>
            <FieldPhoneNumber name={'homePhone'} title={t('Home Phone')} />
          </Col>
          <Col md={6}>
            <FieldPhoneNumber name={'workPhone'} title={t('Work Phone')} />
          </Col>
          <Col md={6}>
            <FieldPhoneNumber name={'cellPhone'} title={t('Cellphone')} />
          </Col>
          <Col md={6}>
            <FieldSelectorPrimary
              isFast={false}
              name={'contactStatusId'}
              title={t('Contact Status')}
              options={formatToFluentOptions(apiContractStatus?.data)}
            />
          </Col>
          <Col>
            <FieldText name={'email'} title={t('Email')} type={'email'} />
          </Col>
        </Container>
      </ModalLayout>
    </HookFormProvider>
  );
}

const styles = (theme) => ({
  header: {
    display: 'flex',
    alignItems: 'center',
    gap: 12,
    img: {
      width: 20,
      height: 20,
    },
    span: {
      fontSize: 14,
      fontWeight: 600,
      flex: 1,
    },
  },
  imageWrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 8,
    u: {
      textDecoration: 'none',
    },
  },
  footer: {
    borderTop: `1px solid ${theme.custom.grey300}`,
    padding: '12px 16px',
    display: 'flex',
    gap: 8,
  },
});
export default EditPatientModal;
