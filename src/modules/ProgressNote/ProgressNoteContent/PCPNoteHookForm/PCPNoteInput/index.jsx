import React, { useContext, useMemo, useState } from 'react';
import { t } from 'utils/string';
import { Stack } from '@fluentui/react';
import { useClass, useModal, useNavigator, useNoteType, useRequest, useUser } from 'hooks';
import {
  AI_SIDEBAR_KEY,
  NOTE_INFORMATION_FIELDS_NAME,
  PROGRESS_NOTE_DISPLAY,
  PROGRESS_NOTE_FORM_NAME,
  PROGRESS_NOTE_VIEW,
  PCP_SECTION_ID,
} from 'constants/progressNote';
import { useParams } from 'react-router';
import { API_PCP_NOTE, API_PROGRESS_NOTE } from 'constants/urlRequest';
import moment from 'moment';
import {
  formatInitialDataPcpcNote,
  formatSaveValuePCP,
} from 'modules/ProgressNote/ProgressNoteContent/utils';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import View from 'components/View';
import TableContent from 'components/TableContent';
import { TABLE_CONTENT } from './tableContentData';
import NoteLayout from '../NoteLayout';
import cn from 'classnames';
import AutoSaveNote from '../AutoSaveNote';
import LogicOnChange from '../components/LogicOnChange';
import { FORM_ID } from 'constants/formId';
import Footer from './Footer';
import { MODULE_NAME, ROUTE_NAME } from 'constants/routes';
import ELEMENT_ID from 'constants/elementId';
import { globalScrollToId } from 'utils/globalScroll';
import { PCPProgressNoteContext } from 'contexts/PCPProgressNoteContext';
import {
  PCPSuggestions,
  PatientTheme,
  RecordAISession,
  RecordSetting,
  Sentiment,
  Transcript,
} from '../AISession';
import { BuilderSuggestionProvider } from './BuilderSuggestionContext';
import { fetchByQueryKey } from 'apis/queryClient';
import NoteInformation from '../NoteInformation';
import QUERY_KEY from 'constants/queryKey';
import useInitProblemAndPlan from 'modules/ProgressNote/components/ProblemAndPlan/useInitProblemAndPlan';
import ContinueNoteModal from '../components/ContinueNoteModal';
import { MODAL_SIZE } from 'constants/modal';
import LoadPreviousNote from '../components/LoadPreviousNote';
import { useForm, useWatch } from 'react-hook-form';
import HookFormProvider from 'components/HookForm/HookFormProvider';

function PCPNoteInput({ scrollContainerId }) {
  const { id } = useParams();
  const {
    editId: contextEditId,
    setEditId,
    autoSaveId: contextAutoSaveId,
    setAutoSaveId,
    setAmendId,
    amendId,
  } = useContext(PCPProgressNoteContext);
  const { searchParams, pathname, navigate } = useNavigator();
  const formId = useNoteType(PROGRESS_NOTE_FORM_NAME.PCP_NOTE);
  const isOnOriginalPage = pathname.includes(ROUTE_NAME.PCP_NOTE);

  const resetContext = () => {
    setEditId();
    setAutoSaveId();
    setAmendId();
  };

  const onAfterResetWhenChangePatient = () => {
    resetContext();
    if (isOnOriginalPage) {
      navigate({ pathname: ROUTE_NAME.PCP_NOTE });
    }
  };

  const onAfterSubmitSuccess = () => {
    resetContext();
    fetchByQueryKey(...[API_PROGRESS_NOTE.ENCOUNTER, searchParams?.patientId]);
    fetchByQueryKey(QUERY_KEY.PROGRESS_NOTE_HISTORY);
    fetchByQueryKey(QUERY_KEY.PROGRESS_NOTE_AUTO_SAVE);
    if (isOnOriginalPage) {
      navigate({
        pathname: ROUTE_NAME.PCP_NOTE,
        hash: MODULE_NAME.PROGRESS_NOTE_HISTORY,
        search: { patientId: searchParams.patientId },
        clearSearchParams: true,
      });
    } else {
      globalScrollToId(ELEMENT_ID.NOTE.ALL_NOTE_HISTORY);
    }
  };

  const onAfterReset = () => {
    resetContext();
    if (isOnOriginalPage) {
      navigate({
        pathname: ROUTE_NAME.PCP_NOTE,
        hash: MODULE_NAME.PROGRESS_NOTE_PCP,
        search: { patientId: searchParams.patientId },
      });
    }
  };

  const objectParams = isOnOriginalPage
    ? { id, autoSaveId: searchParams?.autoSaveId, amendId: searchParams?.amendId }
    : { id: contextEditId, autoSaveId: contextAutoSaveId, amendId };

  return (
    formId && (
      <NoteContent
        formId={formId}
        {...objectParams}
        onAfterResetWhenChangePatient={onAfterResetWhenChangePatient}
        onAfterSubmitSuccess={onAfterSubmitSuccess}
        onAfterReset={onAfterReset}
        scrollContainerId={scrollContainerId}
      />
    )
  );
}

const NoteContent = ({
  formId,
  id,
  autoSaveId,
  onAfterResetWhenChangePatient,
  onAfterSubmitSuccess,
  onAfterReset,
  amendId,
  scrollContainerId,
}) => {
  const [currentSection, setCurrentSection] = useState(PCP_SECTION_ID.NOTE_INFOMATION);
  const [errorTabs, setErrorTabs] = useState([]);
  const [showSidebar, setShowSidebar] = useState(true);
  const { showModal } = useModal();
  const [isStart, setIsStart] = useState();
  const isEdit = !!id || !!autoSaveId;
  const {
    searchParams: { patientId },
  } = useNavigator();
  const { info } = useUser();

  const apiEncounter = useRequest({
    key: [API_PCP_NOTE.DETAIL, patientId, id, formId],
    url: API_PCP_NOTE.DETAIL,
    params: { serviceDate: moment().toISOString() },
    enabled: !autoSaveId,
    requiredParams: {
      ...(id ? { encounterId: id } : {}),
      patientId,
    },
    autoRefetch: true,
    saveHistory: false,
    cacheTime: 0,
  });

  const apiGetAutoSave = useRequest({
    key: [API_PCP_NOTE.AUTO_SAVE, autoSaveId],
    url: `${API_PCP_NOTE.AUTO_SAVE}/${autoSaveId}`,
    enabled: !!autoSaveId,
    autoRefetch: true,
  });

  const activeProblemsFromEdit =
    (apiEncounter?.data || apiGetAutoSave?.data || {})?.diagnosisSection?.activeProblems || [];

  const initProblemAndPlan = useInitProblemAndPlan({ activeProblemsFromEdit, isForPCP: true });

  const endUseData = apiGetAutoSave?.data || apiEncounter?.data;

  const initialValues = useMemo(() => {
    const encounterId = isEdit ? endUseData?.encounter?.encounterId : null;
    const { arrayActiveProblems, arrayProblems, objectProblems } = initProblemAndPlan;
    return formatInitialDataPcpcNote({
      amendId,
      patientId,
      encounterFormId: formId,
      data: endUseData,
      isForCreate: !(endUseData?.encounter?.encounterId || autoSaveId),
      encounterId,
      employeeId: info?.ehrUserId,
      activeProblems: arrayActiveProblems,
      activeProblemsTemp: objectProblems,
      listProblemLoadUI: arrayProblems,
      activeProblemsFromEdit,
    });
  }, [endUseData, initProblemAndPlan]);
  const methods = useForm({ values: initialValues });

  const sidebarItems = useMemo(
    () => [
      {
        key: AI_SIDEBAR_KEY.RECORD,
        text: t('Record Session'),
        rightSide: <RecordSetting />,
        iconName: 'Record',
        Content: <RecordAISession noteType={PROGRESS_NOTE_FORM_NAME.PCP_NOTE} />,
      },
      {
        key: AI_SIDEBAR_KEY.TRANSCRIPT,
        text: t('Transcript'),
        iconName: 'Transcript',
        Content: <Transcript />,
        disabled: true,
      },
      {
        key: AI_SIDEBAR_KEY.THEME,
        text: t('Patient Theme'),
        iconName: 'Person',
        Content: <PatientTheme />,
        disabled: true,
      },
      {
        key: AI_SIDEBAR_KEY.ANALYSIS,
        text: t('Sentiment Analysis'),
        iconName: 'Emoji2',
        Content: <Sentiment />,
        disabled: true,
      },
      {
        key: AI_SIDEBAR_KEY.SUGGESTION,
        text: t('Suggestions'),
        iconName: 'Lightbulb',
        Content: <PCPSuggestions noteType={PROGRESS_NOTE_FORM_NAME.PCP_NOTE} />,
        disabled: true,
      },
    ],
    [endUseData],
  );

  const subType = useWatch({
    name: NOTE_INFORMATION_FIELDS_NAME.subType,
    control: methods?.control,
  });
  const nonBillable = useWatch({
    name: NOTE_INFORMATION_FIELDS_NAME.nonBillable,
    control: methods?.control,
  });

  const displayType = useWatch({
    name: 'ui.displayType',
    control: methods?.control,
  });

  const isShowTableContent = useWatch({
    name: 'ui.isShowTableContent',
    control: methods?.control,
  });

  const tableContentFilter = TABLE_CONTENT.filter(
    (item) => (!subType && !nonBillable) || item?.subServices?.includes(subType),
  );

  return (
    <LoadingWrapper loading={apiEncounter?.loading || apiGetAutoSave?.loading}>
      <HookFormProvider {...methods}>
        <BuilderSuggestionProvider>
          <NoteLayout
            formId={FORM_ID.PCP_NOTE}
            sidebarItems={sidebarItems}
            showSidebar={showSidebar}
            setShowSidebar={setShowSidebar}
            isStart={isStart || isEdit}
            validateStartNote
            setIsStart={(isStart) => {
              if (isStart) {
                showModal({
                  content: <ContinueNoteModal onClose={() => setIsStart(true)} />,
                  size: MODAL_SIZE.MEDIUM,
                });
              } else {
                setIsStart(false);
              }
            }}
            onToggleShowTableContent={() => {
              const values = methods.getValues();
              methods.setValue('ui.isShowTableContent', !values?.ui?.isShowTableContent);
            }}
            isShowTableContent={isShowTableContent}
            displayType={displayType}
            onChangeDisplayType={(displayType) => methods.setValue('ui.displayType', displayType)}
            title={t('PCP Note Input')}
            startStep={<StartStep data={endUseData} displayType={displayType} />}
            footer={
              <Footer
                isAmend={!!amendId}
                setIsStart={setIsStart}
                setCurrentSection={setCurrentSection}
                setErrorTabs={setErrorTabs}
                onAfterSubmitSuccess={onAfterSubmitSuccess}
                onAfterReset={onAfterReset}
              />
            }
            id={ELEMENT_ID.PCP_NOTE.PCP_NOTE_INPUT}
          >
            <Stack horizontal tokens={{ childrenGap: 24 }}>
              <Stack grow className="o-clip">
                <View
                  displayType={displayType}
                  showIndex
                  allowSkip
                  currentSection={currentSection}
                  setCurrentSection={setCurrentSection}
                  tableContent={tableContentFilter}
                  data={endUseData}
                  errorTabs={errorTabs}
                  scrollContainerId={scrollContainerId}
                />
              </Stack>
              {isShowTableContent && (
                <TableContent
                  currentSection={currentSection}
                  setCurrentSection={setCurrentSection}
                  tableContent={tableContentFilter}
                />
              )}
              <AutoSaveNote autoSaveUrl={API_PCP_NOTE.AUTO_SAVE} format={formatSaveValuePCP} />
            </Stack>
          </NoteLayout>
        </BuilderSuggestionProvider>

        <LogicOnChange
          onAfterResetWhenChangePatient={onAfterResetWhenChangePatient}
          patientId={patientId}
          setIsStart={setIsStart}
        />
        <LoadPreviousNote
          isEdit={isEdit}
          isStart={isStart}
          patientId={patientId}
          formId={formId}
          url={API_PCP_NOTE.DETAIL}
        />
      </HookFormProvider>
    </LoadingWrapper>
  );
};

const StartStep = ({ displayType, ...props }) => {
  const css = useClass();

  return (
    <div className={cn('p-16 m-16', css.cmClass.border, css.cmClass.backgroundColor)}>
      <NoteInformation
        {...props}
        servicesName="serviceSection.services"
        fieldType={displayType === PROGRESS_NOTE_DISPLAY.DOCUMENT && PROGRESS_NOTE_VIEW.LIST}
        tabId={PCP_SECTION_ID.NOTE_INFOMATION}
      />
    </div>
  );
};

export default PCPNoteInput;
