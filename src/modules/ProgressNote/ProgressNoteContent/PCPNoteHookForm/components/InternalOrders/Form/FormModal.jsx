import ModalLayout from 'components/GlobalModal/ModalLayout';
import React, { Fragment, useEffect } from 'react';
import { DefaultButton, PrimaryButton } from '@fluentui/react';
import { fetchByQueryKey } from 'apis/queryClient';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import QUERY_KEY from 'constants/queryKey';
import { TEXT_TEMPLATE } from 'constants/texts';
import { API_INTERNAL_ORDER, API_METHOD } from 'constants/urlRequest';
import { useModal, useNavigator, useRequest } from 'hooks';
import InternalOrderForm from './index';
import { handleValidationBE } from '../../../PCPNoteInput/form';
import { t } from 'utils/string';
import { useForm } from 'react-hook-form';
import HookFormProvider from 'components/HookForm/HookFormProvider';

function FormModal({ internalOrder, inpatientId }) {
  const { searchParams } = useNavigator();
  const { hideModal } = useModal();

  const methods = useForm({
    defaultValues: {
      internalOrderId: null,
      listInternalOrderLookupId: [],
      listVaccinesId: [],
      additionalItems: '',
      comments: '',
      completedDate: null,
      pendFor: null,
      pendComments: '',
      patientId: inpatientId || searchParams.patientId,
    },
  });

  useEffect(() => {
    methods.setValue('patientId', inpatientId || searchParams.patientId);
  }, [inpatientId, searchParams.patientId]);

  const apiSave = useRequest({
    method: internalOrder ? API_METHOD.PUT : API_METHOD.POST,
    url: API_INTERNAL_ORDER.UPDATE(internalOrder?.internalOrderId || ''),
  });

  const submitForm = async (values) => {
    const payload = internalOrder
      ? { ...values, listInternalOrderLookupId: [values.internalOrderLookupId] }
      : values;

    await apiSave.request({
      payload,
      options: {
        onError: (error) => handleValidationBE(methods, error),
        onSuccess: () => {
          toast.success(
            internalOrder
              ? TEXT_TEMPLATE.UPDATE_SUCCESSFULLY(t('Internal Order'))
              : TEXT_TEMPLATE.ADD_SUCCESSFULLY(t('Internal Order')),
          );
          fetchByQueryKey(
            ...[QUERY_KEY.PSYCH_NOTE_INTERNAL_ORDER_LIST, inpatientId || searchParams.patientId],
          );
          hideModal();
        },
      },
    });
  };

  const onReset = () => {
    methods.reset();
    methods.setValue('patientId', inpatientId || searchParams.patientId);
  };

  return (
    <HookFormProvider {...methods}>
      <ModalLayout
        title={t('Internal Order Input')}
        loading={apiSave.loading}
        footerRightSide={
          <Fragment>
            <PrimaryButton data-isbutton="button" onClick={methods.handleSubmit(submitForm)}>
              {t('Submit')}
            </PrimaryButton>
            <CalloutConfirmation onOk={onReset}>
              <DefaultButton text={t('Reset')} />
            </CalloutConfirmation>
            <DefaultButton text={t('Close')} onClick={hideModal} />
          </Fragment>
        }
      >
        <div className="p-16">
          <InternalOrderForm inpatientId={inpatientId} isEdit={!!internalOrder} />
        </div>
      </ModalLayout>
    </HookFormProvider>
  );
}

export default FormModal;
