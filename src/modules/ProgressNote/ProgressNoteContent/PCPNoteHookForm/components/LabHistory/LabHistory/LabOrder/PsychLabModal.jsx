import React from 'react';
import { De<PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryButton, Stack } from '@fluentui/react';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import { TEXT_TEMPLATE } from 'constants/texts';
import { useNavigator, useRequest, useModal } from 'hooks';
import LabForm from 'modules/LabOrder/Form/LabForm';
import { fetchByQueryKey } from 'apis/queryClient';
import QUERY_KEY from 'constants/queryKey';
import { API_LAB, API_METHOD } from 'constants/urlRequest';
import { toast } from 'react-toastify';
import { handleValidationBE } from '../../../../PCPNoteInput/form';
import FieldDropDown from 'components/HookForm/FieldDropdown';
import { formatToFluentOptions } from 'utils';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import { t } from 'utils/string';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import { useForm } from 'react-hook-form';
import { useWatch } from 'react-hook-form';
import HookFormProvider from 'components/HookForm/HookFormProvider';

function PsychLabModal() {
  const { searchParams } = useNavigator();
  const { hideModal } = useModal();
  const apiStatus = useRequest({ url: API_LAB.GET_STATUS });
  const pendFor = apiStatus?.data?.find((i) => i.description.includes('Pend')) || {};

  const methods = useForm({
    defaultValues: { patientId: searchParams.patientId },
  });

  const completionStatus = useWatch({ name: 'completionStatus' });

  const apiSave = useRequest({
    url: API_LAB.DEFAULT,
    method: API_METHOD.POST,
    onSuccess: () => {
      fetchByQueryKey(QUERY_KEY.LAB_HISTORY);
      toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY());
      hideModal();
    },
  });

  const onSubmit = async (values) => {
    let temp = { ...values };
    temp.labPanels = Object.values(temp.labPanelsSave || {}).filter((i) => i.checked);
    temp.dxCodeIds = (temp.dx || []).map((i) => i.icD10Code);
    await apiSave.mutateAsync(temp, {
      onError: (error) => {
        handleValidationBE(methods, error);
      },
    });
  };

  return (
    <HookFormProvider {...methods}>
      <ModalLayout
        title="Lab Input"
        loading={apiSave.loading}
        footerRightSide={
          <Stack horizontal tokens={{ childrenGap: 16 }}>
            <FieldDropDown
              placeholder="Completion Status"
              required
              name="completionStatus"
              options={formatToFluentOptions(apiStatus?.data, 'description', 'pendStepId')}
            />
            {completionStatus === pendFor?.pendStepId && (
              <FieldSelectorPrimary
                name="pendFor"
                placeholder="Pend For"
                options={(pendFor?.users || []).map((item) => ({
                  ...item,
                  text: item?.firstName + ' ' + item?.lastName,
                  key: item?.userLogin,
                }))}
              />
            )}
            <PrimaryButton
              text={t('Submit')}
              data-isbutton="button"
              onClick={methods.handleSubmit(onSubmit)}
            />
            <CalloutConfirmation onOk={() => methods.reset()}>
              <DefaultButton text={t('Reset')} data-isbutton="button" />
            </CalloutConfirmation>
          </Stack>
        }
      >
        <Stack tokens={{ padding: 16 }}>
          <LabForm />
        </Stack>
      </ModalLayout>
    </HookFormProvider>
  );
}

export default PsychLabModal;
