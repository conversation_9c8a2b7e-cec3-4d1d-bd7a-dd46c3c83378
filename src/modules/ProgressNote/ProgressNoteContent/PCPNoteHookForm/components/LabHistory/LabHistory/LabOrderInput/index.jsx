import React, { useMemo, useState } from 'react';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import { useModal, useClass, useRequest, useNavigator } from 'hooks';
import FieldDropdown from 'components/HookForm/FieldDropdown';
import { formatToFluentOptions } from 'utils';
import { Col, Row, Container } from 'components/Layout/Grid';
import { API_LAB, API_PROBLEM, API_METHOD } from 'constants/urlRequest';
import cn from 'classnames';
import HorizontalRule from 'components/HorizontalRule';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import FieldCheckbox from 'components/HookForm/FieldCheckbox';
import Table from 'components/Table';
import { handleValidationBE } from '../../../../PCPNoteInput/form';
import FieldText from 'components/HookForm/FieldText';
import { _get } from 'utils/form';
import TableDiagnosisPanel from './TableDiagnosisPanel';
import { toast } from 'react-toastify';
import FieldDate from 'components/HookForm/FieldDate';
import FieldTime from 'components/HookForm/FieldTime';
import { momentCloneDateTime } from 'utils/time';
import { fetchByQueryKey } from 'apis/queryClient';
import QUERY_KEY from 'constants/queryKey';
import ConfirmCallout from 'components/Modal/ConfirmCallout';
import { useBoolean } from '@fluentui/react-hooks';
import { removeUndefinedFromObject, getSubsetOfObject } from 'utils';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { ORDER_DIRECTION } from 'constants/index';
import { TEXT_TEMPLATE } from 'constants/texts';
import { t } from 'utils/string';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import { useForm, useFormContext, useWatch } from 'react-hook-form';
import HookFormProvider from 'components/HookForm/HookFormProvider';

const Wrapper = ({ title, children }) => {
  const css = useClass(styles);

  return (
    <div className={css.wrapper}>
      {title && (
        <React.Fragment>
          <div className={css.title}>{title}</div>
          <HorizontalRule />
        </React.Fragment>
      )}
      {children}
    </div>
  );
};

const initialValuesForm = {
  completionStatus: '0',
  siteId: null,
  providerId: null,
  labFacilityId: null,
  frequencyId: null,
  frequencyTimeId: null,
  labPanels: [],
  dxCodeIds: [],
  specimenCollected: null,
  fasting: null,
  priority: null,
  collectionTime: null,
  clinicalInfo: null,
  labPanelsSave: {},
  templateId: null,
  findLabSearchTemp: null,
  templateName: null,
};

const transferDataPanels = (obj) =>
  Object.keys(obj)
    .map((item) => {
      const temp = { note: null, ...obj[item] };
      delete temp.checked;
      return temp;
    })
    .filter((item) => item?.panelId);

const LabOrderInput = ({ providers = [], initialValue }) => {
  const css = useClass(styles);
  const { hideModal } = useModal();
  const { searchParams } = useNavigator();
  const [morePanels, setMorePanels] = useState([]);
  const [isCalloutSaveTemplateVisible, { toggle: toggleIsCalloutSaveTemplateVisible }] =
    useBoolean(false);

  const methods = useForm({
    defaultValues: initialValue || initialValuesForm,
  });

  const { handleSubmit, reset, setValue, getValues, watch, setError } = methods;
  const labFacilityId = watch('labFacilityId');
  const providerId = watch('providerId');
  const templateId = watch('templateId');

  const inputData = useRequest({
    url: API_LAB.INPUT_DATA,
    requiredParams: {
      PatientId: searchParams.patientId,
    },
    autoRefetch: true,
  });

  const problemList = useRequest({
    url: API_PROBLEM.DEFAULT,
    params: {
      OrderBy: 'diagnostic10Code',
      OrderDirection: ORDER_DIRECTION.ASC,
    },
    requiredParams: { patientId: searchParams.patientId },
    autoRefetch: true,
    cacheTime: 0,
  });

  const labTemplate = useRequest({ key: API_LAB.TEMPLATE, url: API_LAB.TEMPLATE });

  const panel = useRequest({
    url: API_LAB.PANEL,
    params: { PageSize: 1000 },
    requiredParams: { patientId: searchParams.patientId },
    autoRefetch: true,
    enabled: false,
    cacheTime: 0,
  });

  const detailLabTemplate = useRequest({
    enabled: false,
    cacheTime: 0,
  });

  const findLabTest = useRequest({
    key: API_LAB.PANEL + 'Find-Lab-Test',
    url: API_LAB.PANEL,
    params: { Page: 0, PageSize: 10 },
    requiredParams: { patientId: searchParams.patientId },
    autoRefetch: true,
    enabled: false,
    cacheTime: 0,
  });

  const createLab = useRequest({
    key: API_LAB.DEFAULT + 'Create',
    url: API_LAB.DEFAULT,
    method: API_METHOD.POST,
  });

  const createLabTemplate = useRequest({
    key: API_LAB.TEMPLATE + 'Create',
    url: API_LAB.TEMPLATE,
    method: API_METHOD.POST,
  });

  const deleteLabTemplate = useRequest({
    key: API_LAB.TEMPLATE + 'Delete',
    url: API_LAB.TEMPLATE,
    method: API_METHOD.DELETE,
    enabled: false,
  });

  const handleCreateLabTemplate = () => {
    const dataSubmit = {};
    const values = getValues();
    dataSubmit.templateName = values?.templateName;
    dataSubmit.templateId = values?.templateId;
    dataSubmit.labPanels = transferDataPanels(values?.labPanelsSave);
    dataSubmit.labPanels.length === 0 && delete dataSubmit.labPanels;
    createLabTemplate.request({
      payload: dataSubmit,
      options: {
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('Template')));
          fetchByQueryKey(API_LAB.TEMPLATE);
        },
      },
    });
  };

  const handleDeleteLabTemplate = () => {
    const templateId = getValues('templateId');
    deleteLabTemplate.request({
      url: `${API_LAB.TEMPLATE}/${templateId}`,
      options: {
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.DELETE_SUCCESSFULLY(t('Template')));
          fetchByQueryKey(API_LAB.TEMPLATE);
          setValue('templateId', null);
          setValue('labPanelsSave', {});
        },
      },
    });
  };

  const handleAddMorePanel = (panelItem) => {
    if (
      (panel?.data?.items || []).some((item) => item?.panelId === panelItem?.panelId) ||
      morePanels.some((item) => item?.panelId === panelItem?.panelId)
    ) {
      const message = t('Panel already added');
      toast.error(message, { toastId: message });
      return;
    }
    setMorePanels((preS) => [...preS, { ...panelItem, isMore: true }]);
    toast.success(TEXT_TEMPLATE.ADD_SUCCESSFULLY(t('Panel')));
  };

  const handleTemplateChange = async (template) => {
    const data = await detailLabTemplate.request({ url: `${API_LAB.TEMPLATE}/${template?.key}` });
    const objectData = {};
    (data?.data || []).forEach((element) => {
      objectData[element?.panelId] = { ...element, checked: true };
    });
    setValue('labPanelsSave', objectData);
  };

  const handleFindLabTest = (CompanyId, SearchTerm) => {
    if (SearchTerm !== undefined) {
      findLabTest.updateParams({ CompanyId, SearchTerm, Page: 0 });
    }
  };

  const handleResetForm = () => {
    reset();
    panel?.updateParams({ companyId: null });
    findLabTest.updateParams({ companyId: null });
    setMorePanels([]);
  };

  const handleFindLabSearch = () => {
    if (!labFacilityId) {
      const message = t('Please select facility');
      toast.error(message, { toastId: message });
      setValue('templateId', null);
      return;
    }
    handleFindLabTest(getValues('labFacilityIdSave'), getValues('findLabSearchTemp'));
  };

  const onSubmit = (values) => {
    const dataSubmit = removeUndefinedFromObject(
      getSubsetOfObject(
        [
          'completionStatus',
          'siteId',
          'providerId',
          'labFacilityId',
          'frequencyId',
          'specimenCollected',
          'fasting',
          'priority',
          'clinicalInfo',
        ],
        values,
      ),
    );

    values?.labPanelsSave && (dataSubmit.labPanels = transferDataPanels(values?.labPanelsSave));
    values?.diagnosis &&
      (dataSubmit.dxCodeIds = values.diagnosis
        .filter((item) => item?.checked)
        .map((item) => item.checked));
    dataSubmit.patientId = Number(searchParams?.patientId);
    dataSubmit.progressNoteLab = true;
    values?.date &&
      values?.time &&
      (dataSubmit.collectionTime = momentCloneDateTime(values?.date, values?.time).toDate());

    createLab.request({
      payload: Object.fromEntries(Object.entries(dataSubmit).filter(([_, v]) => v != null)),
      options: {
        onSuccess: () => {
          fetchByQueryKey(QUERY_KEY.LAB_HISTORY);
          toast.success(TEXT_TEMPLATE.ADD_SUCCESSFULLY(t('Lab')));
          hideModal();
        },
        onError: (e) => handleValidationBE({ setError }, e),
      },
    });
  };

  // distinct problem list by icD10Code property
  const problemListTransfer = useMemo(
    () => [...new Map((problemList?.data || []).map((item) => [item?.icD10Code, item])).values()],
    [problemList?.data],
  );

  const idSaveTemplateBtn = 'save-lab-order-template';

  return (
    <HookFormProvider {...methods}>
      <ModalLayout
        loading={
          inputData?.loading ||
          createLab?.loading ||
          createLabTemplate?.loading ||
          deleteLabTemplate?.loading
        }
        title={t('Lab Order Input')}
        onClose={hideModal}
        footerRightSide={
          <Stack horizontal tokens={{ childrenGap: 10 }}>
            <React.Fragment>
              <PrimaryButton data-isbutton="button" onClick={handleSubmit(onSubmit)}>
                {t('Submit')}
              </PrimaryButton>
              <CalloutConfirmation onOk={handleResetForm}>
                <DefaultButton text={t('Reset')} />
              </CalloutConfirmation>
            </React.Fragment>
            <DefaultButton data-isbutton="button" onClick={hideModal}>
              {t('Close')}
            </DefaultButton>
          </Stack>
        }
      >
        <Stack className={css.container} tokens={{ childrenGap: 16 }}>
          <Wrapper>
            <Container>
              <Row className={cn(css.row, css.cmClass.flexWrap)}>
                <Col sm={6} md={4} lg={3}>
                  <FieldSelectorPrimary
                    title={t('Order Site')}
                    name="siteId"
                    required
                    options={formatToFluentOptions(providers)}
                    showClear={false}
                  />
                </Col>
                <Col sm={6} md={4} lg={3}>
                  <FieldSelectorPrimary
                    title={t('Order Provider')}
                    name="providerId"
                    required
                    options={formatToFluentOptions(inputData?.data?.psychiatrists)}
                    showClear={false}
                  />
                </Col>
                <Col sm={6} md={4} lg={3}>
                  <FieldSelectorPrimary
                    title={t('Lab Facility')}
                    name="labFacilityId"
                    required
                    showClear={false}
                    options={formatToFluentOptions(
                      inputData?.data?.facilities,
                      'labName',
                      'locationId',
                    )}
                    onChange={({ data }) => {
                      setValue('labFacilityId', data?.locationId);
                      setValue('labFacilityIdSave', data?.companyId);
                      setValue('labPanelsSave', {});
                      setValue('templateId', null);
                      panel?.updateParams({
                        CompanyId: data?.companyId,
                        PageSize: 10000,
                      });
                    }}
                  />
                </Col>
              </Row>
            </Container>
          </Wrapper>
          <Wrapper title={t('Diagnosis')}>
            <Stack tokens={{ childrenGap: 8 }} className={css.diagnosisContainer}>
              {problemListTransfer.map((item, index) => (
                <Stack key={item?.problemListId} tokens={{ childrenGap: 8 }}>
                  <React.Fragment>
                    <FieldCheckbox
                      label={item?.code10Desc}
                      name={`diagnosis[${index}].checked`}
                      disabled={!providerId || !labFacilityId}
                      onChange={({ checked }) => {
                        setValue(`diagnosis[${index}].checked`, checked ? item?.icD10Code : false);
                      }}
                    />

                    <TableDiagnosisPanel
                      onAddToPanel={handleAddMorePanel}
                      icD10Code={item?.icD10Code}
                      providerId={providerId}
                      name={`diagnosis[${index}].checked`}
                    />
                  </React.Fragment>
                </Stack>
              ))}
            </Stack>
          </Wrapper>
          <Wrapper>
            <Container>
              <Row className={css.cmClass.flexWrap}>
                <Col sm={6} md={4} lg={3}>
                  <FieldDropdown
                    options={formatToFluentOptions(labTemplate?.data || [])}
                    title={t('Lab Order Template:')}
                    name="templateId"
                    disabled={!labFacilityId}
                    onChange={({ data }) => {
                      setValue('templateId', data?.key);
                      setValue('templateName', data?.text);
                      handleTemplateChange(data);
                    }}
                  />
                </Col>
                <Col sm={6} md={6} lg={6}>
                  <Stack
                    horizontal
                    className="h-100"
                    verticalAlign="end"
                    tokens={{ childrenGap: 16 }}
                  >
                    <PrimaryButton
                      id={idSaveTemplateBtn}
                      data-isbutton="button"
                      onClick={
                        !templateId
                          ? toggleIsCalloutSaveTemplateVisible
                          : () => handleCreateLabTemplate()
                      }
                    >
                      {t('Save Template')}
                    </PrimaryButton>
                    {isCalloutSaveTemplateVisible && (
                      <ConfirmCallout
                        buttonId={idSaveTemplateBtn}
                        toggle={toggleIsCalloutSaveTemplateVisible}
                        text={t('Save Template')}
                        description={<FieldText title={t('Template name')} name="templateName" />}
                        onOk={() => handleCreateLabTemplate()}
                      />
                    )}
                    <CalloutConfirmation
                      description={t('Are you sure you want to delete this template?')}
                      onOk={() => handleDeleteLabTemplate()}
                    >
                      <DefaultButton disabled={!templateId}>{t('Delete Template')}</DefaultButton>
                    </CalloutConfirmation>
                  </Stack>
                </Col>
              </Row>
            </Container>
          </Wrapper>
          <Wrapper title={t('Lab Info')}>
            <Stack tokens={{ childrenGap: 16 }} className={css.diagnosisContainer}>
              <LabInfoTable
                // templateId={field?.value}
                initLabPanels={initialValue?.labPanels}
                morePanels={morePanels}
                panel={panel}
                detailLabTemplate={detailLabTemplate}
              />
              <HorizontalRule mt={12} mb={12} />
              <Stack horizontal tokens={{ childrenGap: 12 }}>
                <FieldText name="findLabSearchTemp" />

                <LoadingWrapper loading={findLabTest.loading}>
                  <PrimaryButton data-isbutton="button" onClick={handleFindLabSearch}>
                    {t('Find Lab Test')}
                  </PrimaryButton>
                </LoadingWrapper>
              </Stack>
              {(findLabTest?.data?.items || []).length > 0 && (
                <React.Fragment>
                  <Table
                    loading={findLabTest?.loading}
                    columns={columnFindLabTest(handleAddMorePanel)}
                    items={findLabTest?.data?.items || []}
                    totalItems={findLabTest.data?.totalItems}
                    onMetadataChange={(data) => {
                      findLabTest.updateParams(data);
                    }}
                    metadata={findLabTest.params}
                  />
                  <HorizontalRule mb={12} />
                </React.Fragment>
              )}
              <Container>
                <Row className={css.cmClass.flexWrap}>
                  <Col sm={6} md={4} lg={3}>
                    <FieldDropdown
                      options={formatToFluentOptions(inputData?.data?.specimenCollected)}
                      name="specimenCollected"
                      title={t('Specimen Collected')}
                    />
                  </Col>
                  <Col sm={6} md={4} lg={3}>
                    <FieldDate name="date" title={t('Collection Date')} />
                  </Col>
                  <Col sm={6} md={4} lg={3}>
                    <FieldTime name="time" title="Collection Time" />
                  </Col>
                  <Col sm={6} md={4} lg={3}>
                    <FieldDropdown
                      options={formatToFluentOptions(inputData?.data?.fasting)}
                      name="fasting"
                      title={t('Fasting')}
                    />
                  </Col>
                  <Col sm={6} md={4} lg={3}>
                    <FieldDropdown
                      options={formatToFluentOptions(inputData?.data?.priorities)}
                      name="priority"
                      title={t('Priority')}
                    />
                  </Col>
                  <Col sm={6} md={4} lg={3}>
                    <FieldDropdown
                      options={formatToFluentOptions(inputData?.data?.frequencies || [])}
                      name="frequencyId"
                      title={t('Frequency')}
                      required
                    />
                  </Col>
                  <Col sm={6} md={4} lg={3}>
                    <FieldDropdown
                      options={formatToFluentOptions(inputData?.data?.frequencyTimes || [])}
                      name="frequencyTimeId"
                      title={t('For x number of times')}
                    />
                  </Col>
                  <Col sm={12} md={12} lg={6}>
                    <FieldText name="clinicalInfo" title={t('Clinical Info')} />
                  </Col>
                </Row>
              </Container>
            </Stack>
          </Wrapper>
          <Stack tokens={{ childrenGap: 10 }}>
            <Container>
              <Row>
                <Col sm={12} md={3} lg={3}>
                  <FieldDropdown
                    title={t('Completion Status:')}
                    name="completionStatus"
                    options={[
                      { text: t('Signed and complete'), key: '1' },
                      { text: t('Save as incomplete'), key: '0' },
                    ]}
                  />
                </Col>
              </Row>
            </Container>
          </Stack>
        </Stack>
      </ModalLayout>
    </HookFormProvider>
  );
};

const TestName = ({ panelName, isMore }) => {
  const css = useClass(testNameStyles);
  return <div className={cn('cell_style', isMore && css.more)}>{panelName}</div>;
};

const columnFindLabTest = (handleAddMorePanel) => [
  {
    name: 'Lab Panel Name',
    fieldName: 'panelName',
  },
  {
    name: 'CPT Code',
    fieldName: 'alternateNumber',
  },
  {
    name: 'Lab Code',
    fieldName: 'panelNumber',
  },
  {
    name: '',
    fieldName: 'button',
    minWidth: 200,
    renderItem: (item) => (
      <DefaultButton data-isbutton="button" onClick={() => handleAddMorePanel(item)}>
        {t('Add to Panel')}
      </DefaultButton>
    ),
  },
];

const LabInfoTable = ({ initLabPanels = [], morePanels, panel, detailLabTemplate }) => {
  const labPanelsSave = useWatch({ name: 'labPanelsSave' });

  const { setValue } = useFormContext();

  const labInfoPanelItems = useMemo(() => {
    let lab = initLabPanels.concat(panel?.data?.items || []).concat(morePanels);
    lab = lab.concat(
      (detailLabTemplate?.data?.length ? detailLabTemplate?.data || [] : [])
        .map((item) => ({ ...item, isMore: true }))
        .filter((item) => !lab.some((panel) => panel?.panelId === item?.panelId)),
    );

    return lab;
  }, [initLabPanels?.length, morePanels?.length, panel?.data, detailLabTemplate?.data]);

  return (
    <Table
      loading={panel?.loading || detailLabTemplate?.loading}
      columns={_columns(labPanelsSave, setValue)}
      items={labInfoPanelItems}
      pagination={false}
    />
  );
};

const _columns = (labPanelsSave, setValue) => [
  {
    name: '',
    fieldName: 'checkbox',
    maxWidth: 22,
    renderItem: (item) => (
      <FieldCheckbox
        name={`labPanelsSave.${item?.panelId}.checked`}
        onChange={({ checked }) => {
          setValue(`labPanelsSave.${item?.panelId}`, {
            checked: checked,
            panelId: checked && item?.panelId,
            panelName: checked && item?.panelName,
          });
        }}
      />
    ),
  },
  { name: 'Test', fieldName: 'panelName', renderItem: (item) => <TestName {...item} /> },
  {
    name: 'Note',
    fieldName: 'note',
    checked: JSON.stringify(labPanelsSave),
    renderItem: (item) => (
      <FieldText
        disabled={!labPanelsSave?.[item?.panelId]?.checked}
        name={`labPanelsSave.${item?.panelId}.note`}
      />
    ),
  },
];

const testNameStyles = (theme) => ({
  more: {
    color: theme.custom.blue6,
  },
});

const styles = (theme) => ({
  diagnosisContainer: {
    marginTop: 16,
  },
  title: {
    marginBottom: 10,
  },
  row: {
    paddingBottom: 8,
  },
  container: {
    padding: 16,
  },
  wrapper: {
    padding: 16,
    border: `1px solid ${theme.custom.grey300}`,
  },
});

export default LabOrderInput;
