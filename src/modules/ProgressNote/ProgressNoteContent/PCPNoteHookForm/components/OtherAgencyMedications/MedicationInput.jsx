import { Default<PERSON><PERSON>on, PrimaryButton, SearchBox } from '@fluentui/react';
import FieldDate from 'components/HookForm/FieldDate';
import FieldText from 'components/HookForm/FieldText';
import FieldDropdown from 'components/HookForm/FieldDropdown';
import { useClass, useModal, useNavigator, useRequest } from 'hooks';
import { useForm } from 'react-hook-form';
import * as Yup from 'yup';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import React, { useEffect, useState } from 'react';
import FieldLayout from 'components/Form/FieldLayout';
import { API_METHOD, API_PROGRESS_NOTE } from 'constants/urlRequest';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import Table from 'components/Table';
import { formatToFluentOptions } from 'utils';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { toast } from 'react-toastify';
import QUERY_KEY from 'constants/queryKey';
import { fetchByQueryKey } from 'apis/queryClient';
import { Col, Container, Row } from 'components/Layout/Grid';
import { t } from 'utils/string';
import { handleValidationBE } from '../../PCPNoteInput/form';
import HookFormProvider from 'components/HookForm/HookFormProvider';

const MedicationInput = ({ medication = {} }) => {
  const { hideModal } = useModal();
  const isEdit = medication.pcpDrugId;
  const [searchText, setSearchText] = useState('');
  const [showSearchValue, setShowSearchValue] = useState(false);
  const { searchParams } = useNavigator();
  const css = useClass(styles);
  const [prescriptionOptions, setPrescriptionsOptions] = useState([]);
  const formatData = (data) => ({
    ...data,
    effectiveDate: data.effectiveDate ? new Date(data.effectiveDate) : null,
    expirationDate: data.expirationDate ? new Date(data.expirationDate) : null,
  });
  const methods = useForm({
    defaultValues: {
      patientId: searchParams.patientId,
      ...DEFAULT_VALUE,
      ...formatData(medication),
    },
    mode: 'onChange',
    validationSchema: SCHEMA,
  });

  const apiSearch = useRequest({
    url: API_PROGRESS_NOTE.PRESCRIPTIONS_SEARCH,
    params: { ...DEFAULT_PAGINATION_PARAMS, SearchTerm: searchText },
    requiredParams: { PatientId: searchParams.patientId },
    autoRefetch: true,
  });

  const apiMutateMedication = useRequest({
    method: isEdit ? API_METHOD.PUT : API_METHOD.POST,
    url: `${API_PROGRESS_NOTE.MEDICATIONS}/${medication?.pcpDrugId || ''}`,
    enabled: false,
  });

  const apiProvider = useRequest({
    url: API_PROGRESS_NOTE.PRESCRIPTIONS_PROVIDER_LOOKUP,
    requiredParams: { PatientId: searchParams.patientId },
    autoRefetch: true,
  });

  const apiPrescription = useRequest({
    url: API_PROGRESS_NOTE.PRESCRIPTIONS_LOOKUP,
    requiredParams: { PatientId: searchParams.patientId },
    autoRefetch: true,
  });

  useEffect(() => {
    setPrescriptionsOptions(formatToFluentOptions(apiPrescription.data));
  }, [apiPrescription.data]);

  const handleSearch = () => {
    apiSearch.updateParams({ SearchTerm: searchText });
    setShowSearchValue(true);
  };

  const onSubmit = async (formData) => {
    try {
      await apiMutateMedication.mutateAsync(formData);
      toast.success(
        isEdit
          ? t('You have updated medication successfully')
          : t('You have created medication successfully'),
      );
      fetchByQueryKey(QUERY_KEY.OTHER_AGENCY_MEDICATION);
      fetchByQueryKey(QUERY_KEY.PSYCH_NOTE_OTHER_AGENCY_MEDICATION);
      hideModal();
    } catch (error) {
      handleValidationBE(methods, error);
    }
  };

  const handleSelectSearchItem = (item) => {
    if (!prescriptionOptions.find((prescription) => prescription.key === item.drugId)) {
      setPrescriptionsOptions((v) => [...v, { key: item.drugId, text: item.drugName }]);
    }
    methods.setValue('drugId', item.drugId);
    setShowSearchValue(false);
  };

  return (
    <ModalLayout title={t('Medication Input')}>
      <HookFormProvider {...methods}>
        <div className={css.root}>
          <Container>
            <Row className={css.cmClass.flexWrap}>
              <Col md={4}>
                <FieldDropdown
                  name="providerId"
                  isFast={false}
                  formatData={(v) => v.key}
                  title={t('Provider')}
                  options={formatToFluentOptions(apiProvider.data || [])}
                  placeholder={t('Please select an option')}
                />
              </Col>
              <Col md={4}>
                <FieldDropdown
                  name="drugId"
                  required
                  formatData={(v) => v.key}
                  isFast={false}
                  options={prescriptionOptions}
                  title={t('Prescription')}
                  placeholder={t('Please select an option')}
                />
              </Col>
              <Col md={4}>
                <div className={css.search}>
                  <FieldLayout title={t('Prescription Search')}>
                    <SearchBox value={searchText} onChange={(_, v) => setSearchText(v)} />
                  </FieldLayout>
                  <DefaultButton data-isbutton="button" onClick={handleSearch}>
                    {t('Find')}
                  </DefaultButton>
                </div>
              </Col>
              {showSearchValue && (
                <Col md={12}>
                  <Table
                    columns={COLUMNS}
                    metadata={apiSearch.params}
                    onMetadataChange={apiSearch.updateParams}
                    loading={apiSearch.loading}
                    totalItems={apiSearch.data?.totalItems}
                    items={apiSearch.data?.items}
                    onActiveItemChanged={handleSelectSearchItem}
                  />
                </Col>
              )}
              <Col md={4}>
                <FieldDate name="effectiveDate" title={t('Effective Date')} />
              </Col>
              <Col md={4}>
                <FieldDate name="expirationDate" title={t('Expiration Date')} />
              </Col>
              <Col md={4}>
                <FieldText name="facility" title={t('Facility')} />
              </Col>
              <Col md={4}>
                <FieldText name="dosage" title={t('Dosage/Strength')} />
              </Col>
              <Col md={4}>
                <FieldText name="num" title={t('Count/Number')} type="number" />
              </Col>
              <Col md={4}>
                <FieldText name="refill" title={t('Refill')} />
              </Col>
              <Col md={4}>
                <FieldText name="directions" title={t('Directions')} />
              </Col>
              <Col md={4}>
                <FieldText name="purpose" title={t('Purpose/Target Symptom(s)')} />
              </Col>
              <Col md={4}>
                <FieldText name="notes" title={t('Comments')} />
              </Col>
            </Row>
          </Container>

          <div className={css.btnActions}>
            <LoadingWrapper loading={apiMutateMedication.loading}>
              <PrimaryButton
                type="submit"
                data-isbutton="button"
                onClick={methods.handleSubmit(onSubmit)}
              >
                {t('Submit')}
              </PrimaryButton>
            </LoadingWrapper>
            <CalloutConfirmation onOk={() => methods.reset()}>
              <DefaultButton text={t('Reset')} />
            </CalloutConfirmation>
            <DefaultButton data-isbutton="button" onClick={hideModal}>
              {t('Close')}
            </DefaultButton>
          </div>
        </div>
      </HookFormProvider>
    </ModalLayout>
  );
};

const styles = () => {
  return {
    root: {
      padding: 14,
    },
    search: {
      display: 'flex',
      alignItems: 'flex-end',
      gap: 16,
      '& > div': {
        flex: 1,
      },
    },
    btnActions: {
      marginTop: 30,
      display: 'flex',
      justifyContent: 'flex-end',
      gap: 10,
    },
  };
};

export default MedicationInput;

const DEFAULT_VALUE = {
  pcpDrugId: null,
  providerId: null,
  facility: '',
  drugId: undefined,
  dosage: '',
  num: null,
  directions: '',
  purpose: '',
  notes: '',
  effectiveDate: new Date(),
  expirationDate: null,
  refill: 0,
};

const COLUMNS = [
  { name: 'Medication Name', fieldName: 'drugName' },
  { name: 'Generic', fieldName: 'isGeneric' },
  { name: 'Generic of', fieldName: '' },
];

const SCHEMA = Yup.object().shape({
  drugId: Yup.number().required().label(t('Prescription')),
});
