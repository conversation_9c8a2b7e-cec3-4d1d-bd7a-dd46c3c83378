import React, { useEffect } from 'react';
import { useClass, useModal, useRequest } from 'hooks';
import { useForm } from 'react-hook-form';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import { useBoolean } from '@fluentui/react-hooks';
import ConfirmCallout from 'components/Modal/ConfirmCallout';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import { API_CARD_VIEW_PROBLEM_LIST, API_PROBLEM, API_METHOD } from 'constants/urlRequest';
import ProblemList from './ProblemList';
import FieldDropDown from 'components/HookForm/FieldDropdown';
import { Container, Col, Row } from 'components/Layout/Grid';
import { formatToFluentOptions } from 'utils';
import FieldDate from 'components/HookForm/FieldDate';
import FieldCheckBox from 'components/HookForm/FieldCheckbox';
import { toast } from 'react-toastify';
import { t } from 'utils/string';
import cn from 'classnames';
import { mergeListProblemBaseAndEditActivePB } from '../useInitProblemAndPlan';
import HookFormProvider from 'components/HookForm/HookFormProvider';

const DATA_KEY_LIST_PROBLEM = 'listProblemSave';
const defaultValues = {
  status: 'Active',
  startDate: new Date(),
};

function getRemainingPrecedence(array, length) {
  // Extract the precedence values from the array
  const precedenceValues = new Set(array.map((item) => item.precedence));

  // Generate numbers starting from 1, skipping those in precedenceValues
  const result = [];
  let candidate = 1;

  while (result.length < length) {
    if (!precedenceValues.has(candidate)) {
      result.push(candidate);
    }
    candidate++;
  }

  return result;
}

function expandArray(arr, number) {
  const newArr = [...arr];
  if (!newArr.length) return []; // Return empty array if input is empty
  const start = newArr[newArr.length - 1] + 1; // Start from the next number after the last element
  for (let i = 0; i < number; i++) {
    newArr.push(start + i); // Add sequential numbers
  }
  return newArr;
}

const AddAssessmentFormModal = ({
  defaultItems = [],
  handleChangeObjectProblems,
  problemURL = API_PROBLEM.ACTIVE_PROBLEMS_BY_PATIENT,
  arrayProblems,
  objectProblems,
  activeProblemsFromEdit,
  handleChangeArrayProblems,
  arrayActiveProblems,
  isAutoPrecedence,
  patientId,
}) => {
  const { hideModal } = useModal();
  const css = useClass(styles);
  const [isCalloutVisible, { toggle: toggleIsCalloutVisible }] = useBoolean(false);

  const apiPrecedences = useRequest({
    key: [API_CARD_VIEW_PROBLEM_LIST.PRECEDENCES, patientId, 'precedence-lookup-modal'],
    url: API_CARD_VIEW_PROBLEM_LIST.PRECEDENCES,
    requiredParams: {
      patientId,
    },
    autoRefetch: true,
  });

  const snomed = useRequest({
    key: [API_PROBLEM.DIAGNOSTIC_CODE, patientId, 'search-for-add-assessment'],
    url: API_PROBLEM.DIAGNOSTIC_CODE,
    params: {
      Page: 0,
      PageSize: 1000,
      SearchTerm: '',
    },
    requiredParams: { patientId },
    enabled: false,
    autoRefetch: true,
    cacheTime: 0,
  });

  const apiCreateProblems = useRequest({
    method: API_METHOD.POST,
    url: API_PROBLEM.DEFAULT,
    enabled: false,
  });

  const problemListRequest = useRequest({
    enabled: false,
  });

  const status = useRequest({
    key: ['status-lookup-modal-add-assessment'],
    url: API_PROBLEM.STATUS_LOOKUP,
  });

  const defaultValuesWithOptions = {
    ...defaultValues,
    listProblemSave: defaultItems?.reduce((acc, cur) => {
      acc[`key${cur?.diagnosticCodeId}`] = { ...cur, checked: true };
      return acc;
    }, {}),
  };

  const methods = useForm({
    defaultValues: defaultValuesWithOptions,
  });

  // Set statusId when status data is available
  useEffect(() => {
    if (status?.data?.[0]?.key) {
      methods.setValue('statusId', status.data[0].key);
    }
  }, [status?.data, methods]);

  const handleResetForm = () => {
    methods.reset(defaultValuesWithOptions);
    if (status?.data?.[0]?.key) {
      methods.setValue('statusId', status.data[0].key);
    }
  };

  const handleResetFieldChoose = () => methods.setValue(DATA_KEY_LIST_PROBLEM, {});

  const handleFindProblemList = (searchProblemList) => {
    handleResetFieldChoose();
    snomed.updateParams({ SearchTerm: searchProblemList });
  };

  // we have 2 place save precedence
  // * for list problem of patient (save directly by api)
  // * for list problem in note (save by change array active problems)
  const handleSubmitForm = async (values) => {
    const dataSubmit = {};
    const listPrecedenceForCanUseForPatient = expandArray(
      apiPrecedences?.data || [],
      Object.keys(values?.listProblemSave || {})?.length || 0,
    );

    //transfer from list problem obj to arr and add some fields
    dataSubmit.problemsList = Object.keys(values?.listProblemSave || {})
      .map((item) => {
        const relevantProblemItem = values?.listProblemSave[item];

        return {
          ...relevantProblemItem,
          statusId: values?.statusId,
          startDate: values?.startDate,
          endDate: values?.endDate || null,
          // chrnoic (condition) alway equal to 1 from v1
          chronic: 1,
          patientId,
          ...(relevantProblemItem?.behavioral === 1
            ? { precedence: listPrecedenceForCanUseForPatient?.shift() }
            : {}),
        };
      })
      .filter((item) => item?.checked);

    if (
      // check if problems exist
      dataSubmit.problemsList.some((problem) =>
        arrayProblems?.find((item) => item?.icD10Code === problem?.diagnosticCodeId),
      )
    ) {
      toast.error(t('The problem has already exist'));
      return;
    }
    dataSubmit.patientId = patientId.toString();
    dataSubmit.saveToFavoritesList = values?.saveToFavoritesList || false;
    // ignorePrecedence alway true from v1
    dataSubmit.ignorePrecedence = true;

    await apiCreateProblems.request({ payload: dataSubmit });
    // save plan and assessment fields to parent form if status = active
    if (values?.statusId === status.data[0].key) {
      const { data: newProblemList } = await problemListRequest.request({
        url: `${problemURL}?patientId=${patientId}&distinct=true`,
      });
      const precedenceCanUseForNote = getRemainingPrecedence(
        arrayActiveProblems || [],
        dataSubmit?.problemsList?.length || 0,
      );
      newProblemList.forEach((item) => {
        //match item from fetch with item additional data from modal
        const additionalData = (dataSubmit?.problemsList || []).find(
          (additionalData) => additionalData?.diagnosticCodeId === item?.icD10Code,
        );
        item.precedence = null;
        if (additionalData) {
          item.txt = additionalData.plan;
          item.assessment = additionalData.assessment;
          //auto set precedence
          isAutoPrecedence && (item.precedence = precedenceCanUseForNote.shift());
        }
      });

      // Transform new problem list array into an object with custom keys
      const result = newProblemList.reduce((acc, item) => {
        const key = `key${item.icD10Code}`;
        acc[key] = item;
        return acc;
      }, {});

      const newObjectProblems = { ...result, ...objectProblems };
      handleChangeObjectProblems(newObjectProblems);
      const newArrayProblems = mergeListProblemBaseAndEditActivePB(
        activeProblemsFromEdit || [],
        newProblemList,
      );
      handleChangeArrayProblems(newArrayProblems);
    }

    toast.success(t('Create problem successfully'));
    hideModal();
  };

  const idResetBtn = 'reset-add-assessment-form';

  return (
    <HookFormProvider {...methods}>
      <ModalLayout
        title={t('Problems List Favorites')}
        onClose={hideModal}
        loading={apiCreateProblems?.loading}
        footerRightSide={
          <React.Fragment>
            <Stack horizontal tokens={{ childrenGap: 10 }}>
              <PrimaryButton
                data-isbutton="button"
                onClick={methods.handleSubmit(handleSubmitForm)}
              >
                Submit
              </PrimaryButton>
              <DefaultButton
                id={idResetBtn}
                data-isbutton="button"
                onClick={toggleIsCalloutVisible}
              >
                {t('Reset')}
              </DefaultButton>
              <DefaultButton data-isbutton="button" onClick={hideModal}>
                {t('Close')}
              </DefaultButton>
            </Stack>
            {isCalloutVisible && (
              <ConfirmCallout
                buttonId={idResetBtn}
                toggle={toggleIsCalloutVisible}
                text={t('Confirmation')}
                description={t('Are you sure you want to reset form?')}
                onConfirm={handleResetForm}
              />
            )}
          </React.Fragment>
        }
      >
        <ProblemList
          dataKey={DATA_KEY_LIST_PROBLEM}
          loading={snomed?.loading || status?.loading}
          problemListSearch={snomed?.data?.items}
          handleFindProblemList={handleFindProblemList}
          defaultItems={defaultItems}
        />

        <Stack className={cn(css.cmClass.borderColor, css.footerWrapper)}>
          <Container>
            <Row>
              <Col md={3}>
                <FieldDropDown
                  title={t('Status')}
                  name="statusId"
                  options={formatToFluentOptions(status?.data)}
                  isFast={false}
                />
              </Col>
              <Col md={4}>
                <FieldDate title={t('Start Date')} name="startDate" />
              </Col>
              <Col md={4}>
                <FieldDate title={t('End Date')} name="endDate" />
              </Col>
            </Row>
            <Row>
              <Col md={3}>
                <FieldCheckBox label={t('Add to Favorites')} name="saveToFavoritesList" />
              </Col>
            </Row>
          </Container>
        </Stack>
      </ModalLayout>
    </HookFormProvider>
  );
};

const styles = (theme) => ({
  footerWrapper: {
    backgroundColor: theme.semanticColors.bodyBackground,
    position: 'sticky',
    bottom: 0,
    padding: 16,
    borderTop: '1px solid',
  },
});

export default AddAssessmentFormModal;
