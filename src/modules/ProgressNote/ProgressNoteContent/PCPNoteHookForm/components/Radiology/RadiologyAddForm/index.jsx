import React, { useContext, useEffect, useState } from 'react';
import { Stack, PrimaryButton, DefaultButton } from '@fluentui/react';
import OrderInformation from '../OrderInformation';
import { useForm } from 'react-hook-form';
import SubCollapse from 'components/Collapse/SubCollapse';
import { useModal, useRequest, useNavigator } from 'hooks';
import RadiologyInput from '../RadiologyInput';
import { API_RADIOLOGY, API_METHOD } from 'constants/urlRequest';
import { removeUndefinedFromObject, asyncComponentToBase64Image } from 'utils';
import QUERY_KEY from 'constants/queryKey';
import { fetchByQueryKey } from 'apis/queryClient';
import { toast } from 'react-toastify';
import { t } from 'utils/string';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import { TEXT_TEMPLATE } from 'constants/texts';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import RadiologyDiagnosis from '../RadiologyDiagnosis';
import { FORM_LOCAL_DATA } from 'constants/index';
import isString from 'lodash/isString';
import { handleValidationBE } from 'utils/form';
import RadiologyTemplate from './Template';
import { RadiologyContext } from 'contexts/RadiologyContext';
import { MODULE_NAME } from 'constants/routes';
import HookFormProvider from 'components/HookForm/HookFormProvider';

export const initialValuesForm = {
  orderDate: null,
  providerId: null,
  fasting: null,
  priority: null,
  time: null,
  numberOfTime: null,
  frequencyId: null,
  cpoe: null,
  facilityId: null,
  statusId: null,
  pendForId: null,
  statusComments: null,
  ordersSave: {},
};

const RadiologyAddForm = ({ initialValues = initialValuesForm }) => {
  const { hideModal } = useModal();
  const { searchParams, navigate } = useNavigator();

  const [loadingCapture, setLoadingCapture] = useState(false);
  const { setEditItem } = useContext(RadiologyContext);
  const [listDotBreast, setListDotBreast] = useState([]);

  const methods = useForm({
    defaultValues: initialValues,
  });

  const { handleSubmit, reset, setValue, watch } = methods;

  const onHandleReset = () => {
    setEditItem?.();
    reset();
    setListDotBreast([]);
  };

  useEffect(() => {
    if (initialValues.commonOrderId) {
      request({ url: API_RADIOLOGY.GET_DETAIL(initialValues.commonOrderId) });
    }
  }, [initialValues.commonOrderId]);

  const { request, loading } = useRequest({
    enabled: false,
    onSuccess: (value) => {
      if (value) {
        const temp = { ...value };
        const dxCodes = {};
        const _localData = {};
        const ordersSave = {};
        for (const dxCode of temp.dxCodes || []) {
          dxCodes[`diagnosis${dxCode.key}`] = true;
        }
        for (const value of temp.values || []) {
          _localData[`question${value.questionId}answer${value.answerId}`] = value.value;
        }
        for (const order of temp.orders || []) {
          ordersSave[order.testId] = { checked: true, testId: order.testId, note: order.note };
        }
        temp.diagnosis = dxCodes;
        temp._localData = _localData;
        temp.ordersSave = ordersSave;
        temp.numberOfTime = temp.numberOfTime || '';
        setValue(temp);
      }
    },
  });

  const radiologyInputData = useRequest({
    url: API_RADIOLOGY.INPUT_DATA,
    requiredParams: { patientId: searchParams.patientId },
  });

  const radiologySave = useRequest({
    url: API_RADIOLOGY.DEFAULT,
    method: API_METHOD.POST,
    key: API_RADIOLOGY.DEFAULT + 'Save',
  });

  const onSubmit = async (values) => {
    const dataSubmit = removeUndefinedFromObject(values);
    // radiology-input-checkbox
    dataSubmit.orders = Object.values(values.ordersSave || {}).filter((v) => v?.checked);
    // radiology-input-breast-section
    dataSubmit.patientId = searchParams?.patientId;
    setLoadingCapture(true);
    const imageValue = await asyncComponentToBase64Image(values[FORM_LOCAL_DATA]?.image?.imageRef);
    if (imageValue) dataSubmit.image = { ...(values.image || {}), imageValue };
    setLoadingCapture(false);
    // radiology-input-question
    dataSubmit.values = Object.entries(values?.[FORM_LOCAL_DATA] || {})
      .filter(([key]) => key !== 'image')
      .map(([key, value]) => {
        if (isString(value) || value instanceof Date) {
          const [questionId, answerId] = (key || '').replace('question', '').split('answer');
          return { value, questionId, answerId };
        }
        const [ques, answerId] = key.split('answer');
        const questionId = ques?.replace('question', '');
        return { value, questionId, answerId };
      });
    //radiology-diagnosis
    dataSubmit.dxCodes = Object.entries(values?.diagnosis || {})
      .filter(([_, v]) => !!v)
      .map(([k]) => k?.replace('diagnosis', ''));
    dataSubmit.numberOfTime = values.numberOfTime || null;

    delete dataSubmit[FORM_LOCAL_DATA];
    delete dataSubmit.diagnosis;
    delete dataSubmit.ordersSave;

    radiologySave.request({
      payload: dataSubmit,
      options: {
        onError: (err) => handleValidationBE(methods, err),
        onSuccess: () => {
          fetchByQueryKey(QUERY_KEY.RADIOLOGY_HISTORY);
          toast.success(TEXT_TEMPLATE.ADD_SUCCESSFULLY(t('Radiology Order')));
          navigate({ hash: MODULE_NAME.RADIOLOGY_HISTORY });
          hideModal();
          onHandleReset();
        },
      },
    });
  };

  const imageValue = watch('image.imageValue');

  return (
    <HookFormProvider {...methods}>
      <LoadingWrapper
        loading={radiologyInputData?.loading || radiologySave?.loading || loading || loadingCapture}
      >
        <Stack tokens={{ childrenGap: 16 }}>
          <SubCollapse title={t('Radiology Template')}>
            <RadiologyTemplate />
          </SubCollapse>
          <SubCollapse title={t('Order Information')} open>
            <OrderInformation inputData={radiologyInputData?.data} />
          </SubCollapse>
          <SubCollapse title={t('Radiology Diagnosis')} open>
            <RadiologyDiagnosis inputData={radiologyInputData?.data} />
          </SubCollapse>
          <SubCollapse title={t('Radiology Input')} open>
            <RadiologyInput
              imageConcern={imageValue}
              inputData={radiologyInputData?.data}
              listDotBreast={listDotBreast}
              setListDotBreast={setListDotBreast}
            />
          </SubCollapse>
          <Stack tokens={{ childrenGap: 10 }} horizontal>
            <PrimaryButton
              data-isbutton="button"
              type="submit"
              text={t('Submit')}
              onClick={handleSubmit(onSubmit)}
            />
            <CalloutConfirmation onOk={onHandleReset}>
              <DefaultButton text={t('Reset')} />
            </CalloutConfirmation>
          </Stack>
        </Stack>
      </LoadingWrapper>
    </HookFormProvider>
  );
};

export default RadiologyAddForm;
