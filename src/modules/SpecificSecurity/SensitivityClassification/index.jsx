import React, { useState, useMemo } from 'react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { t } from 'utils/string';
import { PrimaryButton, Toggle, Stack, Checkbox, Dropdown, TextField } from '@fluentui/react';
import RadioButton from 'components/RadioButton';
import Table from 'components/Table';
import { useRequest, useClass, useModal } from 'hooks';
import { API_METHOD, API_SPECIFIC_SECURITY } from 'constants/urlRequest';
import cn from 'classnames';
import { TEXT_TEMPLATE } from 'constants/texts';
import { MODAL_SIZE } from 'constants/modal';
import ViewClassificationModal from './ViewClassificationModal';
import { toast } from 'react-toastify';

const restrictionOptionsMap = {
  reporting: { text: 'Do not display in reporting' },
  hie: { text: 'Do not export to HIE (Health Information Exchange)' },
  ai: { text: 'Do not use in AI summaries or AI training' },
  dwh: { text: 'Do not export to any data warehouse' },
};

const classificationNameMap = {
  abortion: {
    text: t('Abortion'),
    description: t('Used in instances where abortion or miscarriage is mentioned'),
  },
  sud: {
    text: t('SUD'),
    description: t('Used in instances where substance use is mentioned'),
  },
  contraceptive: {
    text: t('Contraceptive Care'),
    description: t('Used in instances where contraceptive use is mentioned'),
  },
  genderAffirming: {
    text: t('Gender Affirming Care'),
    description: t('Used in instances where sexual orientation/gender identity is mentioned'),
  },
  alcohol: {
    text: t('Alcohol'),
    description: t('Used in instances where alcohol use is mentioned'),
  },
  hivAids: {
    text: t('HIV/AIDS'),
    description: t('Used in instances where HIV/AIDS is mentioned'),
  },
  childAbuse: {
    text: t('Child Abuse'),
    description: t('Used in instances where child abuse is reported'),
  },
};

const radioOptions = [
  {
    value: 'single',
    label: t('Single (Recommended)'),
    helperText: t(
      'Sensitivity classification will display as a checkbox throughout Axiom, on certain client data records',
    ),
  },
  {
    value: 'multiple',
    label: t('Multiple'),
    helperText: t(
      'Sensitive classifications will display as a multi-select dropdown on certain data records and may have different behavior based on the type of data',
    ),
  },
];

const SensitivityClassification = () => {
  const [isSensitive, setIsSensitive] = useState(false);
  const [classificationType, setClassificationType] = useState();
  const [classificationName, setClassificationName] = useState();
  const [description, setDescription] = useState();
  const [selectedSensitivityOptions, setSelectedSensitivityOptions] = useState([]);
  const [dropdownError, setDropdownError] = useState('');
  const [descriptionError, setDescriptionError] = useState('');
  const { showModal } = useModal();
  const css = useClass();

  const apiSave = useRequest({
    url: API_SPECIFIC_SECURITY.SAVE_SENSITIVITY_CLASSIFICATION,
    method: API_METHOD.POST,
    onSuccess: () => {
      toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('Classification')));
    },
  });

  const apiDelete = useRequest({
    method: API_METHOD.DELETE,
  });

  const apiInfo = useRequest({
    url: API_SPECIFIC_SECURITY.GET_SENSITIVITY_CLASSIFICATION,
    method: API_METHOD.GET,
    params: { classificationType },
    onSuccess: (data) => {
      setIsSensitive(data.isSensitive ?? false);
      setClassificationType(data.classificationType ?? 'single');
      if (
        data.classifications &&
        data.classifications.length > 0 &&
        data.classificationType === 'single'
      ) {
        setSelectedSensitivityOptions(data.classifications[0].selectedSensitivityOptions ?? []);
      }
    },
  });

  const handleSave = () => {
    let valid = true;
    if (!classificationName) {
      setDropdownError(t('Classification Name is required'));
      valid = false;
    } else {
      setDropdownError('');
    }
    if (!description) {
      setDescriptionError(t('Description is required'));
      valid = false;
    } else {
      setDescriptionError('');
    }
    if (!valid) return;

    apiSave.request({
      payload: {
        isSensitive,
        classificationType,
        classifications: [
          {
            classificationName,
            description,
            selectedSensitivityOptions,
          },
        ],
      },
      options: {
        onError: (err) => {
          // TODO: handle error
        },
      },
    });
  };

  const handleClassificationNameChange = (_, option) => {
    setClassificationName(option?.key);
    setDescription(classificationNameMap[option?.key]?.description || '');
  };

  const sensitivityClassificationOptions = useMemo(
    () =>
      Object.entries(classificationNameMap).map(([key, value]) => ({
        key,
        text: value.text,
      })),
    [],
  );

  const restrictionOptions = Object.entries(restrictionOptionsMap).map(([key, value]) => ({
    key,
    text: value.text,
  }));

  const CheckboxStack = () => (
    <Stack tokens={{ childrenGap: 16 }}>
      <div>{t('How should Axiom handle this sensitive data?')}</div>
      {restrictionOptions.map((option) => (
        <Checkbox
          key={option.key}
          label={t(option.text)}
          checked={selectedSensitivityOptions.includes(option.key)}
          onChange={(_, checked) =>
            setSelectedSensitivityOptions((prev) =>
              checked ? [...prev, option.key] : prev.filter((k) => k !== option.key),
            )
          }
        />
      ))}
    </Stack>
  );

  const onView = (item) => {
    showModal({
      content: (
        <ViewClassificationModal
          item={item}
          sensitivityClassificationOptions={sensitivityClassificationOptions}
          restrictionOptions={restrictionOptions}
        />
      ),
      size: MODAL_SIZE.SMALL,
    });
  };

  const onEdit = (item) => {
    setClassificationName(item.classificationName);
    setDescription(item.description);
    setSelectedSensitivityOptions(item.selectedSensitivityOptions || []);
    toast.info(
      `${t('Editing classification')}: ${
        classificationNameMap[item.classificationName]?.text || item.classificationName
      }`,
    );
  };

  const onDelete = (item) => {
    apiDelete.request({
      url: API_SPECIFIC_SECURITY.DELETE_SENSITIVITY_CLASSIFICATION(item.id), // Make sure this endpoint exists
      onSuccess: () => {
        toast.success(t('Classification deleted successfully!'));
        apiInfo.refetch();
      },
      onError: (err) => {
        toast.error(t('Failed to delete classification.'));
      },
    });
  };

  const getMenu = (item) => ({
    items: [
      {
        key: 'view',
        text: t('View'),
        iconProps: { iconName: 'View' },
        onClick: onView,
      },
      {
        key: 'edit',
        text: t('Edit'),
        iconProps: { iconName: 'Edit' },
        onClick: onEdit,
      },
      {
        key: 'delete',
        text: t('Delete'),
        iconProps: { iconName: 'Delete' },
        onClick: onDelete,
      },
    ],
    shouldFocusOnMount: false,
  });

  const _columns = [
    {
      key: 'classificationName',
      name: t('Classification Type'),
      fieldName: 'classificationName',
      minWidth: 120,
      renderItem: (item) =>
        classificationNameMap[item.classificationName]?.text || item.classificationName,
    },
    {
      key: 'description',
      name: t('Classification Description'),
      fieldName: 'description',
      minWidth: 180,
    },
    {
      key: 'security',
      name: t('Can be used in AI features?'),
      fieldName: 'security',
      minWidth: 160,
      renderItem: (item) => (item.selectedSensitivityOptions?.includes('ai') ? t('No') : t('Yes')),
    },
    {
      key: 'reporting',
      name: t('Displays In Reporting?'),
      fieldName: 'reporting',
      minWidth: 160,
      renderItem: (item) =>
        item.selectedSensitivityOptions?.includes('reporting') ? t('No') : t('Yes'),
    },
    {
      key: 'hie',
      name: t('Can Be Exported To HIE?'),
      fieldName: 'hie',
      minWidth: 160,
      renderItem: (item) => (item.selectedSensitivityOptions?.includes('hie') ? t('No') : t('Yes')),
    },
    {
      key: 'dwh',
      name: t('Can Be Exported To Data Warehouse?'),
      fieldName: 'dwh',
      minWidth: 160,
      renderItem: (item) => (item.selectedSensitivityOptions?.includes('dwh') ? t('No') : t('Yes')),
    },
    { key: 'Action', name: t('Action'), fieldName: 'Action', minWidth: 100 },
  ];

  return (
    <CollapseVertical open title={t('Sensitivity Classification')} className="mb-24">
      <Stack tokens={{ childrenGap: 16 }}>
        <div>
          {t(
            'A sensitivity classification allows specific client data to have limited view and edit access based on your specifications. We recommend using this option for data associated with SUD, Pregnancy, Abortion, and other sensitive data types that need elevated security.',
          )}
        </div>
        <Stack horizontal tokens={{ childrenGap: 16 }} className="mt-16">
          <Toggle
            label={t('Enable Sensitivity Classification')}
            inlineLabel={true}
            checked={isSensitive}
            onChange={(_, checked) => setIsSensitive(checked)}
          />
        </Stack>
      </Stack>
      {isSensitive && (
        <Stack horizontalAlign="start" tokens={{ childrenGap: 16 }}>
          <div>{t('What type of classification model do you need?')}</div>
          <Stack horizontal tokens={{ childrenGap: 16 }}>
            {radioOptions.map((option) => (
              <RadioButton
                key={option.value}
                name="classificationType"
                value={option.value}
                label={option.label}
                helperText={option.helperText}
                checked={classificationType === option.value}
                onChange={() => setClassificationType(option.value)}
              />
            ))}
          </Stack>
          {classificationType === 'multiple' && (
            <Stack tokens={{ childrenGap: 16 }} styles={{ root: { width: '100%' } }}>
              <Stack
                tokens={{ childrenGap: 16 }}
                className={cn(
                  css.cmClass.backgroundGrey100,
                  css.cmClass.border,
                  css.cmClass.borderRadius4,
                  'p-16',
                  css.cmClass.maxWidth900,
                )}
              >
                <div className="weight-600">{t('Sensitivity Classification')}</div>
                <Stack horizontal tokens={{ childrenGap: 16 }}>
                  <Dropdown
                    label={t('Classification Name')}
                    options={sensitivityClassificationOptions}
                    placeholder={t('Select')}
                    selectedKey={classificationName}
                    onChange={handleClassificationNameChange}
                    required
                    errorMessage={dropdownError}
                    styles={{ root: { width: '100%' } }}
                  />
                  <TextField
                    label={t('Description')}
                    value={description}
                    onChange={(_, value) => setDescription(value || '')}
                    errorMessage={descriptionError}
                    styles={{ root: { width: '100%' } }}
                    required
                  />
                </Stack>
                <CheckboxStack />
              </Stack>
              <Table
                columns={_columns}
                items={apiInfo.data?.classifications || []}
                loading={apiInfo.loading}
                totalItems={apiInfo.data?.classifications?.length}
                refetch={apiInfo.refetch}
                pagination={false}
                getMenuProps={getMenu}
              />
            </Stack>
          )}
          {classificationType === 'single' && <CheckboxStack />}
        </Stack>
      )}
      <Stack horizontalAlign="start" className="mt-16" tokens={{ childrenGap: 16 }}>
        <PrimaryButton onClick={handleSave}>{t('Save Changes')}</PrimaryButton>
      </Stack>
    </CollapseVertical>
  );
};

export default SensitivityClassification;
