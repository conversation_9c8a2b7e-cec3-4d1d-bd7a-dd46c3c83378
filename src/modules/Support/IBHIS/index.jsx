import React from 'react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { t } from 'utils/string';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import useHookForm from 'components/HookForm/useHookForm';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { API_IBHIS, API_METHOD, API_PATIENT_ASSIGNMENT } from 'constants/urlRequest';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import { formatToFluentOptions } from 'utils';
import { useModal, useRequest } from 'hooks';
import ResponsiveItem from 'components/Layout/Responsive/Item';
import ResponsiveGrid from 'components/Layout/Responsive/Container';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import { TEXT_TEMPLATE } from 'constants/texts';
import { toast } from 'react-toastify';
import { handleValidationBE } from 'utils/form';
import QUERY_KEY from 'constants/queryKey';
import Table from 'components/Table';

import { fetchByQueryKey } from 'apis/queryClient';
import WatchValues from 'components/HookForm/WatchValues';
import { MODAL_SIZE } from 'constants/modal';
import ModalConfirm from 'components/Modal/ModalConfirm';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import HookFormProvider from 'components/HookForm/HookFormProvider';
import { FORM_ID } from 'constants/formId';

const defaultInitialValues = {
  programId: null,
  programName: null,
  providerId: null,
  programSiteId: null,
  assignmentSiteType: null,
};
function SupportIbhis() {
  const methods = useHookForm({
    defaultValues: defaultInitialValues,
  });
  const { showModal } = useModal();
  const { data, loading, updateParams, refetch, params } = useRequest({
    key: QUERY_KEY.IBHIS_SITE_TO_PROGRAM_MAPPING,
    url: API_IBHIS.SITE_TO_PROGRAM_MAPPING,
    params: DEFAULT_PAGINATION_PARAMS,
    autoRefetch: true,
  });

  const apiSave = useRequest({
    key: [API_IBHIS.SITE_TO_PROGRAM_MAPPING, 'save-mapping-site-program'],
    url: API_IBHIS.SITE_TO_PROGRAM_MAPPING,
    method: API_METHOD.POST,
  });

  const apiUpdate = useRequest({
    key: [API_IBHIS.SITE_TO_PROGRAM_MAPPING, 'update-mapping-site-program'],
    url: API_IBHIS.SITE_TO_PROGRAM_MAPPING,
    method: API_METHOD.PUT,
  });

  const apiDelete = useRequest({
    key: [API_IBHIS.SITE_TO_PROGRAM_MAPPING, 'delete-mapping-site-program'],
    url: API_IBHIS.SITE_TO_PROGRAM_MAPPING,
    method: API_METHOD.DELETE,
  });

  const onSubmit = (values) => {
    (values?.ibhisProgramProviderId ? apiUpdate : apiSave).request({
      url: values?.ibhisProgramProviderId
        ? `${API_IBHIS.SITE_TO_PROGRAM_MAPPING}/${values?.ibhisProgramProviderId}`
        : API_IBHIS.SITE_TO_PROGRAM_MAPPING,
      payload: values,
      options: {
        onSuccess: () => {
          if (values?.ibhisProgramProviderId) {
            toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY('Site-to-Program Mapping'));
          } else {
            toast.success(TEXT_TEMPLATE.UPDATE_SUCCESSFULLY('Site-to-Program Mapping'));
          }
          fetchByQueryKey(QUERY_KEY.IBHIS_SITE_TO_PROGRAM_MAPPING);
          onReset();
        },
        onError: (err) => {
          handleValidationBE({ setError: methods.setError }, err);
        },
      },
    });
  };

  const onReset = () => {
    methods.reset(defaultInitialValues);
  };

  const onEdit = (item) => {
    methods.reset(item);
  };

  const onDelete = (item) => {
    showModal({
      content: (
        <ModalConfirm
          message={TEXT_TEMPLATE.DELETE_CONFIRMATION(t('this Mapping'))}
          onOk={() => {
            apiDelete.request({
              url: `${API_IBHIS.SITE_TO_PROGRAM_MAPPING}/${item?.ibhisProgramProviderId}`,
              options: {
                onSuccess: () => {
                  toast.success(TEXT_TEMPLATE.DELETE_SUCCESSFULLY('Site-to-Program Mapping'));
                  fetchByQueryKey(QUERY_KEY.IBHIS_SITE_TO_PROGRAM_MAPPING);
                },
              },
            });
          }}
        />
      ),
      size: MODAL_SIZE.X_SMALL,
    });
  };
  const apiLookup = useRequest({
    key: [API_IBHIS.GET_SERVICE_REQUEST_LOOKUPS, 'lookup-mapping-site-program'],
    url: API_IBHIS.GET_SERVICE_REQUEST_LOOKUPS,
    autoRefetch: true,
  });

  const apiSite = useRequest({
    url: API_PATIENT_ASSIGNMENT.SITE_LOOKUP,
    autoRefetch: true,
  });

  const apiSiteType = useRequest({
    url: API_PATIENT_ASSIGNMENT.SITE_TYPE_LOOKUP,
    shouldCache: true,
  });

  return (
    <Stack tokens={{ childrenGap: 16 }}>
      <CollapseVertical className="mb-24" open title={t('Site-to-Program Mapping History')}>
        <Table
          columns={columns}
          items={data?.items || []}
          loading={loading || apiDelete?.loading}
          totalItems={data?.totalItems}
          metadata={params}
          onMetadataChange={updateParams}
          refetch={refetch}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      </CollapseVertical>
      <CollapseVertical className="mb-24" open title={t('Site-to-Program Mapping Input')}>
        <HookFormProvider {...methods} formId={FORM_ID.SP_IBHIS_SITE_TO_PROGRAM_MAPPING}>
          <LoadingWrapper loading={apiLookup?.loading || apiSave?.loading || apiUpdate?.loading}>
            <Stack tokens={{ padding: 16 }}>
              <ResponsiveGrid>
                <WatchValues name="ibhisProgramProviderId">
                  {(ibhisProgramProviderId) =>
                    !ibhisProgramProviderId && (
                      <ResponsiveItem md={3}>
                        <FieldSelectorPrimary
                          name="assignmentSiteType"
                          title={t('Site Type')}
                          isFast={false}
                          options={formatToFluentOptions(apiSiteType?.data)}
                          required
                          onChange={({ data }) => {
                            methods.setValue('assignmentSiteType', data?.key);
                            apiSite.updateParams({ typeId: data?.key });
                          }}
                        />
                      </ResponsiveItem>
                    )
                  }
                </WatchValues>
                <WatchValues name={['ibhisProgramProviderId', 'providerId', 'providerName']}>
                  {([ibhisProgramProviderId, providerId, providerName]) => (
                    <ResponsiveItem md={3}>
                      <LoadingWrapper loading={apiSite.loading}>
                        <FieldSelectorPrimary
                          disabled={!!ibhisProgramProviderId}
                          name="providerId"
                          title={t('Site')}
                          isFast={false}
                          options={
                            !!ibhisProgramProviderId
                              ? [{ key: providerId, text: providerName }]
                              : formatToFluentOptions(apiSite?.data)
                          }
                          required
                        />
                      </LoadingWrapper>
                    </ResponsiveItem>
                  )}
                </WatchValues>

                <ResponsiveItem md={3}>
                  <FieldSelectorPrimary
                    name="programId"
                    required
                    title={t('Program')}
                    options={formatToFluentOptions(apiLookup?.data?.programOfServices)}
                    onChange={({ data }) => {
                      methods.setValue('programName', data?.description);
                      methods.setValue('programId', data?.key);
                    }}
                  />
                </ResponsiveItem>
              </ResponsiveGrid>

              <Stack className="mt-16" horizontal tokens={{ childrenGap: 16 }}>
                <PrimaryButton
                  text={t('Save ')}
                  onClick={() => {
                    methods.clearErrors();
                    methods.handleSubmit(onSubmit)();
                  }}
                />
                <CalloutConfirmation onOk={onReset}>
                  <DefaultButton text={t('Reset')} />
                </CalloutConfirmation>
              </Stack>
            </Stack>
          </LoadingWrapper>
        </HookFormProvider>
      </CollapseVertical>
    </Stack>
  );
}

export default SupportIbhis;

const columns = [
  { name: 'Site', fieldName: 'providerName' },
  { name: 'Program', fieldName: 'programId' },
  {
    name: 'Create Date',
    fieldName: 'createDateTime',
    renderItem: ({ createDateTime, createUser }, _, c, render) =>
      render({ date: createDateTime, user: createUser }),
  },
  {
    name: 'Update Date',
    fieldName: 'updateDateTime',
    renderItem: ({ updateDateTime, updateUser }, _, c, render) =>
      render({ date: updateDateTime, user: updateUser }),
  },
  { key: 'Action', name: 'Action', fieldName: 'action' },
];
