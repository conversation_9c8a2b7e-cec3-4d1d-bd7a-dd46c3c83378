import React, { useEffect, useState } from 'react';
import { useClass, useNavigator, useRequest, useUser } from 'hooks';
import DynamicFormViewerHistory from 'modules/DynamicForm/Viewer/History';
import { API_DYNAMIC_FORM, API_SECURITY_FORMS } from 'constants/urlRequest';
import DynamicFormViewerInput from 'modules/DynamicForm/Viewer';
import cn from 'classnames';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import StickyToolbar from 'components/Toolbar/StickyToolbar';
import { t } from 'utils/string';
import useDynamicFormAnswer from 'modules/DynamicForm/useDynamicFormAnswer';
import { toast } from 'react-toastify';
import useGetDynamicFormWithQuestion from '../useGetDynamicFormWithQuestion';
import DynamicFormViewerAutoSave from 'modules/DynamicForm/Viewer/AutoSave';
import { dynamicFormIsV1 } from 'utils/dynamicForm';
import { isNil } from 'utils';

const DynamicFormViewer = ({ formId }) => {
  const css = useClass();
  const { navigateBack } = useNavigator();
  const { params, searchParams } = useNavigator();
  const id = formId || params?.id;
  const saveId = searchParams?.saveId;
  const { info } = useUser();
  const [formPermission, setFormPermission] = useState();

  const { data, loading } = useRequest({
    key: `${API_SECURITY_FORMS.BY_USER}-${info?.username}`,
    url: API_SECURITY_FORMS.BY_USER,
    requiredParams: { userName: info?.username },
  });

  const { dynamicFormAnswer, apiFormDetail } = useDynamicFormAnswer({ saveId });

  const { data: dynamicForm, loading: loadingForm } = useGetDynamicFormWithQuestion({ id });

  const apiAnswerStatus = useRequest({
    url: API_DYNAMIC_FORM.ANSWER_STATUSES(id),
    enabled: false,
    autoRefetch: true,
  });

  useEffect(() => {
    apiAnswerStatus.updateParams({ currentStatus: apiFormDetail?.data?.statusCode });
  }, [apiFormDetail?.data?.statusCode]);

  useEffect(() => {
    const permission = data?.find(
      (d) =>
        (d?.formTypeId === id || d?.formTypeId === dynamicForm?.linkedFormId) &&
        d?.secTypeDesc === 'Dynamic Form',
    );

    setFormPermission(permission);
    if (permission?.userView === false) {
      toast.error(t('You have no permission to view this form'), { toastId: id });
    }
  }, [data, id, dynamicForm?.linkedFormId]);

  const tools = [
    {
      key: 'back',
      name: t('Back'),
      icon: 'Back',
      active: true,
      onClick: navigateBack,
    },
  ];

  const dfContent = dynamicFormAnswer || dynamicForm;

  const CONTENT_MAPPING = [
    {
      key: 'dynamicFormViewerHistory',
      Component: DynamicFormViewerHistory,
    },
    ...(!dfContent?.formInformation?.enabledAutoSave || dynamicFormIsV1(dfContent)
      ? []
      : [
          {
            key: 'dynamicFormViewerAutoSave',
            Component: DynamicFormViewerAutoSave,
          },
        ]),
    ...(dynamicFormIsV1(dfContent)
      ? []
      : [
          {
            key: 'dynamicFormViewerInput',
            Component: DynamicFormViewerInput,
          },
        ]),
  ];

  return (
    <div className="flex-1">
      <StickyToolbar tools={tools} />
      <LoadingWrapper loading={loading || apiFormDetail.loading || loadingForm}>
        {formPermission?.userView !== false && (
          <div className={cn('p-16', css.cmClass.col, css.cmClass.gap32)}>
            {CONTENT_MAPPING.map(({ key, Component }) => (
              <div key={key} id={key}>
                <Component
                  dynamicForm={dfContent}
                  apiAnswerStatus={apiAnswerStatus}
                  formPermission={formPermission}
                />
              </div>
            ))}
          </div>
        )}
      </LoadingWrapper>
    </div>
  );
};

export default DynamicFormViewer;
