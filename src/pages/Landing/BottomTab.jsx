import { useDispatch, useSelector } from 'react-redux';
import { BackgroundContext } from 'contexts/BackgroundContext';
import { ROUTE_NAME } from 'constants/routes';
import { useBlocker, useModal, useNavigator } from 'hooks';
import React, { useMemo } from 'react';
import TabsBottom from 'components/TabsBottom';
import EmployeeInfo from 'modules/HREnrollment/components/EmployeeInfo';
import PatientInfo from 'modules/Inpatient-Residential/components/PatientInfo';
import {
  ACTION_NAME,
  ENROLLMENT_ACTION_KEYS,
  INPATIENT_ACTION_KEYS,
  RESIDENTIAL_ACTION_KEYS,
  TRIAGE_ACTION_KEYS,
} from 'components/TabsBottom/constants';
import { resetState } from 'store/actions/tabsBottom';
import { MODAL_SIZE } from 'constants/modal';
import { t } from 'utils/string';
import ModalConfirm from 'components/Modal/ModalConfirm';
import { SECURITY_FUNCTIONS } from 'constants/modules';
import PatientInfoTriage from 'modules/Inpatient-Residential/components/PatientInfoTriage';

const BottomTab = () => {
  const { showModal } = useModal();
  const { pathname } = useNavigator();
  const { openBottomTabs, tabs } = useSelector((state) => state.tabsBottom);
  const dispatch = useDispatch();
  const security = useSelector((state) => state.settings.pageSecurity);
  const items = useSelector((state) => state.settings.navigationItems);

  const ACTION_KEYS = useMemo(
    () => ({
      [ROUTE_NAME.HR]: ENROLLMENT_ACTION_KEYS.filter(
        (key) => security?.find((i) => i?.pageKey === key)?.view,
      ),
      [ROUTE_NAME.TRIAGE_OBSERVATION]: TRIAGE_ACTION_KEYS.filter(
        (i) =>
          typeof i !== 'string' ||
          (!Object.values(SECURITY_FUNCTIONS).includes(i) && !ACTION_NAME[i]?.route) ||
          security?.find((item) => item?.pageKey === i)?.view ||
          items?.find((item) => item?.key === ACTION_NAME[i]?.route),
      ),
      [ROUTE_NAME.INPATIENT]: INPATIENT_ACTION_KEYS.filter(
        (i) =>
          typeof i !== 'string' ||
          (!Object.values(SECURITY_FUNCTIONS).includes(i) && !ACTION_NAME[i]?.route) ||
          security?.find((item) => item?.pageKey === i)?.view ||
          items?.find((item) => item?.key === ACTION_NAME[i]?.route),
      ),
      [ROUTE_NAME.RESIDENTIAL]: RESIDENTIAL_ACTION_KEYS.filter(
        (i) =>
          typeof i !== 'string' || // Divider/Header items
          (!Object.values(SECURITY_FUNCTIONS).includes(i) && !ACTION_NAME[i]?.route) || // inpatient items
          security?.find((item) => item?.pageKey === i)?.view || // SECURITY_FUNCTIONS items
          items?.find((item) => item?.key === ACTION_NAME[i]?.route), // standalone-page items
      ),
      [ROUTE_NAME.E_MAR]: [],
    }),
    [security, items],
  );

  const onBack = (autoUnblockingTx) =>
    showModal({
      content: (
        <ModalConfirm
          message={t('There are unfinished tabs. Are you sure you want to close all tabs?')}
          onOk={() => {
            dispatch(resetState());
            autoUnblockingTx?.retry();
          }}
        />
      ),
      size: MODAL_SIZE.X_SMALL,
    });

  useBlocker(onBack, tabs?.length > 0);

  return (
    openBottomTabs &&
    !!ACTION_KEYS[pathname] &&
    !!tabs.length && (
      <BackgroundContext.Provider value={{ canHaveBG: false }}>
        <TabsBottom infoComponent={INFO_COMPONENT[pathname]} actionKeys={ACTION_KEYS[pathname]} />
      </BackgroundContext.Provider>
    )
  );
};

export default BottomTab;

const INFO_COMPONENT = {
  [ROUTE_NAME.HR]: <EmployeeInfo />,
  [ROUTE_NAME.INPATIENT]: <PatientInfo />,
  [ROUTE_NAME.RESIDENTIAL]: <PatientInfo />,
  [ROUTE_NAME.E_MAR]: <PatientInfo />,
  [ROUTE_NAME.TRIAGE_OBSERVATION]: <PatientInfoTriage />,
};
