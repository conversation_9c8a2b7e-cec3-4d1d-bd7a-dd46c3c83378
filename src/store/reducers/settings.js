import { SETTING } from 'store/types/settings';

const initState = {
  loading: false,
  isSetLoading: false,
  AIPermission: {},
  aiSettings: {},
  settings: {},
  navigationItems: [],
  cardview: [],
  navigationHistories: [],
  pageSecurity: [],
  toolBarPages: {},
  shortcuts: [],
  patientMiniBar: '',
  isCardViewPanelCollased: true,
};

const reducer = (state = initState, action) => {
  const { type, payload } = action;
  switch (type) {
    case SETTING.GET_SETTING:
      return { ...state, loading: true };
    case SETTING.SET_SETTING_START:
      return { ...state, isSetLoading: true };
    case SETTING.SET_SETTING_SUCCESSFULLY:
      return { ...state, settings: payload, loading: false, isSetLoading: false };
    case SETTING.SET_ITEMS_SUCCESSFUL:
      return { ...state, navigationItems: payload, loading: false, isSetLoading: false };
    case SETTING.TOGGLE_CARD_VIEW:
      return { ...state, isCardViewPanelCollased: !state.isCardViewPanelCollased };
    case SETTING.SET_CARD_VIEW_SUCCESSFULLY:
      return { ...state, cardview: payload };
    case SETTING.SET_AI_SIDEBAR:
      return { ...state, AIPermission: payload };
    case SETTING.SET_AI_SETTINGS:
      return { ...state, aiSettings: payload || state.aiSettings };
    case SETTING.SET_SETTING_FAILED:
      return { ...state, loading: false, isSetLoading: false };
    case SETTING.SET_NAVIGATION_HISTORIES_SUCCESSFULLY:
      return { ...state, navigationHistories: payload };
    case SETTING.SET_TOOL_BAR_PAGES_SUCCESSFUL:
      return { ...state, toolBarPages: payload, isSetLoading: false };
    case SETTING.SET_PAGE_SECURITY:
      return { ...state, pageSecurity: payload };
    case SETTING.SET_SHORTCUTS:
      return { ...state, shortcuts: payload };
    case SETTING.SET_PATIENT_MINI_BAR:
      return { ...state, patientMiniBar: payload };
    case SETTING.RESET:
      return initState;
    default:
      return state;
  }
};

export default reducer;
