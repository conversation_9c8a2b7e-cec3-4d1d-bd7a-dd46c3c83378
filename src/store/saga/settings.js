import { all, call, put, takeLatest } from 'redux-saga/effects';
import { SETTING } from 'store/types/settings';
import { DEFAULT_AI_SETTINGS, DEFAULT_SETTINGS } from 'constants/settings';
import { SETTING_KEYS } from 'constants/settingKeys';
import {
  getAllPageList,
  getPatientPermission,
  getShortcuts,
  getUserPageList,
  getV2Settings,
  requestInfo,
  saveV2Settings,
} from 'apis/hims/settings';
import {
  SECURITY_FUNCTIONS,
  mockNavigatorItems,
  MODULE_DATA,
  moduleMapping,
  PAGE,
} from 'constants/modules';
import { WELCOME_WAYS_KEY } from 'constants/welcome';
import { WELCOME } from 'store/types/welcome';
import { toast } from 'react-toastify';
import { ERROR_MESSAGE, TEXT_TEMPLATE } from 'constants/texts';
import { DEFAULT_CARD_VIEW } from 'constants/cardview';
import { AUTH_CONS, NAVIGATION_HISTORIES } from 'constants/index';
import { MODULE_NAME, ROUTE_NAME } from 'constants/routes';
import { removeUndefinedFromObject } from 'utils';
import { AUTHENTICATION } from 'store/types/authentication';
import { APPLICATION } from 'store/types/application';
import { t } from 'utils/string';

const formatCardViewSetting = (settings = [], security = {}) => {
  const settingsObject = settings.reduce((acc, cur) => ({ ...acc, [cur?.key]: cur }), {});
  const defaultCardView = DEFAULT_CARD_VIEW.reduce((acc, cur) => ({ ...acc, [cur?.key]: cur }), {});
  const key1 = settings.map((item) => item?.key);
  const key2 = Object.keys(defaultCardView);
  const isEqual = key1.length === key2.length && key1.every((key) => key2.includes(key));
  const cardViewSetting = isEqual ? settings : DEFAULT_CARD_VIEW;

  return {
    isCardViewIdentical: isEqual,
    cardViewSetting: cardViewSetting
      ?.map((i) => ({
        ...i,
        route: defaultCardView[i?.key]?.route,
        active: settingsObject[i?.key]?.active,
      }))
      ?.filter((i) => security?.find((s) => s?.key === i?.route)),
  };
};

const onFailed = (e) => {
  const message = e.response?.data?.message;
  toast.error(message || ERROR_MESSAGE, { toastId: message });
};

// those modules always enabled
const ENABLED_MODULES = [
  MODULE_NAME.PROGRESS_NOTE_PCP,
  MODULE_NAME.PROGRESS_NOTE_PSYCH,
  MODULE_NAME.PROGRESS_NOTE_GROUP,
  MODULE_NAME.PROGRESS_NOTE_CM,
  MODULE_NAME.PSYCH_EVALUATION_NOTE_INPUT,
  MODULE_NAME.PSYCHOTHERAPY_NOTE_INPUT,
  MODULE_NAME.NURSE_PROGRESS_NOTE,
  MODULE_NAME.ENGAGEMENT_SESSION_NOTE_INPUT,
  MODULE_NAME.PSYCHOSOCIAL_ASSESSMENT_NOTE_INPUT,
  MODULE_NAME.PROGRESS_NOTE_AUTO_SAVE,
  MODULE_NAME.PROGRESS_NOTE_HISTORY,
  MODULE_NAME.CM3_CONTACT,
  MODULE_NAME.CM3_APPOINTMENT,
  MODULE_NAME.CM3_PRESCIPTION,
  MODULE_NAME.CM3_GROUP,
  MODULE_NAME.CM3_DEMOGRAPHIC,
  MODULE_NAME.DYNAMIC_SERVICE_PLAN_VIEWER,
  MODULE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER,
  MODULE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER_HISTORY,
  MODULE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER_AUTO_SAVE,
];
const transformNavigationItemGetData = (navigationSettings, pagelist = [], isDeveloper) => {
  const [pages, modules] = isDeveloper
    ? [pagelist, []]
    : pagelist.reduce(
        (acc, cur) => {
          if (cur?.type === 'SubPage' && cur?.view) {
            const pageKey = cur?.pageKey?.replace(
              new RegExp(`${cur?.category}/|-component`, 'g'),
              '',
            );
            acc[1].push({ ...cur, pageKey });
          } else if (cur?.type === 'Page' && cur?.view) acc[0].push(cur);
          return acc;
        },
        [[], ENABLED_MODULES.map((pageKey) => ({ pageKey }))],
      );

  let clone = structuredClone(navigationSettings);
  if (!isDeveloper) {
    clone = clone.map((i) => {
      return {
        ...i,
        subMenu: (i?.subMenu || []).map((s) => {
          const subModule = modules.find((m) => m?.pageKey === s?.key);
          return { ...s, moduleName: subModule?.pageName, disabled: !subModule };
        }),
      };
    });
  }

  const newModule = mockNavigatorItems.filter(({ key }) => !clone.find((i) => i?.key === key));
  const payload = newModule.concat(clone).map((item, i) => {
    let pageData = pages.find(({ pagePath }) => item.key === pagePath);

    if (
      ((!pageData || pageData?.disabled) && isDeveloper) ||
      (!!pageData && pageData?.pagePath !== item?.key)
    ) {
      pageData = mockNavigatorItems.find(({ key }) => key === item.key);
    }
    if (!pageData || (!pageData?.view && !isDeveloper)) return null;

    const { subMenu, showCardView } = item;
    const { pageId, pageName, pagePath, icon, isShowPatientInfo, isCardViewEnable, key } = pageData;
    const page = PAGE?.[item.key] || {};

    return {
      pageId,
      key: pagePath || key,
      text: t(pageName) || page?.text,
      path: pagePath || key,
      iconProps: { iconName: icon },
      isCardViewEnable,
      showCardView: isCardViewEnable && (showCardView ?? true),
      showPatientInfo: isShowPatientInfo,
      subMenu: subMenu?.reduce((acc, cur) => {
        if (!MODULE_DATA[cur.key]) return acc;

        const { icon, rootPage, defaultShow } = MODULE_DATA[cur.key];
        const text = t(cur?.props?.name) || cur?.moduleName || MODULE_DATA[cur.key]?.text;
        acc.push({ ...cur, text, icon, rootPage, defaultShow });
        return acc;
      }, []),
    };
  });

  return payload.filter(Boolean);
};

// those modules are redundant, should not show on navigation bar
const REDUNDANT_ROUTES = [
  ROUTE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER,
  ROUTE_NAME.DYNAMIC_FORM_VIEWER,
  ROUTE_NAME.DYNAMIC_FORM_BUILDER,
];
const synchronizeNavigationItems = (navigationSettings = []) => {
  let isChanged = false;
  const modulesRoot = mockNavigatorItems.reduce((acc, item) => ({ ...acc, [item.key]: item }), {});

  let clone = structuredClone(navigationSettings)?.filter(
    (i) => !REDUNDANT_ROUTES.includes(i?.key),
  );
  clone.forEach((item) => {
    const defaultSubMenu = modulesRoot[item?.key]?.subMenu || [];

    const newModules = defaultSubMenu
      .filter((m) => !item?.subMenu?.find((s) => s?.key === m?.key))
      .map((m) => ({ key: m?.key, rootPage: m?.rootPage, isSubPath: m?.isSubPath }));

    const removedModules = item?.subMenu?.filter(
      (s) => s?.rootPage !== item?.key || defaultSubMenu.find((m) => m?.key === s?.key),
    );

    if (newModules?.length || removedModules?.length !== item?.subMenu?.length) {
      isChanged = true;
      const subMenu = (newModules || []).concat(removedModules || []);
      item.subMenu = subMenu?.filter(Boolean) || [];
    }
  });

  return [isChanged, clone];
};

function* getAllSettings() {
  try {
    yield put({ type: AUTHENTICATION.INFO_START_CHECKLOGIN });

    const [settings, pagelist, allPageList, info, patientPermission, shortcuts] = yield all([
      call(getV2Settings),
      call(getUserPageList),
      call(getAllPageList),
      call(requestInfo),
      call(getPatientPermission),
      call(getShortcuts),
    ]);
    const isDeveloper = info?.isDeveloper;
    const pageSecurity = isDeveloper
      ? moduleMapping.concat(Object.values(SECURITY_FUNCTIONS)).map((m) => ({
          pageKey: m.key || m,
          delete: true,
          print: true,
          save: true,
          view: true,
        }))
      : pagelist.map((p) => ({
          ...p,
          pageKey: p?.pageKey?.replace(new RegExp(`${p?.category}/|-component`, 'g'), ''),
        }));

    const globalSettings = settings.v2GlobalSetting || {};

    const globalSettingsPayload = { ...DEFAULT_SETTINGS, ...globalSettings };
    const aiSettings = settings.aiSetting || {};
    const aiSettingPayload = { ...DEFAULT_AI_SETTINGS, ...aiSettings };

    const [isChange, navigationSettings] = synchronizeNavigationItems(settings.v2NavigationSetting);

    if (isChange) {
      yield call(saveV2Settings, { v2NavigationSetting: navigationSettings });
    }

    const navigationSettingsPayload = transformNavigationItemGetData(
      navigationSettings,
      isDeveloper ? allPageList : pagelist,
      isDeveloper,
    );
    const navigationHistories = JSON.parse(localStorage.getItem(NAVIGATION_HISTORIES)) || [];

    //use default card view when BE and local not identical
    const { isCardViewIdentical, cardViewSetting } = formatCardViewSetting(
      settings.v2CardViewSetting || [],
      navigationSettingsPayload,
    );

    if (localStorage.getItem(AUTH_CONS.JUST_LOGIN)) {
      const { [SETTING_KEYS.WELCOME_WAY_SHOW]: wayShowWelcome } = globalSettingsPayload;
      yield all([
        put({
          type: WELCOME.SET_IS_SHOW,
          payload: { isShowModal: wayShowWelcome !== WELCOME_WAYS_KEY.NOT_SHOW },
        }),
      ]);
    }
    localStorage.setItem(AUTH_CONS.JUST_LOGIN, '');

    const v2ToolbarSetting = settings.v2ToolbarSetting || {};
    const { [SETTING_KEYS.LAYOUT_VIEW]: layoutView } = globalSettingsPayload;

    const AIPermission =
      settings.isEnabledAIModule || isDeveloper
        ? {
            aiModuleDynamicFormEnabled: settings.aiModuleDynamicFormEnabled,
            aiModuleProgressNoteEnabled: settings.aiModuleProgressNoteEnabled,
            aiModuleRCMEnabled: settings.aiModuleRCMEnabled,
            aiModuleVirtualAssistantEnabled: settings.aiModuleVirtualAssistantEnabled,
            aiModuleVirtualPatientEnabled: settings.aiModuleVirtualPatientEnabled,
            patientThemeModuleEnabled: settings.patientThemeModuleEnabled,
          }
        : {};

    yield all([
      put({ type: SETTING.SET_SETTING_SUCCESSFULLY, payload: globalSettingsPayload }),
      put({ type: SETTING.SET_AI_SIDEBAR, payload: AIPermission }),
      put({ type: SETTING.SET_AI_SETTINGS, payload: aiSettingPayload }),
      put({ type: SETTING.SET_ITEMS_SUCCESSFUL, payload: navigationSettingsPayload }),
      //request save on BE to default card view when local and BE not identical
      put({
        type: isCardViewIdentical ? SETTING.SET_CARD_VIEW_SUCCESSFULLY : SETTING.SET_CARD_VIEW,
        payload: cardViewSetting,
      }),
      put({ type: SETTING.SET_NAVIGATION_HISTORIES_SUCCESSFULLY, payload: navigationHistories }),
      put({ type: SETTING.SET_TOOL_BAR_PAGES_SUCCESSFUL, payload: v2ToolbarSetting }),
      put({ type: SETTING.SET_PAGE_SECURITY, payload: pageSecurity }),
      put({ type: SETTING.SET_SHORTCUTS, payload: shortcuts }),
      put({ type: SETTING.SET_PATIENT_MINI_BAR, payload: settings.patientInformationBarItems }),
      put({
        type: AUTHENTICATION.INFO_SUCCESS,
        payload: { data: { ...info, patientPermission } },
      }),
      put({ type: APPLICATION.SET_PAGE_SECTIONS_DISPLAY, payload: layoutView }),
    ]);

    yield put({ type: AUTHENTICATION.ASK_EXTEND_SESSION });
  } catch (e) {
    console.log(e);
    onFailed(e);

    yield put({ type: AUTHENTICATION.INFO_FAIL });
    yield put({ type: SETTING.SET_SETTING_FAILED });
    yield put({ type: AUTHENTICATION.LOG_OUT });
  }
}

function* setGlobalSettings({ payload, onSuccess, onError, noToast = false }) {
  try {
    yield put({ type: SETTING.SET_SETTING_START });
    const aiSettings = payload?.aiSettings;
    delete payload['aiSettings'];

    //remove later
    delete payload['quickDateSelectionButtons'];

    const body = { v2GlobalSetting: payload };
    if (aiSettings) body['aiSetting'] = aiSettings;

    yield call(saveV2Settings, body);
    yield all([
      put({ type: SETTING.SET_SETTING_SUCCESSFULLY, payload }),
      put({ type: SETTING.SET_AI_SETTINGS, payload: aiSettings }),
    ]);
    if (!noToast) toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY('Setting'));
    onSuccess?.(payload);
  } catch (e) {
    onFailed(e);
    onError?.(e);
    yield put({ type: SETTING.SET_SETTING_FAILED });
  }
}

function* setNavigationSettings({ payload, onSuccess, onError }) {
  //just save what we need to customize
  const transformSavePayload = (payload || []).map(({ key, showCardView, subMenu }) => ({
    key,
    showCardView,
    subMenu: (subMenu || []).map(({ key, rootPage, hidden, isSubPath, props }) =>
      removeUndefinedFromObject({
        key,
        rootPage,
        hidden,
        isSubPath,
        props: props && !!Object.keys(props) ? props : undefined,
      }),
    ),
  }));

  try {
    yield put({ type: SETTING.SET_SETTING_START });
    yield call(saveV2Settings, { v2NavigationSetting: transformSavePayload });
    yield put({ type: SETTING.SET_ITEMS_SUCCESSFUL, payload });
    onSuccess?.(payload);
  } catch (e) {
    onFailed(e);
    onError?.(e);
    yield put({ type: SETTING.SET_SETTING_FAILED });
  }
}

function* setToolbarSettings({ payload, options }) {
  try {
    yield put({ type: SETTING.SET_SETTING_START });
    yield call(saveV2Settings, { v2ToolbarSetting: payload || {} });
    yield put({ type: SETTING.SET_TOOL_BAR_PAGES_SUCCESSFUL, payload: payload || {} });
    options?.onSuccess?.();
  } catch (e) {
    console.log(e);
    onFailed(e);
    options?.onError?.(e);
    yield put({ type: SETTING.SET_SETTING_FAILED });
  }
}

function* setCardViewSettings({ payload }) {
  try {
    yield call(saveV2Settings, { v2CardViewSetting: payload });
    yield put({ type: SETTING.SET_CARD_VIEW_SUCCESSFULLY, payload });
  } catch (e) {
    onFailed(e);
    yield put({ type: SETTING.SET_SETTING_FAILED });
  }
}

function* setNavigationHistoriesSettings({ payload }) {
  try {
    localStorage.setItem(NAVIGATION_HISTORIES, JSON.stringify(payload));
    yield put({ type: SETTING.SET_NAVIGATION_HISTORIES_SUCCESSFULLY, payload });
  } catch (e) {
    onFailed(e);
    yield put({ type: SETTING.SET_SETTING_FAILED });
  }
}

const saga = [
  takeLatest(SETTING.GET_SETTING, getAllSettings),
  takeLatest(SETTING.SET_SETTING, setGlobalSettings),
  takeLatest(SETTING.SET_ITEMS, setNavigationSettings),
  takeLatest(SETTING.SET_CARD_VIEW, setCardViewSettings),
  takeLatest(SETTING.SET_NAVIGATION_HISTORIES, setNavigationHistoriesSettings),
  takeLatest(SETTING.SET_TOOL_BAR_PAGES, setToolbarSettings),
];

export default saga;
