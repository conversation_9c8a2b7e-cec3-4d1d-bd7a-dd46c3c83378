import { withNamespace } from '../helper';

const namespace = '@SETTING';

export const SETTING = {
  GET_SETTING: withNamespace('GET_SETTING', namespace),
  SET_SETTING: withNamespace('SET_SETTING', namespace),
  SET_SETTING_START: withNamespace('SET_SETTING_START', namespace),
  SET_SETTING_SUCCESSFULLY: withNamespace('SET_SETTING_SUCCESSFULLY', namespace),
  SET_SETTING_FAILED: withNamespace('SET_SETTING_FAILED', namespace),

  SET_ITEMS: withNamespace('SET_ITEMS', namespace),
  SET_ITEMS_SUCCESSFUL: withNamespace('SET_ITEMS_SUCCESSFUL', namespace),
  SET_SUBMENU: withNamespace('SET_SUBMENU', namespace),
  SET_SUBMENU_SUCCESSFUL: withNamespace('SET_SUBMENU_SUCCESSFUL', namespace),

  SET_CARD_VIEW: withNamespace('SET_CARD_VIEW', namespace),
  SET_CARD_VIEW_SUCCESSFULLY: withNamespace('SET_CARD_VIEW_SUCCESSFULLY', namespace),
  TOGGLE_CARD_VIEW: withNamespace('TOGGLE_CARD_VIEW', namespace),

  SET_NAVIGATION_HISTORIES: withNamespace('SET_NAVIGATION_HISTORIES', namespace),
  SET_NAVIGATION_HISTORIES_SUCCESSFULLY: withNamespace(
    'SET_NAVIGATION_HISTORIES_SUCCESSFULLY',
    namespace,
  ),

  SET_AI_SIDEBAR: withNamespace('SET_AI_SIDEBAR', namespace),
  SET_AI_SETTINGS: withNamespace('SET_AI_SETTINGS', namespace),

  SET_TOOL_BAR_PAGES: withNamespace('SET_TOOL_BAR_PAGES', namespace),
  SET_TOOL_BAR_PAGES_SUCCESSFUL: withNamespace('SET_TOOL_BAR_PAGES_SUCCESSFUL', namespace),

  SET_PAGE_SECURITY: withNamespace('SET_PAGE_SECURITY', namespace),

  SET_SHORTCUTS: withNamespace('SET_SHORTCUTS', namespace),

  SET_PATIENT_MINI_BAR: withNamespace('SET_PATIENT_MINI_BAR', namespace),

  RESET: withNamespace('RESET', namespace),
};
